<?php $__env->startSection('title', 'تعديل صلاحيات المستخدم'); ?>

<?php $__env->startSection('content'); ?>
<div class="mb-6">
    <div class="flex justify-between items-center">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white">تعديل صلاحيات المستخدم</h2>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                تعديل صلاحيات: <?php echo e($user->first_name); ?> <?php echo e($user->last_name); ?>

            </p>
        </div>
        <a href="<?php echo e(route('admin.permissions.index')); ?>" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-md flex items-center transition-all">
            <i class="fas fa-arrow-right ml-2"></i>
            <span>العودة</span>
        </a>
    </div>
</div>

<!-- معلومات المستخدم -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <div class="flex items-center">
        <div class="flex-shrink-0 h-16 w-16">
            <div class="h-16 w-16 rounded-full bg-primary text-white flex items-center justify-center text-xl font-bold">
                <?php echo e(substr($user->first_name, 0, 1)); ?><?php echo e(substr($user->last_name, 0, 1)); ?>

            </div>
        </div>
        <div class="mr-4">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">
                <?php echo e($user->first_name); ?> <?php echo e($user->last_name); ?>

            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400"><?php echo e($user->email); ?></p>
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1
                <?php if($user->user_type === 'admin'): ?> bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                <?php elseif($user->user_type === 'employee'): ?> bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                <?php else: ?> bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 <?php endif; ?>">
                <?php if($user->user_type === 'admin'): ?> مدير
                <?php elseif($user->user_type === 'employee'): ?> موظف
                <?php else: ?> عميل <?php endif; ?>
            </span>
        </div>
    </div>
</div>

<form action="<?php echo e(route('admin.permissions.update-user', $user->user_id)); ?>" method="POST">
    <?php echo csrf_field(); ?>
    <?php echo method_field('PUT'); ?>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- الأدوار -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">الأدوار</h3>
            <div class="space-y-3">
                <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <label class="flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer">
                    <input type="checkbox" name="roles[]" value="<?php echo e($role->name); ?>"
                           <?php echo e($user->hasRole($role->name) ? 'checked' : ''); ?>

                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                    <div class="mr-3">
                        <div class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($role->name); ?></div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                            <?php echo e($role->permissions->count()); ?> صلاحية
                        </div>
                    </div>
                </label>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>

        <!-- الصلاحيات المباشرة -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">الصلاحيات المباشرة</h3>
            <div class="space-y-4">
                <?php $__currentLoopData = $permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group => $groupPermissions): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <h4 class="font-medium text-gray-800 dark:text-white mb-3 capitalize cursor-pointer hover:text-primary transition-colors"
                        onclick="toggleGroupPermissions('<?php echo e($group); ?>_permissions')"
                        title="اضغط لتحديد/إلغاء تحديد جميع الصلاحيات">
                        <?php switch($group):
                            case ('users'): ?> 👥 إدارة المستخدمين <?php break; ?>
                            <?php case ('menu'): ?> 🍽️ إدارة القائمة <?php break; ?>
                            <?php case ('orders'): ?> 🛒 إدارة الطلبات <?php break; ?>
                            <?php case ('reservations'): ?> 📅 إدارة الحجوزات <?php break; ?>
                            <?php case ('inventory'): ?> 📦 إدارة المخزون <?php break; ?>
                            <?php case ('ingredients'): ?> 🥘 إدارة المكونات <?php break; ?>
                            <?php case ('expenses'): ?> 💰 إدارة المصروفات <?php break; ?>
                            <?php case ('reports'): ?> 📊 التقارير <?php break; ?>
                            <?php case ('tables'): ?> 🪑 إدارة الطاولات <?php break; ?>
                            <?php case ('payments'): ?> 💳 إدارة المدفوعات <?php break; ?>
                            <?php case ('notifications'): ?> 🔔 إدارة الإشعارات <?php break; ?>
                            <?php case ('settings'): ?> ⚙️ الإعدادات <?php break; ?>
                            <?php case ('dashboard'): ?> 🏠 لوحة التحكم <?php break; ?>
                            <?php default: ?> <?php echo e($group); ?> <?php break; ?>
                        <?php endswitch; ?>
                        <i class="fas fa-chevron-down text-xs mr-2"></i>
                    </h4>
                    <div class="grid grid-cols-1 gap-2">
                        <?php $__currentLoopData = $groupPermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <label class="flex items-center">
                            <input type="checkbox" name="permissions[]" value="<?php echo e($permission->name); ?>"
                                   <?php echo e($user->hasDirectPermission($permission->name) ? 'checked' : ''); ?>

                                   class="h-3 w-3 text-primary focus:ring-primary border-gray-300 rounded">
                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">
                                <?php
                                    $permissionParts = explode('.', $permission->name);
                                    $permissionAction = count($permissionParts) > 1 ? $permissionParts[1] : '';
                                ?>

                                <?php if(str_contains($permission->name, '_')): ?>
                                    
                                    <?php echo e(str_replace('_', ' ', $permission->name)); ?>

                                <?php elseif($permissionAction): ?>
                                    
                                    <?php switch($permissionAction):
                                        case ('view'): ?> عرض <?php break; ?>
                                        <?php case ('create'): ?> إضافة <?php break; ?>
                                        <?php case ('edit'): ?> تعديل <?php break; ?>
                                        <?php case ('delete'): ?> حذف <?php break; ?>
                                        <?php case ('status'): ?> تغيير الحالة <?php break; ?>
                                        <?php case ('export'): ?> تصدير <?php break; ?>
                                        <?php case ('send'): ?> إرسال <?php break; ?>
                                        <?php case ('permissions'): ?> إدارة الصلاحيات <?php break; ?>
                                        <?php case ('admin'): ?> المدير <?php break; ?>
                                        <?php case ('employee'): ?> الموظف <?php break; ?>
                                        <?php case ('financial'): ?> مالية <?php break; ?>
                                        <?php case ('sales'): ?> مبيعات <?php break; ?>
                                        <?php case ('inventory'): ?> مخزون <?php break; ?>
                                        <?php default: ?> <?php echo e($permissionAction); ?> <?php break; ?>
                                    <?php endswitch; ?>
                                <?php else: ?>
                                    
                                    <?php echo e($permission->name); ?>

                                <?php endif; ?>
                            </span>
                        </label>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>

    <!-- أزرار الحفظ -->
    <div class="mt-6 flex justify-end space-x-2 space-x-reverse">
        <a href="<?php echo e(route('admin.permissions.index')); ?>"
           class="px-6 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">
            إلغاء
        </a>
        <button type="submit"
                class="px-6 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-all">
            <i class="fas fa-save ml-2"></i>
            حفظ التغييرات
        </button>
    </div>
</form>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحديد/إلغاء تحديد جميع الصلاحيات في مجموعة
        const groupHeaders = document.querySelectorAll('h4');
        groupHeaders.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                const container = this.parentElement;
                const checkboxes = container.querySelectorAll('input[type="checkbox"]');
                const allChecked = Array.from(checkboxes).every(cb => cb.checked);

                checkboxes.forEach(cb => {
                    cb.checked = !allChecked;
                });
            });
        });

        // إضافة تلميح للمجموعات
        groupHeaders.forEach(header => {
            header.title = 'اضغط لتحديد/إلغاء تحديد جميع الصلاحيات في هذه المجموعة';
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\Telegram Desktop\cs450 (3)\cs450 (3)\cs450\resources\views/admin/permissions/edit-user.blade.php ENDPATH**/ ?>