<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Table;

class TableSeeder extends Seeder
{
    public function run()
    {
        // حذف البيانات الموجودة
        Table::truncate();

        $tables = [
            [
                'table_number' => 1,
                'capacity' => 2,
                'status' => 'available',
                'location' => 'منطقة النافذة'
            ],
            [
                'table_number' => 2,
                'capacity' => 4,
                'status' => 'available',
                'location' => 'منطقة عامة'
            ],
            [
                'table_number' => 3,
                'capacity' => 6,
                'status' => 'available',
                'location' => 'منطقة عائلية'
            ],
            [
                'table_number' => 4,
                'capacity' => 2,
                'status' => 'available',
                'location' => 'منطقة هادئة'
            ],
            [
                'table_number' => 5,
                'capacity' => 8,
                'status' => 'available',
                'location' => 'منطقة عائلية'
            ],
            [
                'table_number' => 6,
                'capacity' => 4,
                'status' => 'available',
                'location' => 'منطقة النافذة'
            ],
            [
                'table_number' => 7,
                'capacity' => 2,
                'status' => 'available',
                'location' => 'منطقة هادئة'
            ],
            [
                'table_number' => 8,
                'capacity' => 10,
                'status' => 'available',
                'location' => 'منطقة المناسبات'
            ]
        ];

        foreach ($tables as $table) {
            Table::create($table);
        }
    }
}
