<!-- القائمة الجانبية للشاشات الكبيرة -->
<aside id="sidebar" class="fixed flex flex-col w-72 h-screen bg-white dark:bg-gray-800 shadow-lg sidebar-transition z-20 border-r border-gray-200 dark:border-gray-700 top-0 right-0">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="relative">
                    <span class="text-3xl font-black bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">Eat Hub</span>
                    <div class="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-orange-500 to-red-600 rounded-full animate-pulse"></div>
                </div>
                <span class="mr-3 px-3 py-1 bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900 dark:to-red-900 text-orange-700 dark:text-orange-300 text-xs rounded-full font-semibold border border-orange-200 dark:border-orange-700">الإدارة</span>
            </div>
            <?php if(Auth::user()->user_type == 'admin'): ?>
            <a href="<?php echo e(route('employee.dashboard')); ?>"
               class="p-3 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group"
               title="الانتقال لواجهة الموظف">
                <i class="fas fa-user-tie text-lg group-hover:scale-110 transition-transform"></i>
            </a>
            <a href="<?php echo e(route('customer.index')); ?>"
               class="p-3 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group"
               title="الانتقال للصفحة الرئيسية">
                <i class="fas fa-home text-lg group-hover:scale-110 transition-transform"></i>
            </a>
            <?php elseif(Auth::user()->user_type == 'employee' && Auth::user()->can('dashboard.admin')): ?>
            <a href="<?php echo e(route('employee.dashboard')); ?>"
               class="p-3 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group"
               title="العودة لواجهة الموظف">
                <i class="fas fa-arrow-left text-lg group-hover:scale-110 transition-transform"></i>
            </a>
            <?php endif; ?>
        </div>
    </div>

    <div class="py-6 flex flex-col flex-1 overflow-y-scroll scrollable-sidebar" style="max-height: calc(100vh - 200px);">
        <nav class="px-6 space-y-2">
            <a href="<?php echo e(route('admin.dashboard')); ?>" data-page="dashboard" class="nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-orange-50 dark:hover:bg-orange-900/20 hover:text-orange-700 dark:hover:text-orange-300 transition-all duration-300 <?php echo e(request()->routeIs('admin.dashboard') ? 'bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300' : ''); ?>">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-orange-500 to-red-600 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-tachometer-alt text-white"></i>
                </div>
                <span class="font-semibold">لوحة التحكم</span>
            </a>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('users.view')): ?>
            <a href="<?php echo e(route('admin.users')); ?>" data-page="users" class="nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-700 dark:hover:text-blue-300 transition-all duration-300 <?php echo e(request()->routeIs('admin.users*') ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300' : ''); ?>">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-users text-white"></i>
                </div>
                <span class="font-semibold">إدارة المستخدمين</span>
            </a>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('menu.view')): ?>
            <a href="<?php echo e(route('admin.menu')); ?>" data-page="menu" class="nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-700 dark:hover:text-red-300 transition-all duration-300 <?php echo e(request()->routeIs('admin.menu*') ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300' : ''); ?>">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-red-500 to-rose-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-utensils text-white"></i>
                </div>
                <span class="font-semibold">قائمة الطعام</span>
            </a>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('inventory.view')): ?>
            <a href="<?php echo e(route('admin.inventory')); ?>" data-page="inventory" class="nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-purple-50 dark:hover:bg-purple-900/20 hover:text-purple-700 dark:hover:text-purple-300 transition-all duration-300 <?php echo e(request()->routeIs('admin.inventory*') ? 'bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300' : ''); ?>">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-purple-500 to-indigo-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-warehouse text-white"></i>
                </div>
                <span class="font-semibold">إدارة المخزون</span>
            </a>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('orders.view')): ?>
            <a href="<?php echo e(route('admin.orders')); ?>" data-page="orders" class="nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-green-50 dark:hover:bg-green-900/20 hover:text-green-700 dark:hover:text-green-300 transition-all duration-300 <?php echo e(request()->routeIs('admin.orders*') ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300' : ''); ?>">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-shopping-cart text-white"></i>
                </div>
                <span class="font-semibold">إدارة الطلبات</span>
            </a>
            <?php endif; ?>

            <!-- <a href="<?php echo e(route('admin.reservations')); ?>" data-page="reservations" class="nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-cyan-50 dark:hover:bg-cyan-900/20 hover:text-cyan-700 dark:hover:text-cyan-300 transition-all duration-300 <?php echo e(request()->routeIs('admin.reservations*') ? 'bg-cyan-50 dark:bg-cyan-900/20 text-cyan-700 dark:text-cyan-300' : ''); ?>">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-cyan-500 to-blue-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-calendar-check text-white"></i>
                </div>
                <span class="font-semibold">إدارة الحجوزات</span>
            </a> -->

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('expenses.view')): ?>
            <a href="<?php echo e(route('admin.expenses')); ?>" data-page="expenses" class="nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-yellow-50 dark:hover:bg-yellow-900/20 hover:text-yellow-700 dark:hover:text-yellow-300 transition-all duration-300 <?php echo e(request()->routeIs('admin.expenses*') ? 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300' : ''); ?>">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-yellow-500 to-orange-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-money-bill-wave text-white"></i>
                </div>
                <span class="font-semibold">المصروفات</span>
            </a>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('reports.view')): ?>
            <a href="<?php echo e(route('admin.reports')); ?>" data-page="reports" class="nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-indigo-50 dark:hover:bg-indigo-900/20 hover:text-indigo-700 dark:hover:text-indigo-300 transition-all duration-300 <?php echo e(request()->routeIs('admin.reports*') ? 'bg-indigo-50 dark:bg-indigo-900/20 text-indigo-700 dark:text-indigo-300' : ''); ?>">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-indigo-500 to-blue-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-chart-bar text-white"></i>
                </div>
                <span class="font-semibold">التقارير</span>
            </a>
            <?php endif; ?>

            <a href="<?php echo e(route('admin.notifications')); ?>" data-page="notifications" class="sidebar-notification-link nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-pink-50 dark:hover:bg-pink-900/20 hover:text-pink-700 dark:hover:text-pink-300 transition-all duration-300 <?php echo e(request()->routeIs('admin.notifications*') ? 'bg-pink-50 dark:bg-pink-900/20 text-pink-700 dark:text-pink-300' : ''); ?>">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-pink-500 to-rose-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-bell sidebar-bell-icon text-white"></i>
                </div>
                <span class="font-semibold">الإشعارات</span>
                <span id="notification-badge" class="mr-auto bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold animate-pulse">0</span>
            </a>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('users.permissions')): ?>
            <a href="<?php echo e(route('admin.permissions.index')); ?>" data-page="permissions" class="nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-teal-50 dark:hover:bg-teal-900/20 hover:text-teal-700 dark:hover:text-teal-300 transition-all duration-300 <?php echo e(request()->routeIs('admin.permissions.index') || request()->routeIs('admin.permissions.edit-user') || request()->routeIs('admin.permissions.roles') ? 'bg-teal-50 dark:bg-teal-900/20 text-teal-700 dark:text-teal-300' : ''); ?>">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-teal-500 to-cyan-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-user-shield text-white"></i>
                </div>
                <span class="font-semibold">إدارة الصلاحيات</span>
            </a>

            <!-- <a href="<?php echo e(route('admin.permissions.employees')); ?>" data-page="employee-permissions" class="nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-emerald-50 dark:hover:bg-emerald-900/20 hover:text-emerald-700 dark:hover:text-emerald-300 transition-all duration-300 <?php echo e(request()->routeIs('admin.permissions.employees*') ? 'bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300' : ''); ?>">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-emerald-500 to-green-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-user-cog text-white"></i>
                </div>
                <span class="font-semibold">صلاحيات الموظفين</span>
            </a> -->
            <?php endif; ?>


        <div class="mt-auto px-6 py-4">
            <div class="border-t border-gray-200 dark:border-gray-700 pt-6 space-y-2">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('settings.view')): ?>
                <a href="<?php echo e(route('admin.settings')); ?>" class="nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700/20 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-300 <?php echo e(request()->routeIs('admin.settings*') ? 'bg-gray-50 dark:bg-gray-700/20 text-gray-800 dark:text-gray-200' : ''); ?>">
                    <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-gray-500 to-slate-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-cog text-white"></i>
                    </div>
                    <span class="font-semibold">الإعدادات</span>
                </a>
                <?php endif; ?>

                <form action="<?php echo e(route('logout')); ?>" method="POST" class="w-full">
                    <?php echo csrf_field(); ?>
                    <button type="submit" class="w-full group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-700 dark:hover:text-red-300 transition-all duration-300">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-red-500 to-pink-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-sign-out-alt text-white"></i>
                        </div>
                        <span class="font-semibold">تسجيل الخروج</span>
                    </button>
                </form>
            </div>
        </div>
    </div>
</aside>


<?php /**PATH C:\Users\<USER>\Downloads\Telegram Desktop\cs450 (3)\cs450 (3)\cs450\resources\views/includes/sidebar.blade.php ENDPATH**/ ?>