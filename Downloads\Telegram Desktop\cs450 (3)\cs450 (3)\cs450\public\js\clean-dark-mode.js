/**
 * تنظيف شامل لنظام الوضع المظلم
 * يحل جميع مشاكل التضارب
 */

// دالة تنظيف شاملة
window.cleanDarkModeSystem = function() {
    console.log('🧹 بدء التنظيف الشامل لنظام الوضع المظلم...');
    
    // 1. حذف جميع المفاتيح المتضاربة من localStorage
    const conflictingKeys = [
        'theme_preference',
        'effective_theme', 
        'theme_timestamp',
        'darkMode_backup'
    ];
    
    conflictingKeys.forEach(key => {
        if (localStorage.getItem(key)) {
            localStorage.removeItem(key);
            console.log(`🗑️ تم حذف ${key}`);
        }
    });
    
    // 2. الاحتفاظ بالوضع الحالي فقط
    const currentMode = document.documentElement.classList.contains('dark');
    localStorage.setItem('darkMode', currentMode ? 'true' : 'false');
    console.log(`💾 تم حفظ الوضع الحالي: ${currentMode ? 'مظلم' : 'فاتح'}`);
    
    // 3. تنظيف جميع event listeners المتضاربة
    const button = document.getElementById('darkModeToggle');
    if (button) {
        // إنشاء زر جديد بدون event listeners قديمة
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
        console.log('🔄 تم تنظيف event listeners للزر');
    }
    
    // 4. إعادة تحميل الصفحة للتأكد من التطبيق الصحيح
    console.log('🔄 إعادة تحميل الصفحة...');
    setTimeout(() => location.reload(), 1000);
};

// دالة إصلاح سريع بدون إعادة تحميل
window.quickFixDarkMode = function() {
    console.log('⚡ إصلاح سريع للوضع المظلم...');
    
    // تنظيف localStorage
    localStorage.removeItem('theme_preference');
    localStorage.removeItem('effective_theme');
    
    // تثبيت الوضع الحالي
    const currentMode = document.documentElement.classList.contains('dark');
    localStorage.setItem('darkMode', currentMode ? 'true' : 'false');
    
    // إصلاح الزر
    const button = document.getElementById('darkModeToggle');
    const icon = document.getElementById('darkModeIcon');
    
    if (button && icon) {
        // إزالة onclick القديم وإضافة جديد
        button.onclick = function(e) {
            e.preventDefault();
            
            const html = document.documentElement;
            const wasLight = !html.classList.contains('dark');
            
            if (wasLight) {
                html.classList.add('dark');
                localStorage.setItem('darkMode', 'true');
                icon.className = 'theme-icon fas fa-sun group-hover:rotate-180 transition-transform duration-500';
                console.log('🌙 تم التبديل إلى المظلم');
            } else {
                html.classList.remove('dark');
                localStorage.setItem('darkMode', 'false');
                icon.className = 'theme-icon fas fa-moon group-hover:rotate-180 transition-transform duration-500';
                console.log('☀️ تم التبديل إلى الفاتح');
            }
        };
        
        // تحديث الأيقونة
        const isDark = document.documentElement.classList.contains('dark');
        icon.className = isDark ? 
            'theme-icon fas fa-sun group-hover:rotate-180 transition-transform duration-500' :
            'theme-icon fas fa-moon group-hover:rotate-180 transition-transform duration-500';
        
        console.log('✅ تم إصلاح الزر والأيقونة');
    } else {
        console.error('❌ لم يتم العثور على الزر أو الأيقونة');
    }
    
    return {
        success: true,
        currentMode: currentMode ? 'dark' : 'light',
        savedMode: localStorage.getItem('darkMode')
    };
};

// تشغيل تلقائي للإصلاح السريع عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    // انتظار قليل للتأكد من تحميل كل شيء
    setTimeout(() => {
        console.log('🔧 تشغيل الإصلاح التلقائي...');
        quickFixDarkMode();
    }, 500);
});

console.log('🛠️ تم تحميل أدوات التنظيف والإصلاح');
console.log('💡 استخدم cleanDarkModeSystem() للتنظيف الشامل');
console.log('💡 استخدم quickFixDarkMode() للإصلاح السريع');
