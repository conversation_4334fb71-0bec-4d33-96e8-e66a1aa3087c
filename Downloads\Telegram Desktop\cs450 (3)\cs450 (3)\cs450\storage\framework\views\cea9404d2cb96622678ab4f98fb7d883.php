<!-- قسم التقييمات -->
<div class="py-16 bg-gray-100 dark:bg-gray-900">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-800 dark:text-white mb-4">ماذا يقول عملاؤنا</h2>
            <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">تجارب حقيقية لعملائنا الكرام</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php $__empty_1 = true; $__currentLoopData = $latestReviews ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="card-hover bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md relative">
                    <!-- نوع التقييم -->
                    <div class="absolute top-4 left-4">
                        <?php if(isset($review->type) && $review->type === 'reservation'): ?>
                            <span class="bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 px-2 py-1 rounded-full text-xs font-medium">
                                <i class="fas fa-calendar-check ml-1"></i>حجز
                            </span>
                        <?php else: ?>
                            <span class="bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-2 py-1 rounded-full text-xs font-medium">
                                <i class="fas fa-shopping-cart ml-1"></i>طلب
                            </span>
                        <?php endif; ?>
                    </div>

                    <div class="flex items-center mb-4 mt-6">
                        <div class="w-12 h-12 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center overflow-hidden ml-3">
                            <span class="text-primary text-xl font-bold"><?php echo e(substr($review->user->first_name ?? 'ع', 0, 1)); ?></span>
                        </div>
                        <div>
                            <h4 class="font-bold text-gray-800 dark:text-white"><?php echo e($review->user->first_name ?? 'عميل'); ?> <?php echo e($review->user->last_name ?? ''); ?></h4>
                            <div class="text-yellow-400 flex text-sm">
                                <?php for($i = 1; $i <= 5; $i++): ?>
                                    <?php if($i <= $review->rating): ?>
                                        <i class="fas fa-star"></i>
                                    <?php elseif($i - 0.5 <= $review->rating): ?>
                                        <i class="fas fa-star-half-alt"></i>
                                    <?php else: ?>
                                        <i class="far fa-star"></i>
                                    <?php endif; ?>
                                <?php endfor; ?>
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                <?php echo e($review->created_at->diffForHumans()); ?>

                            </div>
                        </div>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 italic">"<?php echo e($review->comment); ?>"</p>

                    <!-- معلومات إضافية حسب نوع التقييم -->
                    <?php if(isset($review->type) && $review->type === 'reservation'): ?>
                        <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                            <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                <i class="fas fa-utensils ml-1"></i>
                                <span>تقييم تجربة الحجز والخدمة</span>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                            <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                <i class="fas fa-pizza-slice ml-1"></i>
                                <span>تقييم جودة الطعام والتوصيل</span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <!-- تقييمات افتراضية في حالة عدم وجود تقييمات -->
                <?php echo $__env->make('customer.components.default-reviews', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endif; ?>
        </div>

        <!-- زر عرض جميع التقييمات -->
        <div class="mt-12 text-center">
            <button onclick="showAllReviews()" class="btn-hover-effect inline-block bg-white dark:bg-gray-800 border-2 border-primary text-primary hover:bg-primary hover:text-white dark:hover:bg-primary font-bold py-3 px-8 rounded-full text-lg transition">
                <i class="fas fa-comments ml-2"></i>
                عرض جميع التقييمات
            </button>
        </div>
    </div>
</div>

<!-- Modal لعرض جميع التقييمات -->
<div id="reviewsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div class="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-2xl font-bold text-gray-800 dark:text-white">جميع التقييمات</h3>
            <button onclick="closeReviewsModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="p-6 overflow-y-auto max-h-[70vh]">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6" id="allReviewsContainer">
                <!-- التقييمات ستتم إضافتها هنا -->
            </div>
        </div>
        <div class="p-6 border-t border-gray-200 dark:border-gray-700 flex justify-end">
            <button onclick="closeReviewsModal()" class="bg-primary hover:bg-primary/90 text-white px-6 py-2 rounded-lg transition">
                إغلاق
            </button>
        </div>
    </div>
</div>

<script>
function showAllReviews() {
    // جلب التقييمات الحقيقية من قاعدة البيانات
    fetch('/api/reviews/all')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.reviews.length > 0) {
                displayAllReviews(data.reviews);
            } else {
                showNoReviewsMessage();
            }
        })
        .catch(error => {
            console.error('Error fetching reviews:', error);
            showNoReviewsMessage();
        });
}

function displayAllReviews(reviews) {
    const container = document.getElementById('allReviewsContainer');
    container.innerHTML = '';

    reviews.forEach(review => {
        const stars = generateStars(review.rating);
        const reviewType = review.type || (review.order_id ? 'order' : 'reservation');
        const typeLabel = reviewType === 'reservation' ? 'حجز' : 'طلب';
        const typeIcon = reviewType === 'reservation' ? 'fas fa-calendar-check' : 'fas fa-shopping-cart';
        const typeColor = reviewType === 'reservation' ? 'green' : 'blue';

        // تنسيق التاريخ
        const reviewDate = new Date(review.created_at);
        const formattedDate = reviewDate.toLocaleDateString('ar-EG', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        const reviewHTML = `
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 relative">
                <!-- نوع التقييم -->
                <div class="absolute top-3 left-3">
                    <span class="${typeColor === 'green' ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' : 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'} px-2 py-1 rounded-full text-xs font-medium">
                        <i class="${typeIcon} ml-1"></i>${typeLabel}
                    </span>
                </div>

                <div class="flex items-center mb-3 mt-6">
                    <div class="w-10 h-10 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center ml-3">
                        <span class="text-primary text-lg font-bold">${review.first_name.charAt(0)}</span>
                    </div>
                    <div>
                        <h4 class="font-bold text-gray-800 dark:text-white">${review.first_name} ${review.last_name}</h4>
                        <div class="text-yellow-400 flex text-sm mb-1">
                            ${stars}
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                            ${formattedDate}
                        </div>
                    </div>
                </div>
                <p class="text-gray-600 dark:text-gray-300 text-sm italic">"${review.comment}"</p>

                <!-- معلومات إضافية -->
                <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                    <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                        <i class="${reviewType === 'reservation' ? 'fas fa-utensils' : 'fas fa-pizza-slice'} ml-1"></i>
                        <span>${reviewType === 'reservation' ? 'تقييم تجربة الحجز والخدمة' : 'تقييم جودة الطعام والتوصيل'}</span>
                    </div>
                </div>
            </div>
        `;
        container.innerHTML += reviewHTML;
    });

    document.getElementById('reviewsModal').classList.remove('hidden');
}

function showNoReviewsMessage() {
    const container = document.getElementById('allReviewsContainer');
    container.innerHTML = `
        <div class="text-center py-12">
            <i class="fas fa-star text-6xl text-gray-300 dark:text-gray-600 mb-4"></i>
            <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">لا توجد تقييمات بعد</h3>
            <p class="text-gray-600 dark:text-gray-400">كن أول من يقيم تجربته معنا!</p>
        </div>
    `;
    document.getElementById('reviewsModal').classList.remove('hidden');
}

function generateStars(rating) {
    let stars = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
            stars += '<i class="fas fa-star"></i>';
        } else if (i - 0.5 <= rating) {
            stars += '<i class="fas fa-star-half-alt"></i>';
        } else {
            stars += '<i class="far fa-star"></i>';
        }
    }
    return stars;
}

function closeReviewsModal() {
    document.getElementById('reviewsModal').classList.add('hidden');
}

// إغلاق المودال عند النقر خارجه
document.getElementById('reviewsModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeReviewsModal();
    }
});
</script>
<?php /**PATH C:\Users\<USER>\Downloads\Telegram Desktop\cs450 (3)\cs450 (3)\cs450\resources\views/customer/sections/reviews.blade.php ENDPATH**/ ?>