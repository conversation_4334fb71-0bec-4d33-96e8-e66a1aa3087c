<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر بسيط</title>
    
    <!-- تطبيق الوضع المظلم فوراً -->
    <script>
        (function() {
            const savedTheme = localStorage.getItem('darkMode');
            console.log('🔍 الثيم المحفوظ:', savedTheme);
            
            if (savedTheme === 'true' || savedTheme === 'enabled') {
                document.documentElement.classList.add('dark');
                console.log('🌙 تم تطبيق الوضع المظلم');
            } else if (savedTheme === 'false' || savedTheme === 'disabled') {
                document.documentElement.classList.remove('dark');
                console.log('☀️ تم تطبيق الوضع الفاتح');
            } else {
                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                    document.documentElement.classList.add('dark');
                    localStorage.setItem('darkMode', 'true');
                } else {
                    document.documentElement.classList.remove('dark');
                    localStorage.setItem('darkMode', 'false');
                }
            }
        })();
    </script>

    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class'
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100 dark:bg-gray-900 min-h-screen p-8">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                    اختبار زر الوضع المظلم البسيط
                </h1>
                
                <!-- زر الوضع المظلم - نسخة مبسطة -->
                <button id="darkModeToggle" class="p-3 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group" title="تبديل الثيم">
                    <i id="darkModeIcon" class="fas fa-moon group-hover:rotate-180 transition-transform duration-500"></i>
                </button>
            </div>
            
            <div class="space-y-4">
                <!-- معلومات الحالة -->
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h3 class="font-semibold mb-2 text-gray-900 dark:text-white">حالة النظام:</h3>
                    <div id="status" class="text-sm text-gray-600 dark:text-gray-300">
                        جاري التحميل...
                    </div>
                </div>
                
                <!-- أزرار الاختبار -->
                <div class="grid grid-cols-2 gap-4">
                    <button onclick="testToggle()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                        اختبار التبديل
                    </button>
                    <button onclick="resetMode()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg">
                        إعادة تعيين
                    </button>
                </div>
                
                <!-- سجل الأحداث -->
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h3 class="font-semibold mb-2 text-gray-900 dark:text-white">سجل الأحداث:</h3>
                    <div id="log" class="text-sm text-gray-600 dark:text-gray-300 max-h-40 overflow-y-auto bg-white dark:bg-gray-800 p-2 rounded">
                        جاري التحميل...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let logs = [];
        
        function addLog(message) {
            const time = new Date().toLocaleTimeString('ar');
            logs.unshift(`[${time}] ${message}`);
            if (logs.length > 10) logs = logs.slice(0, 10);
            
            const logElement = document.getElementById('log');
            if (logElement) {
                logElement.innerHTML = logs.join('<br>');
            }
        }
        
        function updateStatus() {
            const isDark = document.documentElement.classList.contains('dark');
            const saved = localStorage.getItem('darkMode');
            const button = document.getElementById('darkModeToggle');
            const icon = document.getElementById('darkModeIcon');
            
            const statusHTML = `
                الوضع الحالي: ${isDark ? '🌙 مظلم' : '☀️ فاتح'}<br>
                المحفوظ: ${saved || 'غير محدد'}<br>
                الزر: ${button ? '✅' : '❌'}<br>
                الأيقونة: ${icon ? '✅' : '❌'}
            `;
            
            const statusElement = document.getElementById('status');
            if (statusElement) {
                statusElement.innerHTML = statusHTML;
            }
        }
        
        function testToggle() {
            addLog('بدء اختبار التبديل...');
            
            const html = document.documentElement;
            const icon = document.getElementById('darkModeIcon');
            
            if (html.classList.contains('dark')) {
                html.classList.remove('dark');
                localStorage.setItem('darkMode', 'false');
                if (icon) {
                    icon.className = 'fas fa-moon group-hover:rotate-180 transition-transform duration-500';
                }
                addLog('تم التبديل إلى الوضع الفاتح ☀️');
            } else {
                html.classList.add('dark');
                localStorage.setItem('darkMode', 'true');
                if (icon) {
                    icon.className = 'fas fa-sun group-hover:rotate-180 transition-transform duration-500';
                }
                addLog('تم التبديل إلى الوضع المظلم 🌙');
            }
            
            updateStatus();
        }
        
        function resetMode() {
            addLog('إعادة تعيين النظام...');
            
            // حذف جميع المفاتيح المتعلقة بالثيم
            ['darkMode', 'theme_preference', 'effective_theme'].forEach(key => {
                localStorage.removeItem(key);
            });
            
            // إعادة تعيين إلى الوضع الفاتح
            document.documentElement.classList.remove('dark');
            localStorage.setItem('darkMode', 'false');
            
            const icon = document.getElementById('darkModeIcon');
            if (icon) {
                icon.className = 'fas fa-moon group-hover:rotate-180 transition-transform duration-500';
            }
            
            addLog('تم إعادة التعيين إلى الوضع الفاتح');
            updateStatus();
        }
        
        // إعداد الزر عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            addLog('تم تحميل الصفحة');
            
            const button = document.getElementById('darkModeToggle');
            if (button) {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    addLog('تم النقر على الزر!');
                    testToggle();
                });
                addLog('تم ربط الزر بنجاح');
            } else {
                addLog('لم يتم العثور على الزر!');
            }
            
            updateStatus();
            
            // تحديث دوري
            setInterval(updateStatus, 2000);
        });
        
        // مراقبة تغييرات الوضع
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const isDark = document.documentElement.classList.contains('dark');
                    addLog(`تم تغيير الوضع إلى: ${isDark ? 'مظلم' : 'فاتح'}`);
                }
            });
        });
        
        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['class']
        });
    </script>
</body>
</html>
