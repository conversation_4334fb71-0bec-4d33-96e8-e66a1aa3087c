# نظام الاتصال والإشعارات - Eat Hub 📧

## نظرة عامة

تم تطوير نظام اتصال متكامل يسمح للعملاء بإرسال رسائل للمطعم، مع إرسال إشعارات تلقائية لجميع المديرين والموظفين.

## ✨ المميزات

### 1. نموذج اتصال متطور
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **التحقق من البيانات**: فحص شامل للمدخلات
- **رسائل الخطأ**: باللغة العربية وواضحة
- **حفظ البيانات**: الاحتفاظ بالبيانات عند حدوث خطأ

### 2. خريطة تفاعلية
- **موقع المطعم**: خريطة حقيقية تعرض الموقع
- **معلومات شاملة**: العنوان، الهاتف، ساعات العمل
- **روابط الاتجاهات**: للوصول السهل للمطعم
- **تصميم جميل**: متناسق مع هوية الموقع

### 3. نظام إشعارات ذكي
- **إشعارات فورية**: للمديرين والموظفين
- **تصنيف الإشعارات**: حسب النوع (اتصال، طلبات، حجوزات)
- **إحصائيات شاملة**: عدد الإشعارات المقروءة وغير المقروءة
- **واجهة سهلة**: لإدارة الإشعارات

## 🚀 كيفية الاستخدام

### للعملاء:
1. اذهب إلى صفحة الاتصال: `/contact`
2. املأ النموذج بالمعلومات المطلوبة
3. اختر موضوع الرسالة
4. اكتب رسالتك
5. اضغط "إرسال الرسالة"

### للمديرين والموظفين:
1. سجل دخول إلى النظام
2. اذهب إلى قسم الإشعارات
3. ستجد إشعار جديد بنوع "رسالة اتصال"
4. انقر على الإشعار لرؤية التفاصيل

## 🧪 اختبار النظام

### صفحة الاختبار الشاملة
زر الرابط: `/test-contact-system`

### روابط الاختبار السريع:
- **صفحة الاتصال**: `/contact`
- **دخول كمدير**: `/test-admin-login`
- **دخول كموظف**: `/test-employee-login`

### خطوات الاختبار:
1. أرسل رسالة من صفحة الاتصال
2. سجل دخول كمدير أو موظف
3. تحقق من وصول الإشعار
4. اقرأ تفاصيل الرسالة

## 📁 الملفات المحدثة

### Controllers:
- `app/Http/Controllers/ContactController.php` - معالجة رسائل الاتصال
- `app/Http/Controllers/NotificationController.php` - إدارة الإشعارات

### Views:
- `resources/views/contact.blade.php` - صفحة الاتصال
- `resources/views/admin/notifications/index.blade.php` - إشعارات المدير
- `resources/views/employee/notifications/index.blade.php` - إشعارات الموظف

### Routes:
- `routes/web.php` - المسارات الجديدة

## ⚙️ الإعدادات

### تخصيص معلومات المطعم:
في ملف `contact.blade.php`:
```php
// العنوان
'شارع الجمهورية، طرابلس، ليبيا'

// الهاتف
'+218 91 234 5678'

// البريد الإلكتروني
'<EMAIL>'
```

### تخصيص الخريطة:
```javascript
// الإحداثيات
const lat = 32.8872;  // خط العرض
const lng = 13.1913;  // خط الطول
```

## 🔧 المتطلبات التقنية

### المكتبات المستخدمة:
- **Laravel 11**: إطار العمل الأساسي
- **Leaflet**: مكتبة الخرائط
- **Tailwind CSS**: التصميم
- **Font Awesome**: الأيقونات

### قاعدة البيانات:
- جدول `notifications`: لحفظ الإشعارات
- جدول `users`: المستخدمين (مديرين وموظفين)

## 🛡️ الأمان

### الحماية المطبقة:
- **CSRF Protection**: حماية من هجمات CSRF
- **Data Validation**: التحقق من صحة البيانات
- **SQL Injection Prevention**: حماية من حقن SQL
- **XSS Protection**: حماية من هجمات XSS

## 📊 الإحصائيات

### ما يتم تتبعه:
- عدد الرسائل المرسلة
- عدد الإشعارات المقروءة/غير المقروءة
- أنواع الإشعارات المختلفة
- إحصائيات حسب المستخدم

## 🔄 التحديثات المستقبلية

### مميزات مقترحة:
- **إشعارات البريد الإلكتروني**: إرسال إيميل عند وصول رسالة
- **إشعارات SMS**: رسائل نصية للحالات العاجلة
- **ردود تلقائية**: رسائل شكر تلقائية للعملاء
- **تصنيف الرسائل**: حسب الأولوية والنوع
- **أرشفة الرسائل**: حفظ الرسائل القديمة

## 🆘 حل المشاكل

### المشاكل الشائعة:

#### الإشعارات لا تصل:
- تأكد من وجود مديرين/موظفين في النظام
- تحقق من صحة أنواع المستخدمين (`user_type`)

#### الخريطة لا تظهر:
- تحقق من اتصال الإنترنت
- تأكد من صحة الإحداثيات

#### النموذج لا يعمل:
- تحقق من CSRF token
- تأكد من صحة المسارات

## 📞 الدعم التقني

للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من ملفات السجل (logs)
3. استخدم صفحة الاختبار للتشخيص

## 🎯 الخلاصة

تم تطوير نظام اتصال متكامل وفعال يحسن من تجربة العملاء ويساعد الإدارة على متابعة استفسارات العملاء بكفاءة عالية.

---

**تاريخ آخر تحديث**: ديسمبر 2024  
**الإصدار**: 1.0  
**المطور**: Augment Agent
