@extends('layouts.admin')

@section('title', 'التقارير - لوحة تحكم Eat Hub')

@section('page-title', 'التقارير')

@section('content')
<!-- العنوان الرئيسي -->
<div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <div>
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">التقارير الشاملة</h2>
        <p class="text-gray-600 dark:text-gray-400">نظام تقارير متكامل يغطي جميع جوانب المطعم</p>
    </div>
    <div class="flex space-x-2 space-x-reverse mt-4 md:mt-0">
        <button onclick="printAllReports()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center">
            <i class="fas fa-print mr-2"></i>
            طباعة التقارير
        </button>
        <button onclick="exportAllReports()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center">
            <i class="fas fa-download mr-2"></i>
            تصدير جميع التقارير
        </button>
    </div>
</div>

<!-- فلاتر التاريخ العامة -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">فلاتر التاريخ العامة</h3>
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
            <input type="date" id="global_start_date" value="{{ date('Y-m-01') }}"
                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
            <input type="date" id="global_end_date" value="{{ date('Y-m-d') }}"
                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">فترات سريعة</label>
            <select id="quick_period" onchange="setQuickPeriod()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                <option value="">اختر فترة</option>
                <option value="today">اليوم</option>
                <option value="yesterday">أمس</option>
                <option value="this_week">هذا الأسبوع</option>
                <option value="last_week">الأسبوع الماضي</option>
                <option value="this_month">هذا الشهر</option>
                <option value="last_month">الشهر الماضي</option>
                <option value="this_year">هذا العام</option>
            </select>
        </div>
        <div class="flex items-end">
            <button onclick="applyGlobalFilters()" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center justify-center">
                <i class="fas fa-filter mr-2"></i>
                تطبيق الفلاتر
            </button>
        </div>
    </div>
</div>

<!-- التقارير الأساسية -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
    <!-- تقرير المبيعات -->
    @can('reports.sales')
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all card-hover border-l-4 border-blue-500">
        <div class="flex justify-between items-start mb-4">
            <div>
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2">📊 تقرير المبيعات</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">المبيعات اليومية والمنتجات الأكثر مبيعاً</p>
            </div>
            <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3">
                <i class="fas fa-chart-line text-blue-500 text-xl"></i>
            </div>
        </div>
        <div class="space-y-3">
            <a href="{{ route('admin.reports.sales') }}" class="block bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-md transition-all text-center">
                <i class="fas fa-eye mr-2"></i>
                عرض التقرير
            </a>
        </div>
    </div>
    @endcan

    <!-- تقرير المصروفات -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all card-hover border-l-4 border-red-500">
        <div class="flex justify-between items-start mb-4">
            <div>
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2">💸 تقرير المصروفات</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">المصروفات اليومية والتصنيفات</p>
            </div>
            <div class="rounded-full bg-red-100 dark:bg-red-900/30 p-3">
                <i class="fas fa-money-bill-wave text-red-500 text-xl"></i>
            </div>
        </div>
        <div class="space-y-3">
            <a href="{{ route('admin.reports.expenses') }}" class="block bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded-md transition-all text-center">
                <i class="fas fa-eye mr-2"></i>
                عرض التقرير
            </a>
        </div>
    </div>

    <!-- التقرير المالي الشامل -->
    @can('reports.financial')
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all card-hover border-l-4 border-green-500">
        <div class="flex justify-between items-start mb-4">
            <div>
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2">💰 التقرير المالي الشامل</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">الأرباح والخسائر والتحليل المالي المتكامل</p>
            </div>
            <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3">
                <i class="fas fa-coins text-green-500 text-xl"></i>
            </div>
        </div>
        <div class="space-y-3">
            <a href="{{ route('admin.reports.financial') }}" class="block bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded-md transition-all text-center">
                <i class="fas fa-eye mr-2"></i>
                عرض التقرير
            </a>
        </div>
    </div>
    @endcan

    <!-- تقرير الطلبات -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all card-hover border-l-4 border-orange-500">
        <div class="flex justify-between items-start mb-4">
            <div>
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2">🛒 تقرير الطلبات</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">حالة الطلبات والإحصائيات التفصيلية</p>
            </div>
            <div class="rounded-full bg-orange-100 dark:bg-orange-900/30 p-3">
                <i class="fas fa-shopping-cart text-orange-500 text-xl"></i>
            </div>
        </div>
        <div class="space-y-3">
            <a href="{{ route('admin.reports.orders') }}" class="block bg-orange-500 hover:bg-orange-600 text-white font-bold py-2 px-4 rounded-md transition-all text-center">
                <i class="fas fa-eye mr-2"></i>
                عرض التقرير
            </a>
        </div>
    </div>

    <!-- تقرير المخزون -->
    @can('reports.inventory')
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all card-hover border-l-4 border-purple-500">
        <div class="flex justify-between items-start mb-4">
            <div>
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2">📦 تقرير المخزون</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">حالة المخزون والمنتجات منخفضة المخزون</p>
            </div>
            <div class="rounded-full bg-purple-100 dark:bg-purple-900/30 p-3">
                <i class="fas fa-boxes text-purple-500 text-xl"></i>
            </div>
        </div>
        <div class="space-y-3">
            <a href="{{ route('admin.reports.inventory') }}" class="block bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded-md transition-all text-center">
                <i class="fas fa-eye mr-2"></i>
                عرض التقرير
            </a>
        </div>
    </div>
    @endcan

    <!-- تقرير العملاء -->
    @can('reports.customers')
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all card-hover border-l-4 border-indigo-500">
        <div class="flex justify-between items-start mb-4">
            <div>
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2">👥 تقرير العملاء</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">إحصائيات العملاء ونشاطهم</p>
            </div>
            <div class="rounded-full bg-indigo-100 dark:bg-indigo-900/30 p-3">
                <i class="fas fa-users text-indigo-500 text-xl"></i>
            </div>
        </div>
        <div class="space-y-3">
            <a href="{{ route('admin.reports.customers') }}" class="block bg-indigo-500 hover:bg-indigo-600 text-white font-bold py-2 px-4 rounded-md transition-all text-center">
                <i class="fas fa-eye mr-2"></i>
                عرض التقرير
            </a>
        </div>
    </div>
    @endcan
</div>

<!-- التقارير المتقدمة -->
<div class="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-lg p-6 mb-6">
    <div class="flex items-center justify-between mb-6">
        <div>
            <h3 class="text-xl font-bold text-gray-800 dark:text-white">📈 التقارير المتقدمة</h3>
            <p class="text-gray-600 dark:text-gray-400">تقارير تحليلية متقدمة لاتخاذ قرارات أفضل</p>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- تقرير الأداء -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all card-hover border-l-4 border-blue-500">
            <div class="flex justify-between items-start mb-4">
                <div>
                    <h4 class="text-lg font-bold text-gray-800 dark:text-white mb-2">📊 تقرير الأداء</h4>
                    <p class="text-gray-600 dark:text-gray-400 text-sm">مقارنة الأداء بين فترات زمنية مختلفة</p>
                </div>
                <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3">
                    <i class="fas fa-chart-bar text-blue-500 text-xl"></i>
                </div>
            </div>
            <a href="{{ route('admin.reports.performance') }}" class="block bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-md transition-all text-center">
                <i class="fas fa-eye mr-2"></i>
                عرض التقرير
            </a>
        </div>

        <!-- تقرير أداء الموظفين -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all card-hover border-l-4 border-teal-500">
            <div class="flex justify-between items-start mb-4">
                <div>
                    <h4 class="text-lg font-bold text-gray-800 dark:text-white mb-2">👨‍💼 تقرير أداء الموظفين</h4>
                    <p class="text-gray-600 dark:text-gray-400 text-sm">إحصائيات أداء الموظفين ومعدل الإنجاز</p>
                </div>
                <div class="rounded-full bg-teal-100 dark:bg-teal-900/30 p-3">
                    <i class="fas fa-user-tie text-teal-500 text-xl"></i>
                </div>
            </div>
            <a href="{{ route('admin.reports.employee-performance') }}" class="block bg-teal-500 hover:bg-teal-600 text-white font-bold py-2 px-4 rounded-md transition-all text-center">
                <i class="fas fa-eye mr-2"></i>
                عرض التقرير
            </a>
        </div>
    </div>
</div>

<!-- JavaScript للتصدير والفلاتر -->
<script>
// طباعة جميع التقارير
function printAllReports() {
    const startDate = document.getElementById('global_start_date').value;
    const endDate = document.getElementById('global_end_date').value;

    if (!startDate || !endDate) {
        showNotification('يرجى تحديد فترة زمنية أولاً', 'warning');
        return;
    }

    showNotification('جاري تحضير التقارير للطباعة...', 'info');

    // فتح نافذة طباعة مع جميع التقارير
    const printWindow = window.open(`/admin/reports/print-all?start_date=${startDate}&end_date=${endDate}`, '_blank');
    if (printWindow) {
        printWindow.onload = function() {
            printWindow.print();
        };
    }
}

// تصدير جميع التقارير
function exportAllReports() {
    const startDate = document.getElementById('global_start_date').value;
    const endDate = document.getElementById('global_end_date').value;

    if (!startDate || !endDate) {
        showNotification('يرجى تحديد فترة زمنية أولاً', 'warning');
        return;
    }

    showNotification('جاري تحضير ملف التصدير...', 'info');

    // تصدير ملف Excel شامل
    window.open(`/admin/reports/export-all?start_date=${startDate}&end_date=${endDate}`, '_blank');
}

// تطبيق الفترات السريعة
function setQuickPeriod() {
    const period = document.getElementById('quick_period').value;
    const today = new Date();
    let startDate, endDate;

    switch(period) {
        case 'today':
            startDate = endDate = today.toISOString().split('T')[0];
            break;
        case 'yesterday':
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            startDate = endDate = yesterday.toISOString().split('T')[0];
            break;
        case 'this_week':
            const startOfWeek = new Date(today);
            startOfWeek.setDate(today.getDate() - today.getDay());
            startDate = startOfWeek.toISOString().split('T')[0];
            endDate = today.toISOString().split('T')[0];
            break;
        case 'last_week':
            const lastWeekEnd = new Date(today);
            lastWeekEnd.setDate(today.getDate() - today.getDay() - 1);
            const lastWeekStart = new Date(lastWeekEnd);
            lastWeekStart.setDate(lastWeekEnd.getDate() - 6);
            startDate = lastWeekStart.toISOString().split('T')[0];
            endDate = lastWeekEnd.toISOString().split('T')[0];
            break;
        case 'this_month':
            startDate = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
            endDate = today.toISOString().split('T')[0];
            break;
        case 'last_month':
            const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
            const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
            startDate = lastMonth.toISOString().split('T')[0];
            endDate = lastMonthEnd.toISOString().split('T')[0];
            break;
        case 'this_year':
            startDate = new Date(today.getFullYear(), 0, 1).toISOString().split('T')[0];
            endDate = today.toISOString().split('T')[0];
            break;
        default:
            return;
    }

    document.getElementById('global_start_date').value = startDate;
    document.getElementById('global_end_date').value = endDate;
}

// تطبيق الفلاتر العامة
function applyGlobalFilters() {
    const startDate = document.getElementById('global_start_date').value;
    const endDate = document.getElementById('global_end_date').value;

    // يمكن إضافة منطق إضافي هنا لتحديث التقارير
    showNotification('تم تطبيق الفلاتر بنجاح', 'success');
}

// إظهار الإشعارات
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg text-white ${
        type === 'success' ? 'bg-green-500' :
        type === 'error' ? 'bg-red-500' :
        type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : type === 'warning' ? 'exclamation' : 'info'}-circle mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);

    // إزالة الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// تحديث الصفحة عند تحميلها
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات تفاعلية للبطاقات
    const cards = document.querySelectorAll('.card-hover');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // تحديث التواريخ الافتراضية
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    document.getElementById('global_start_date').value = firstDayOfMonth.toISOString().split('T')[0];
    document.getElementById('global_end_date').value = today.toISOString().split('T')[0];
});
</script>

<style>
.card-hover {
    transition: all 0.3s ease-in-out;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

/* تحسين الفلاتر */
#quick_period:focus,
#global_start_date:focus,
#global_end_date:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    border-color: #3b82f6;
}

/* تحسين البطاقات المميزة */
.border-l-4 {
    position: relative;
}

.border-l-4::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 4px;
    background: linear-gradient(180deg, var(--tw-border-opacity), transparent);
}

/* تحسين الأزرار */
.bg-blue-500:hover,
.bg-green-500:hover,
.bg-purple-500:hover,
.bg-orange-500:hover,
.bg-red-500:hover,
.bg-indigo-500:hover,
.bg-yellow-500:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* تحسين الإشعارات */
.notification-enter {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* تحسين الخلفيات المتدرجة */
.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

/* تحسين الظلال */
.shadow-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
</style>

@endsection
