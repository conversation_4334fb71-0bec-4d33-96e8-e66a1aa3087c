<?php

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

echo "فحص صلاحيات الموظفين...\n\n";

// عرض جميع الموظفين
$employees = User::where('user_type', 'employee')->get();

echo "الموظفين في النظام:\n";
foreach ($employees as $employee) {
    echo "- {$employee->first_name} {$employee->last_name} ({$employee->email})\n";
    
    // عرض الأدوار
    $roles = $employee->roles->pluck('name')->toArray();
    if (!empty($roles)) {
        echo "  الأدوار: " . implode(', ', $roles) . "\n";
    }
    
    // عرض الصلاحيات المباشرة
    $permissions = $employee->permissions->pluck('name')->toArray();
    if (!empty($permissions)) {
        echo "  الصلاحيات المباشرة: " . count($permissions) . " صلاحية\n";
        foreach ($permissions as $perm) {
            echo "    - $perm\n";
        }
    }
    
    // فحص صلاحية الوصول للإدارة
    $canAccessAdmin = $employee->can('dashboard.admin');
    echo "  يمكنه الوصول للوحة الإدارة: " . ($canAccessAdmin ? "نعم" : "لا") . "\n";
    
    echo "\n";
}

echo "الأدوار المتاحة في النظام:\n";
$roles = Role::all();
foreach ($roles as $role) {
    echo "- {$role->name} ({$role->permissions->count()} صلاحية)\n";
}
