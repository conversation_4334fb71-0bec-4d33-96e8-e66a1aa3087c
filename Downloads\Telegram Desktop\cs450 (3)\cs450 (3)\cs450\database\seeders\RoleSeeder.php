<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // تحقق إذا كان الدور موجودًا قبل إنشائه
        if (!Role::where('name', 'admin')->exists()) {
            Role::create(['name' => 'admin']);
        }

        if (!Role::where('name', 'employee')->exists()) {
            Role::create(['name' => 'employee']);
        }

        if (!Role::where('name', 'customer')->exists()) {
            Role::create(['name' => 'customer']);
        }
    }
}