@include('customer.partials.head')

<body class="bg-gray-50 dark:bg-gray-900">

@include('customer.partials.header')

<!-- المحتوى الرئيسي -->
<main class="min-h-screen pt-20">
    <div class="container mx-auto px-4 py-8">
        <!-- عنوان الصفحة -->
        <div class="flex items-center mb-8">
            <a href="{{ route('customer.cart') }}"
               class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 p-2 rounded-lg transition ml-4">
                <i class="fas fa-arrow-right"></i>
            </a>
            <div>
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2">إتمام الطلب</h1>
                <p class="text-gray-600 dark:text-gray-400">أكمل بياناتك لإتمام عملية الشراء</p>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- نموذج الدفع -->
            <div class="lg:col-span-2">
                <form id="checkoutForm" action="{{ route('customer.orders.store') }}" method="POST">
                    @csrf

                    <!-- نوع الطلب -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-6">
                        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">
                            <i class="fas fa-truck text-primary ml-2"></i>
                            نوع الطلب
                        </h2>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                            <label class="flex items-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 order-type-option">
                                <input type="radio" name="order_type" value="dine_in" class="ml-3" {{ ($orderType ?? 'dine_in') === 'dine_in' ? 'checked' : '' }}>
                                <div class="flex items-center">
                                    <i class="fas fa-utensils text-primary text-xl ml-3"></i>
                                    <div>
                                        <div class="font-medium text-gray-800 dark:text-white">تناول في المطعم</div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">خدمة الطاولة</div>
                                    </div>
                                </div>
                            </label>

                            <label class="flex items-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 order-type-option">
                                <input type="radio" name="order_type" value="pickup" class="ml-3" {{ ($orderType ?? 'dine_in') === 'pickup' ? 'checked' : '' }}>
                                <div class="flex items-center">
                                    <i class="fas fa-shopping-bag text-green-500 text-xl ml-3"></i>
                                    <div>
                                        <div class="font-medium text-gray-800 dark:text-white">استلام من المطعم</div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">طلب مسبق للاستلام</div>
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- معلومات الطلب -->
                    <div id="orderInfo" class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-6">
                        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">
                            <i class="fas fa-user text-primary ml-2"></i>
                            معلومات الطلب
                        </h2>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم الأول</label>
                                <input type="text" name="first_name" value="{{ auth()->user()->first_name }}"
                                       class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم الأخير</label>
                                <input type="text" name="last_name" value="{{ auth()->user()->last_name }}"
                                       class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white" required>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الهاتف</label>
                                <input type="tel" name="phone" value="{{ auth()->user()->phone }}"
                                       class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البريد الإلكتروني</label>
                                <input type="email" name="email" value="{{ auth()->user()->email }}"
                                       class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white" required>
                            </div>
                        </div>

                        <div class="mt-4" id="tableInfo" style="display: none;">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الطاولة (اختياري)</label>
                            <input type="number" name="table_number" min="1" max="50"
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                   placeholder="أدخل رقم الطاولة إذا كنت تجلس بالفعل">
                        </div>

                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ملاحظات إضافية</label>
                            <textarea name="notes" rows="3"
                                      class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                      placeholder="أي ملاحظات خاصة بالطلب..."></textarea>
                        </div>
                    </div>

                    <!-- طريقة الدفع -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-6">
                        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">
                            <i class="fas fa-credit-card text-primary ml-2"></i>
                            طريقة الدفع
                        </h2>

                        <div class="space-y-4">
                            <!-- الدفع نقداً - متاح دائماً -->
                            <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                                <input type="radio" name="payment_method" value="cash" class="ml-3" checked>
                                <div class="flex items-center">
                                    <i class="fas fa-money-bill-wave text-green-500 text-xl ml-3"></i>
                                    <div>
                                        <div class="font-medium text-gray-800 dark:text-white">
                                            <span class="delivery-text">الدفع عند التوصيل</span>
                                            <span class="pickup-text" style="display: none;">الدفع في المطعم</span>
                                        </div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">
                                            <span class="delivery-text">ادفع نقداً عند استلام الطلب</span>
                                            <span class="pickup-text" style="display: none;">ادفع نقداً في المطعم</span>
                                        </div>
                                    </div>
                                </div>
                            </label>

                            <!-- الدفع الإلكتروني - للتوصيل فقط -->
                            <div class="electronic-payment-options">
                                <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <input type="radio" name="payment_method" value="card" class="ml-3">
                                    <div class="flex items-center">
                                        <i class="fas fa-credit-card text-blue-500 text-xl ml-3"></i>
                                        <div>
                                            <div class="font-medium text-gray-800 dark:text-white">بطاقة ائتمان</div>
                                            <div class="text-sm text-gray-600 dark:text-gray-400">ادفع بالبطاقة الائتمانية</div>
                                        </div>
                                    </div>
                                </label>

                                <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <input type="radio" name="payment_method" value="wallet" class="ml-3">
                                    <div class="flex items-center">
                                        <i class="fas fa-wallet text-purple-500 text-xl ml-3"></i>
                                        <div>
                                            <div class="font-medium text-gray-800 dark:text-white">محفظة إلكترونية</div>
                                            <div class="text-sm text-gray-600 dark:text-gray-400">ادفع عبر المحفظة الإلكترونية</div>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظات إضافية -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
                        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">
                            <i class="fas fa-sticky-note text-primary ml-2"></i>
                            ملاحظات إضافية
                        </h2>

                        <textarea name="notes" rows="4"
                                  class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                  placeholder="أي ملاحظات خاصة للطلب..."></textarea>
                    </div>
                </form>
            </div>

            <!-- ملخص الطلب -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 sticky top-24">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">ملخص الطلب</h2>

                    <!-- عناصر الطلب -->
                    <div class="space-y-3 mb-6 max-h-60 overflow-y-auto">
                        @foreach($cartItems as $item)
                        <div class="flex items-center justify-between text-sm">
                            <div class="flex items-center">
                                <span class="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs ml-2">{{ $item->quantity }}</span>
                                <span class="text-gray-800 dark:text-white">{{ $item->menuItem->name }}</span>
                            </div>
                            <span class="font-medium text-gray-800 dark:text-white">{{ number_format($item->subtotal, 2) }} د.ل</span>
                        </div>
                        @endforeach
                    </div>

                    <!-- تفاصيل التكلفة -->
                    <div class="space-y-3 mb-6 border-t border-gray-200 dark:border-gray-700 pt-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">المجموع الفرعي:</span>
                            <span class="font-medium text-gray-800 dark:text-white">{{ number_format($total, 2) }} د.ل</span>
                        </div>

                        @if(isset($cartOffers) && $cartOffers->count() > 0)
                        <!-- العروض المطبقة -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-3">
                            <h4 class="text-sm font-medium text-gray-800 dark:text-white mb-2">العروض المطبقة:</h4>
                            @foreach($cartOffers as $cartOffer)
                            <div class="flex justify-between text-sm text-green-600 dark:text-green-400">
                                <span>{{ $cartOffer->offer->title }}</span>
                                <span>-{{ number_format($cartOffer->discount_amount, 2) }} د.ل</span>
                            </div>
                            @endforeach
                            <div class="flex justify-between text-sm font-medium text-green-600 dark:text-green-400 border-t border-green-200 dark:border-green-700 pt-2 mt-2">
                                <span>إجمالي الخصم:</span>
                                <span>-{{ number_format($offersDiscount ?? 0, 2) }} د.ل</span>
                            </div>
                        </div>
                        @endif

                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">الضريبة ({{ $taxRate }}%):</span>
                            <span id="taxAmount" class="font-medium text-gray-800 dark:text-white">{{ $settingsHelper->formatCurrency($tax) }}</span>
                        </div>
                        <div class="flex justify-between text-sm" id="serviceFeeRow" style="display: none;">
                            <span class="text-gray-600 dark:text-gray-400">رسوم الخدمة:</span>
                            <span class="font-medium text-gray-800 dark:text-white" id="serviceFeeAmount">0.00 د.ل</span>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-3">
                            <div class="flex justify-between">
                                <span class="text-lg font-semibold text-gray-800 dark:text-white">المجموع الكلي:</span>
                                <span class="text-lg font-bold text-primary" id="finalTotalAmount">{{ number_format($finalTotal, 2) }} د.ل</span>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="space-y-3">
                        <button type="submit" form="checkoutForm"
                                class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition">
                            <i class="fas fa-check ml-2"></i>
                            تأكيد الطلب
                        </button>
                        <a href="{{ route('customer.cart') }}"
                           class="w-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-3 px-6 rounded-lg transition text-center block">
                            <i class="fas fa-arrow-right ml-2"></i>
                            العودة للسلة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

@include('customer.partials.footer')

<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحكم في نوع الطلب
    const orderTypeRadios = document.querySelectorAll('input[name="order_type"]');
    const orderInfo = document.getElementById('orderInfo');
    const tableInfo = document.getElementById('tableInfo');
    const serviceFeeRow = document.getElementById('serviceFeeRow');
    const serviceFeeAmount = document.getElementById('serviceFeeAmount');
    const finalTotalAmount = document.getElementById('finalTotalAmount');

    // القيم الأساسية
    const subtotal = parseFloat('{{ $total }}');
    const offersDiscount = parseFloat('{{ $offersDiscount ?? 0 }}');
    const subtotalAfterDiscount = subtotal - offersDiscount;
    const taxRate = parseFloat('{{ $taxRate }}');



    // حساب الضريبة بناءً على المجموع الفرعي بعد الخصم
    function calculateTax(amount) {
        return amount * (taxRate / 100);
    }

    // تحديث عرض الضريبة
    function updateTaxDisplay(taxAmount) {
        const taxElement = document.getElementById('taxAmount');
        if (taxElement) {
            taxElement.textContent = taxAmount.toFixed(2) + ' د.ل';
        }
    }

    orderTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'pickup') {
                // استلام من المطعم - إخفاء معلومات الطاولة
                tableInfo.style.display = 'none';
                serviceFeeRow.style.display = 'none';

                // تحديث المجموع الكلي (بدون رسوم إضافية)
                const tax = calculateTax(subtotalAfterDiscount);
                const newTotal = subtotalAfterDiscount + tax;
                finalTotalAmount.textContent = newTotal.toFixed(2) + ' د.ل';

                // تحديث عرض الضريبة
                document.querySelector('.text-gray-600:contains("الضريبة")').nextElementSibling.textContent = tax.toFixed(2) + ' د.ل';

                // إزالة required من رقم الطاولة
                const tableField = document.querySelector('input[name="table_number"]');
                if (tableField) {
                    tableField.removeAttribute('required');
                }

            } else if (this.value === 'dine_in') {
                // تناول في المطعم - إظهار معلومات الطاولة
                tableInfo.style.display = 'block';
                serviceFeeRow.style.display = 'none'; // لا توجد رسوم إضافية

                // تحديث المجموع الكلي (بدون رسوم إضافية)
                const tax = calculateTax(subtotalAfterDiscount);
                const newTotal = subtotalAfterDiscount + tax;
                finalTotalAmount.textContent = newTotal.toFixed(2) + ' د.ل';

                // تحديث عرض الضريبة
                updateTaxDisplay(tax);
            }

            // تحديث تصميم الخيارات
            document.querySelectorAll('.order-type-option').forEach(option => {
                option.classList.remove('border-primary', 'bg-primary/5');
                option.classList.add('border-gray-300', 'dark:border-gray-600');
            });

            this.closest('.order-type-option').classList.remove('border-gray-300', 'dark:border-gray-600');
            this.closest('.order-type-option').classList.add('border-primary', 'bg-primary/5');
        });
    });

    // تطبيق التصميم الأولي للخيار المحدد
    const checkedOption = document.querySelector('input[name="order_type"]:checked');
    if (checkedOption) {
        checkedOption.closest('.order-type-option').classList.remove('border-gray-300', 'dark:border-gray-600');
        checkedOption.closest('.order-type-option').classList.add('border-primary', 'bg-primary/5');

        // تطبيق الحالة الأولية
        if (checkedOption.value === 'pickup') {
            // إخفاء معلومات الطاولة
            tableInfo.style.display = 'none';
        } else if (checkedOption.value === 'dine_in') {
            // إظهار معلومات الطاولة
            tableInfo.style.display = 'block';
        }

            // تغيير نص الدفع النقدي
            document.querySelectorAll('.delivery-text').forEach(el => el.style.display = 'none');
            document.querySelectorAll('.pickup-text').forEach(el => el.style.display = 'inline');

            // تحديث رسوم التوصيل
            deliveryFeeAmount.textContent = '0.00 د.ل';
            deliveryFeeRow.querySelector('.text-gray-600').textContent = 'رسوم التوصيل (ملغاة):';
            deliveryFeeAmount.classList.add('line-through', 'text-gray-400');

            // تحديث المجموع الكلي
            const tax = calculateTax(subtotalAfterDiscount);
            const newTotal = subtotalAfterDiscount + tax;
            finalTotalAmount.textContent = newTotal.toFixed(2) + ' د.ل';

            // تحديث عرض الضريبة
            updateTaxDisplay(tax);
        }
    }
});
</script>

@include('customer.partials.scripts')

</body>
</html>
