<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الطلب #{{ $order->order_id }} - Eat Hub</title>
    @include('customer.partials.head')
</head>
<body class="bg-gray-50 dark:bg-gray-900">

@include('customer.partials.header')

<main class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- رأس الصفحة -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h1 class="text-2xl font-bold text-gray-800 dark:text-white">
                    <i class="fas fa-receipt text-primary ml-2"></i>
                    تفاصيل الطلب #{{ $order->order_id }}
                </h1>
                <div class="flex items-center space-x-2 space-x-reverse">
                    <span class="px-3 py-1 rounded-full text-sm font-medium
                        @if($order->status === 'pending') bg-yellow-100 text-yellow-800
                        @elseif($order->status === 'preparing') bg-blue-100 text-blue-800
                        @elseif($order->status === 'completed') bg-green-100 text-green-800
                        @else bg-red-100 text-red-800 @endif">
                        @if($order->status === 'pending') قيد الانتظار
                        @elseif($order->status === 'preparing') جاري التحضير
                        @elseif($order->status === 'completed') مكتمل
                        @else ملغي @endif
                    </span>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                    <span class="text-gray-600 dark:text-gray-400">تاريخ الطلب:</span>
                    <span class="font-medium text-gray-800 dark:text-white">{{ $order->created_at->format('Y-m-d H:i') }}</span>
                </div>
                <div>
                    <span class="text-gray-600 dark:text-gray-400">نوع الطلب:</span>
                    <span class="font-medium text-gray-800 dark:text-white">
                        @if($order->order_type === 'delivery')
                            <i class="fas fa-truck text-primary ml-1"></i>
                            توصيل
                        @else
                            <i class="fas fa-store text-green-500 ml-1"></i>
                            استلام من المطعم
                        @endif
                    </span>
                </div>
                <div>
                    <span class="text-gray-600 dark:text-gray-400">طريقة الدفع:</span>
                    <span class="font-medium text-gray-800 dark:text-white">
                        @if($order->payment_method === 'cash')
                            <i class="fas fa-money-bill-wave text-green-500 ml-1"></i>
                            نقدي
                        @elseif($order->payment_method === 'card')
                            <i class="fas fa-credit-card text-blue-500 ml-1"></i>
                            بطاقة ائتمان
                        @else
                            <i class="fas fa-wallet text-purple-500 ml-1"></i>
                            محفظة إلكترونية
                        @endif
                    </span>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- عناصر الطلب -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">
                        <i class="fas fa-utensils text-primary ml-2"></i>
                        عناصر الطلب
                    </h2>

                    <div class="space-y-4">
                        @foreach($order->items as $item)
                        <div class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center ml-4">
                                    @if($item->menuItem->image_url)
                                        <img src="{{ $item->menuItem->image_url }}"
                                             alt="{{ $item->menuItem->name }}"
                                             class="w-full h-full object-cover rounded-lg">
                                    @else
                                        <i class="fas fa-utensils text-gray-400 text-xl"></i>
                                    @endif
                                </div>
                                <div>
                                    <h3 class="font-medium text-gray-800 dark:text-white">{{ $item->menuItem->name }}</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ number_format($item->price, 2) }} د.ل × {{ $item->quantity }}</p>
                                </div>
                            </div>
                            <div class="text-lg font-bold text-primary">
                                {{ number_format($item->price * $item->quantity, 2) }} د.ل
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- ملخص الطلب -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-6">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">ملخص الطلب</h2>

                    @php
                        $subtotal = $order->items->sum(function($item) { return $item->price * $item->quantity; });
                        $tax = $settingsHelper->calculateTax($subtotal);
                        $delivery = ($order->order_type === 'pickup') ? 0 : 10;
                    @endphp

                    <div class="space-y-3">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">المجموع الفرعي:</span>
                            <span class="font-medium text-gray-800 dark:text-white">{{ number_format($subtotal, 2) }} د.ل</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">الضريبة ({{ $taxRate }}%):</span>
                            <span class="font-medium text-gray-800 dark:text-white">{{ $settingsHelper->formatCurrency($tax) }}</span>
                        </div>

                        <div class="border-t border-gray-200 dark:border-gray-700 pt-3">
                            <div class="flex justify-between">
                                <span class="text-lg font-semibold text-gray-800 dark:text-white">المجموع الكلي:</span>
                                <span class="text-lg font-bold text-primary">{{ number_format($order->total_amount, 2) }} د.ل</span>
                            </div>
                        </div>
                    </div>
                </div>

                @if($order->order_type === 'delivery' && $order->delivery_address)
                <!-- معلومات التوصيل -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-6">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                        <i class="fas fa-map-marker-alt text-primary ml-2"></i>
                        عنوان التوصيل
                    </h2>
                    <p class="text-gray-600 dark:text-gray-400">{{ $order->delivery_address }}</p>
                </div>
                @endif

                @if($order->notes)
                <!-- الملاحظات -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-6">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                        <i class="fas fa-sticky-note text-primary ml-2"></i>
                        ملاحظات
                    </h2>
                    <p class="text-gray-600 dark:text-gray-400">{{ $order->notes }}</p>
                </div>
                @endif

                <!-- أزرار الإجراءات -->
                <div class="space-y-3">
                    <!-- زر الفاتورة متاح لجميع الطلبات -->
                    <a href="{{ route('customer.orders.invoice', $order->order_id) }}"
                       class="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-6 rounded-lg transition text-center block">
                        <i class="fas fa-download ml-2"></i>
                        عرض الفاتورة
                    </a>

                    @if(in_array($order->status, ['pending', 'preparing']))
                        <a href="{{ route('customer.orders.cancel.page', $order->order_id) }}"
                           class="w-full bg-red-500 hover:bg-red-600 text-white font-bold py-3 px-6 rounded-lg transition text-center block">
                            <i class="fas fa-times ml-2"></i>
                            إلغاء الطلب
                        </a>
                    @endif

                    <a href="{{ route('customer.orders') }}"
                       class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition text-center block">
                        <i class="fas fa-list ml-2"></i>
                        جميع الطلبات
                    </a>
                    <a href="{{ route('customer.menu') }}"
                       class="w-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-3 px-6 rounded-lg transition text-center block">
                        <i class="fas fa-utensils ml-2"></i>
                        طلب جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
</main>

@include('customer.partials.footer')
@include('customer.partials.scripts')

</body>
</html>
