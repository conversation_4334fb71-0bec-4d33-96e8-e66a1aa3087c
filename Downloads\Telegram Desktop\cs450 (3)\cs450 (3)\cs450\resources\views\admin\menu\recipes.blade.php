@extends('layouts.admin')

@section('title', 'إدارة الوصفات والتكاليف')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-800 dark:text-white">إدارة الوصفات والتكاليف</h1>
            <p class="text-gray-600 dark:text-gray-400">ربط عناصر المنيو بالمكونات وحساب التكاليف والأرباح</p>
        </div>
        <a href="{{ route('admin.menu') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-all">
            <i class="fas fa-arrow-right ml-2"></i>
            العودة للمنيو
        </a>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <div class="flex items-center">
                <div class="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
                    <i class="fas fa-utensils text-blue-600 dark:text-blue-400"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">إجمالي الأطباق</p>
                    <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ $menuItems->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <div class="flex items-center">
                <div class="p-3 bg-green-100 dark:bg-green-900 rounded-full">
                    <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">لها وصفات</p>
                    <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ $menuItems->filter(function($item) { return $item->hasRecipe(); })->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <div class="flex items-center">
                <div class="p-3 bg-orange-100 dark:bg-orange-900 rounded-full">
                    <i class="fas fa-exclamation-triangle text-orange-600 dark:text-orange-400"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">بدون وصفات</p>
                    <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ $menuItems->filter(function($item) { return !$item->hasRecipe(); })->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <div class="flex items-center">
                <div class="p-3 bg-purple-100 dark:bg-purple-900 rounded-full">
                    <i class="fas fa-chart-line text-purple-600 dark:text-purple-400"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">متوسط الربح</p>
                    <p class="text-2xl font-bold text-gray-800 dark:text-white">
                        {{ number_format($menuItems->filter(function($item) { return $item->hasRecipe(); })->avg(function($item) { return $item->calculateProfitMargin(); }), 1) }}%
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الأطباق -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-semibold text-gray-800 dark:text-white">تفاصيل الأطباق والتكاليف</h2>
        </div>

        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الطبق</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">السعر</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">التكلفة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الربح</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">هامش الربح</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الوصفة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($menuItems as $item)
                        @php
                            $costAnalysis = $item->getCostAnalysis();
                        @endphp
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-12 w-12">
                                        <img class="h-12 w-12 rounded-lg object-cover" src="{{ $item->image_url }}" alt="{{ $item->name }}">
                                    </div>
                                    <div class="mr-4">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $item->name }}</div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">{{ $item->category }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900 dark:text-white">{{ number_format($item->price, 2) }} د.ل</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($costAnalysis['has_recipe'])
                                    <div class="text-sm text-gray-900 dark:text-white">{{ number_format($costAnalysis['ingredients_cost'], 2) }} د.ل</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">{{ $costAnalysis['cost_percentage'] }}% من السعر</div>
                                @else
                                    <span class="text-sm text-gray-400">غير محسوبة</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($costAnalysis['has_recipe'])
                                    <div class="text-sm font-medium {{ $costAnalysis['profit'] > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                        {{ number_format($costAnalysis['profit'], 2) }} د.ل
                                    </div>
                                @else
                                    <span class="text-sm text-gray-400">غير محسوب</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($costAnalysis['has_recipe'])
                                    <div class="flex items-center">
                                        <div class="text-sm font-medium {{ $costAnalysis['profit_margin'] > 50 ? 'text-green-600 dark:text-green-400' : ($costAnalysis['profit_margin'] > 25 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400') }}">
                                            {{ $costAnalysis['profit_margin'] }}%
                                        </div>
                                        <div class="mr-2 w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                            <div class="h-2 rounded-full {{ $costAnalysis['profit_margin'] > 50 ? 'bg-green-500' : ($costAnalysis['profit_margin'] > 25 ? 'bg-yellow-500' : 'bg-red-500') }}" 
                                                 style="width: {{ min($costAnalysis['profit_margin'], 100) }}%"></div>
                                        </div>
                                    </div>
                                @else
                                    <span class="text-sm text-gray-400">غير محسوب</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($costAnalysis['has_recipe'])
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                        <i class="fas fa-check-circle ml-1"></i>
                                        {{ $item->recipe->count() }} مكونات
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
                                        <i class="fas fa-exclamation-triangle ml-1"></i>
                                        لا توجد وصفة
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="{{ route('admin.menu.edit', $item->item_id) }}" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ route('admin.menu.show', $item->item_id) }}" class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if(!$costAnalysis['has_recipe'])
                                        <button onclick="showAddRecipeModal({{ $item->item_id }}, '{{ $item->name }}')" class="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300">
                                            <i class="fas fa-plus-circle"></i>
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <!-- تحليل الربحية -->
    <div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- أكثر الأطباق ربحية -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">أكثر الأطباق ربحية</h3>
            <div class="space-y-3">
                @foreach($menuItems->filter(function($item) { return $item->hasRecipe(); })->sortByDesc(function($item) { return $item->calculateProfitMargin(); })->take(5) as $item)
                    @php $analysis = $item->getCostAnalysis(); @endphp
                    <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                        <div>
                            <div class="font-medium text-gray-800 dark:text-white">{{ $item->name }}</div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ number_format($analysis['profit'], 2) }} د.ل ربح</div>
                        </div>
                        <div class="text-green-600 dark:text-green-400 font-bold">{{ $analysis['profit_margin'] }}%</div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- أطباق تحتاج مراجعة -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">أطباق تحتاج مراجعة</h3>
            <div class="space-y-3">
                @foreach($menuItems->filter(function($item) { return !$item->hasRecipe() || $item->calculateProfitMargin() < 25; })->take(5) as $item)
                    @php $analysis = $item->getCostAnalysis(); @endphp
                    <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                        <div>
                            <div class="font-medium text-gray-800 dark:text-white">{{ $item->name }}</div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                @if(!$analysis['has_recipe'])
                                    لا توجد وصفة
                                @else
                                    هامش ربح منخفض
                                @endif
                            </div>
                        </div>
                        <div class="{{ !$analysis['has_recipe'] ? 'text-red-600 dark:text-red-400' : 'text-yellow-600 dark:text-yellow-400' }} font-bold">
                            @if($analysis['has_recipe'])
                                {{ $analysis['profit_margin'] }}%
                            @else
                                <i class="fas fa-exclamation-triangle"></i>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>

<!-- Modal لإضافة وصفة سريعة -->
<div id="addRecipeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">إضافة وصفة سريعة</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">لإضافة وصفة كاملة، يرجى استخدام صفحة تعديل الطبق.</p>
                <div class="flex justify-end space-x-3">
                    <button onclick="closeAddRecipeModal()" class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200">
                        إلغاء
                    </button>
                    <button onclick="goToEditPage()" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                        تعديل الطبق
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    let currentItemId = null;

    function showAddRecipeModal(itemId, itemName) {
        currentItemId = itemId;
        document.getElementById('addRecipeModal').classList.remove('hidden');
    }

    function closeAddRecipeModal() {
        document.getElementById('addRecipeModal').classList.add('hidden');
        currentItemId = null;
    }

    function goToEditPage() {
        if (currentItemId) {
            window.location.href = `/admin/menu/${currentItemId}/edit`;
        }
    }
</script>
@endsection
