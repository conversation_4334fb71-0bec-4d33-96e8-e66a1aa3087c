<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Offer;
use App\Models\MenuItem;

class OfferSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // عرض خصم العائلة (حجز)
        $familyOffer = Offer::firstOrCreate(
            ['slug' => 'family-discount'],
            [
            'title' => 'خصم 25% على طلبات الوجبات العائلية',
            'slug' => 'family-discount',
            'description' => 'استمتع بخصم 25% على جميع الوجبات العائلية عند طلب وجبة لأكثر من 4 أشخاص. هذا العرض مثالي للعائلات الكبيرة والتجمعات الودية.',
            'type' => 'حجز',
            'discount_percentage' => 25.00,
            'original_price' => 200.00,
            'discounted_price' => 150.00,
            'start_date' => now()->subDays(5),
            'end_date' => now()->addDays(30),
            'is_active' => true,
            'terms_conditions' => ['ساري على الوجبات العائلية فقط', 'الحد الأدنى 4 أشخاص', 'لا يشمل المشروبات', 'غير قابل للجمع مع عروض أخرى', 'يتطلب حجز مسبق'],
            'max_uses' => 100,
            'min_order_amount' => 150.00
            ]
        );

        // عرض الأطباق الجديدة (طعام)
        $newDishesOffer = Offer::firstOrCreate(
            ['slug' => 'new-dishes'],
            [
            'title' => 'تذوق أطباقنا الجديدة',
            'slug' => 'new-dishes',
            'description' => 'اكتشف مجموعة جديدة من الأطباق الشهية المضافة حديثاً إلى قائمة طعامنا. أطباق مبتكرة بنكهات عالمية وتحضير احترافي.',
            'type' => 'طعام',
            'discount_percentage' => 15.00,
            'start_date' => now()->subDays(10),
            'end_date' => now()->addDays(60),
            'is_active' => true,
            'terms_conditions' => ['ينطبق على الأطباق الجديدة فقط', 'متاح للطلب المباشر والتوصيل', 'لا يشمل المشروبات والحلويات'],
            'max_uses' => 200
            ]
        );

        // عرض ليالي الموسيقى (حجز)
        Offer::firstOrCreate(
            ['slug' => 'music-nights'],
            [
            'title' => 'ليالي الموسيقى الحية',
            'slug' => 'music-nights',
            'description' => 'انضم إلينا كل خميس لليالي الموسيقى الحية مع تشكيلة من أشهى المأكولات. أمسيات موسيقية رائعة في أجواء مميزة.',
            'type' => 'حجز',
            'discount_percentage' => 20.00,
            'original_price' => 120.00,
            'discounted_price' => 96.00,
            'start_date' => now(),
            'end_date' => now()->addDays(90),
            'is_active' => true,
            'terms_conditions' => ['كل يوم خميس من الساعة 7 مساءً', 'يتطلب حجز مسبق', 'رسوم دخول 10 د.ل للشخص الواحد', 'الحد الأدنى للحجز شخصين'],
            'max_uses' => 50,
            'min_order_amount' => 100.00
            ]
        );

        // عرض وجبة الإفطار (طعام)
        $breakfastOffer = Offer::firstOrCreate(
            ['slug' => 'breakfast-special'],
            [
            'title' => 'عرض وجبة الإفطار الخاص',
            'slug' => 'breakfast-special',
            'description' => 'ابدأ يومك بوجبة إفطار شهية ومتكاملة. عرض خاص على جميع وجبات الإفطار من الساعة 7 صباحاً حتى 11 صباحاً.',
            'type' => 'طعام',
            'discount_amount' => 15.00,
            'start_date' => now()->subDays(3),
            'end_date' => now()->addDays(45),
            'is_active' => true,
            'terms_conditions' => ['متاح من الساعة 7 صباحاً حتى 11 صباحاً', 'ينطبق على وجبات الإفطار فقط', 'يشمل المشروبات الساخنة']
            ]
        );

        // عرض منتهي الصلاحية للاختبار
        Offer::firstOrCreate(
            ['slug' => 'summer-expired'],
            [
            'title' => 'عرض الصيف المنتهي',
            'slug' => 'summer-expired',
            'description' => 'عرض صيفي منتهي الصلاحية للاختبار.',
            'type' => 'طعام',
            'discount_percentage' => 30.00,
            'start_date' => now()->subDays(60),
            'end_date' => now()->subDays(10),
            'is_active' => false,
            'terms_conditions' => ['عرض منتهي الصلاحية'],
            'max_uses' => 75
            ]
        );

        // عروض "اشتري واحصل" الجديدة

        // عرض: اشتري 1 واحصل على 1 مجاناً
        Offer::firstOrCreate(
            ['slug' => 'buy-1-get-1-free'],
            [
            'title' => '🔥 اشتري 1 واحصل على 1 مجاناً',
            'slug' => 'buy-1-get-1-free',
            'description' => 'عرض رائع! اطلب أي طبق واحصل على طبق آخر مجاناً من نفس الفئة',
            'type' => 'طعام',
            'offer_type' => 'buy_get',
            'buy_quantity' => 1,
            'get_quantity' => 1,
            'min_order_amount' => 20.00,
            'start_date' => now(),
            'end_date' => now()->addDays(30),
            'is_active' => true,
            'terms_conditions' => ['يطبق على جميع الأطباق', 'العنصر المجاني من نفس قيمة العنصر المدفوع أو أقل'],
            'max_uses' => 100
            ]
        );

        // عرض: اشتري 2 واحصل على 1 مجاناً
        Offer::firstOrCreate(
            ['slug' => 'buy-2-get-1-free'],
            [
            'title' => '🎉 اشتري 2 واحصل على 1 مجاناً',
            'slug' => 'buy-2-get-1-free',
            'description' => 'وفر أكثر! اطلب طبقين واحصل على الثالث مجاناً',
            'type' => 'طعام',
            'offer_type' => 'buy_get',
            'buy_quantity' => 2,
            'get_quantity' => 1,
            'min_order_amount' => 40.00,
            'start_date' => now(),
            'end_date' => now()->addDays(25),
            'is_active' => true,
            'terms_conditions' => ['يطبق على جميع الأطباق', 'العنصر المجاني من أقل قيمة في الطلب'],
            'max_uses' => 75
            ]
        );

        // عرض: اشتري 3 واحصل على 2 مجاناً
        Offer::firstOrCreate(
            ['slug' => 'buy-3-get-2-free'],
            [
            'title' => '💥 اشتري 3 واحصل على 2 مجاناً',
            'slug' => 'buy-3-get-2-free',
            'description' => 'عرض استثنائي للمجموعات! اطلب 3 أطباق واحصل على 2 إضافية مجاناً',
            'type' => 'طعام',
            'offer_type' => 'buy_get',
            'buy_quantity' => 3,
            'get_quantity' => 2,
            'min_order_amount' => 60.00,
            'start_date' => now(),
            'end_date' => now()->addDays(20),
            'is_active' => true,
            'terms_conditions' => ['مثالي للمجموعات', 'العناصر المجانية من أقل قيمة في الطلب'],
            'max_uses' => 50
            ]
        );

        // ربط بعض العروض بعناصر القائمة
        $menuItems = MenuItem::take(5)->get();
        if ($menuItems->count() > 0) {
            $newDishesOffer->menuItems()->attach($menuItems->pluck('item_id')->toArray());
            $breakfastOffer->menuItems()->attach($menuItems->take(3)->pluck('item_id')->toArray());
        }
    }
}
