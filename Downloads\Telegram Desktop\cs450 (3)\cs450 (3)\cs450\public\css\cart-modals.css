/* تحسينات نوافذ السلة */

/* تأثيرات النوافذ المنبثقة */
.modal-backdrop {
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    animation: slideUp 0.3s ease-out;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* تأثيرات الأزرار */
.delete-btn {
    position: relative;
    overflow: hidden;
}

.delete-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.delete-btn:hover::before {
    left: 100%;
}

/* تأثيرات التحميل */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .modal-content {
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    }
}

/* تأثيرات الهوفر للعناصر */
.cart-item {
    transition: all 0.3s ease;
}

.cart-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* تحسينات الأزرار */
.btn-primary {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.btn-secondary {
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* تأثيرات النصوص */
.text-animate {
    animation: textGlow 2s ease-in-out infinite alternate;
}

@keyframes textGlow {
    from {
        text-shadow: 0 0 5px rgba(239, 68, 68, 0.5);
    }
    to {
        text-shadow: 0 0 10px rgba(239, 68, 68, 0.8);
    }
}

/* تحسينات الاستجابة */
@media (max-width: 640px) {
    .modal-content {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }
    
    .modal-buttons {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .modal-buttons button {
        width: 100%;
    }
}

/* تأثيرات الأيقونات */
.icon-bounce {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* تحسينات الإشعارات */
.notification-success {
    background: linear-gradient(135deg, #10b981, #059669);
    animation: slideInRight 0.5s ease-out;
}

.notification-error {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* تأثيرات التركيز */
.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.3);
}

/* تحسينات الصور */
.item-image {
    transition: all 0.3s ease;
}

.item-image:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
}

/* تأثيرات الحدود */
.border-animate {
    position: relative;
    overflow: hidden;
}

.border-animate::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #ef4444, transparent);
    animation: borderSlide 2s linear infinite;
}

@keyframes borderSlide {
    0% { left: -100%; }
    100% { left: 100%; }
}
