@extends('employee.layouts.app')

@section('title', 'إدارة الطلبات')

@section('content')
<div id="orders-page" class="page fade-in">
    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="mb-4 bg-green-100 dark:bg-green-900/30 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-300 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline">{{ session('success') }}</span>
        </div>
    @endif

    @if(session('error'))
        <div class="mb-4 bg-red-100 dark:bg-red-900/30 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline">{{ session('error') }}</span>
        </div>
    @endif

    <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">إدارة الطلبات</h2>
            @if(isset($stats))
                <div class="flex items-center space-x-4 space-x-reverse text-sm text-gray-600 dark:text-gray-400">
                    <span>المجموع: <strong class="text-gray-800 dark:text-white">{{ $stats['total'] }}</strong></span>
                    <span>في الانتظار: <strong class="text-yellow-600">{{ $stats['pending'] }}</strong></span>
                    <span>قيد التحضير: <strong class="text-blue-600">{{ $stats['preparing'] }}</strong></span>
                    <span>مكتمل: <strong class="text-green-600">{{ $stats['completed'] }}</strong></span>
                    <span>ملغي: <strong class="text-red-600">{{ $stats['canceled'] }}</strong></span>
                </div>
            @endif
        </div>
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 sm:space-x-reverse">
            <form action="{{ route('employee.orders') }}" method="GET" class="relative">
                @if(request('status'))
                    <input type="hidden" name="status" value="{{ request('status') }}">
                @endif
                <input type="text" name="search" placeholder="بحث برقم الطلب أو اسم العميل..." value="{{ request('search') }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary text-base">
                <button type="submit" class="absolute left-3 top-2.5 text-gray-500 dark:text-gray-400">
                    <i class="fas fa-search"></i>
                </button>
            </form>
            <a href="{{ route('employee.orders.create') }}" class="bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
                <i class="fas fa-plus ml-2"></i>
                <span>إنشاء طلب</span>
            </a>
        </div>
    </div>

    @if(session('success'))
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline">{{ session('success') }}</span>
    </div>
    @endif

    @if(session('error'))
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline">{{ session('error') }}</span>
    </div>
    @endif

    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 card-hover">
            <div class="flex justify-between items-center">
                <div>
                    <h5 class="text-lg font-semibold text-gray-800 dark:text-white">إجمالي الطلبات</h5>
                    <p class="text-3xl font-bold text-primary">{{ $orderStats['total'] }}</p>
                </div>
                <div class="text-4xl text-primary">
                    <i class="fas fa-shopping-cart"></i>
                </div>
            </div>
        </div>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 card-hover">
            <div class="flex justify-between items-center">
                <div>
                    <h5 class="text-lg font-semibold text-gray-800 dark:text-white">طلبات قيد الانتظار</h5>
                    <p class="text-3xl font-bold text-yellow-500">{{ $orderStats['pending'] }}</p>
                </div>
                <div class="text-4xl text-yellow-500">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 card-hover">
            <div class="flex justify-between items-center">
                <div>
                    <h5 class="text-lg font-semibold text-gray-800 dark:text-white">طلبات قيد التحضير</h5>
                    <p class="text-3xl font-bold text-blue-500">{{ $orderStats['preparing'] }}</p>
                </div>
                <div class="text-4xl text-blue-500">
                    <i class="fas fa-utensils"></i>
                </div>
            </div>
        </div>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 card-hover">
            <div class="flex justify-between items-center">
                <div>
                    <h5 class="text-lg font-semibold text-gray-800 dark:text-white">طلبات مكتملة</h5>
                    <p class="text-3xl font-bold text-green-500">{{ $orderStats['completed'] }}</p>
                </div>
                <div class="text-4xl text-green-500">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="flex flex-wrap border-b border-gray-200 dark:border-gray-700">
            <a href="{{ route('employee.orders') }}" class="px-6 py-3 {{ !request('status') ? 'border-b-2 border-primary text-primary' : 'text-gray-600 dark:text-gray-300 hover:text-primary' }} font-medium">
                <i class="fas fa-list-ul ml-1"></i>جميع الطلبات
            </a>
            <a href="{{ route('employee.orders', ['status' => 'pending']) }}" class="px-6 py-3 {{ request('status') == 'pending' ? 'border-b-2 border-primary text-primary' : 'text-gray-600 dark:text-gray-300 hover:text-primary' }} font-medium">
                <i class="fas fa-clock ml-1"></i>قيد الانتظار
            </a>
            <a href="{{ route('employee.orders', ['status' => 'preparing']) }}" class="px-6 py-3 {{ request('status') == 'preparing' ? 'border-b-2 border-primary text-primary' : 'text-gray-600 dark:text-gray-300 hover:text-primary' }} font-medium">
                <i class="fas fa-utensils ml-1"></i>قيد التحضير
            </a>
            <a href="{{ route('employee.orders', ['status' => 'completed']) }}" class="px-6 py-3 {{ request('status') == 'completed' ? 'border-b-2 border-primary text-primary' : 'text-gray-600 dark:text-gray-300 hover:text-primary' }} font-medium">
                <i class="fas fa-check-circle ml-1"></i>مكتمل
            </a>
            <a href="{{ route('employee.orders', ['status' => 'canceled']) }}" class="px-6 py-3 {{ request('status') == 'canceled' ? 'border-b-2 border-primary text-primary' : 'text-gray-600 dark:text-gray-300 hover:text-primary' }} font-medium">
                <i class="fas fa-times-circle ml-1"></i>ملغي
            </a>
        </div>

        <div class="p-6">
            <!-- Advanced Filter Section -->
            <div class="mb-6 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">تصفية الطلبات</h3>
                    <button id="toggleFilters" class="text-primary hover:text-primary/80 text-sm">
                        <i class="fas fa-filter ml-1"></i>إظهار/إخفاء الفلاتر
                    </button>
                </div>

                <form id="filterForm" action="{{ route('employee.orders') }}" method="GET" class="space-y-4">
                    <!-- الصف الأول: البحث والحالة -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البحث</label>
                            <div class="relative">
                                <input type="text" name="search" placeholder="بحث برقم الطلب أو اسم العميل..."
                                       value="{{ request('search') }}"
                                       class="w-full px-4 py-2 pl-10 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                                <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحالة</label>
                            <select name="status" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                                <option value="">جميع الحالات</option>
                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>في الانتظار</option>
                                <option value="preparing" {{ request('status') == 'preparing' ? 'selected' : '' }}>قيد التحضير</option>
                                <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>مكتمل</option>
                                <option value="canceled" {{ request('status') == 'canceled' ? 'selected' : '' }}>ملغي</option>
                            </select>
                        </div>
                    </div>

                    <!-- الصف الثاني: التاريخ وطريقة الدفع -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
                            <input type="date" name="date_from" value="{{ request('date_from') }}"
                                   class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
                            <input type="date" name="date_to" value="{{ request('date_to') }}"
                                   class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">طريقة الدفع</label>
                            <select name="payment_method" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                                <option value="">جميع طرق الدفع</option>
                                <option value="cash" {{ request('payment_method') == 'cash' ? 'selected' : '' }}>نقدي</option>
                                <option value="card" {{ request('payment_method') == 'card' ? 'selected' : '' }}>بطاقة</option>
                                <option value="credit_card" {{ request('payment_method') == 'credit_card' ? 'selected' : '' }}>بطاقة ائتمان</option>
                                <option value="wallet" {{ request('payment_method') == 'wallet' ? 'selected' : '' }}>محفظة رقمية</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">حالة الدفع</label>
                            <select name="payment_status" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                                <option value="">جميع حالات الدفع</option>
                                <option value="paid" {{ request('payment_status') == 'paid' ? 'selected' : '' }}>مدفوع</option>
                                <option value="unpaid" {{ request('payment_status') == 'unpaid' ? 'selected' : '' }}>غير مدفوع</option>
                            </select>
                        </div>
                        <div class="flex items-end space-x-2 space-x-reverse">
                            <button type="submit" class="flex-1 bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-md transition-colors">
                                <i class="fas fa-search ml-2"></i>تصفية
                            </button>
                            <a href="{{ route('employee.orders') }}" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-center transition-colors">
                                <i class="fas fa-times ml-2"></i>مسح
                            </a>
                        </div>

                        <!-- رسالة تذكيرية -->
                        <div id="filterHint" class="hidden mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
                            <p class="text-sm text-blue-700 dark:text-blue-300">
                                <i class="fas fa-info-circle ml-1"></i>
                                تم تغيير الفلاتر. اضغط على "تصفية" لتطبيق التغييرات.
                            </p>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Active Filters Display -->
            @if(request()->hasAny(['search', 'status', 'date_from', 'date_to', 'payment_method', 'payment_status']))
                <div class="mb-4 flex flex-wrap items-center gap-2">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">الفلاتر النشطة:</span>

                    @if(request('search'))
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                            البحث: {{ request('search') }}
                            <a href="{{ request()->fullUrlWithQuery(['search' => null]) }}" class="ml-1 text-blue-600 hover:text-blue-800">
                                <i class="fas fa-times"></i>
                            </a>
                        </span>
                    @endif

                    @if(request('status'))
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                            الحالة:
                            @switch(request('status'))
                                @case('pending') في الانتظار @break
                                @case('preparing') قيد التحضير @break
                                @case('completed') مكتمل @break
                                @case('canceled') ملغي @break
                            @endswitch
                            <a href="{{ request()->fullUrlWithQuery(['status' => null]) }}" class="ml-1 text-green-600 hover:text-green-800">
                                <i class="fas fa-times"></i>
                            </a>
                        </span>
                    @endif

                    @if(request('date_from'))
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300">
                            من تاريخ: {{ request('date_from') }}
                            <a href="{{ request()->fullUrlWithQuery(['date_from' => null]) }}" class="ml-1 text-purple-600 hover:text-purple-800">
                                <i class="fas fa-times"></i>
                            </a>
                        </span>
                    @endif

                    @if(request('date_to'))
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300">
                            إلى تاريخ: {{ request('date_to') }}
                            <a href="{{ request()->fullUrlWithQuery(['date_to' => null]) }}" class="ml-1 text-purple-600 hover:text-purple-800">
                                <i class="fas fa-times"></i>
                            </a>
                        </span>
                    @endif

                    @if(request('payment_method'))
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                            طريقة الدفع:
                            @switch(request('payment_method'))
                                @case('cash') نقدي @break
                                @case('card') بطاقة @break
                                @case('credit_card') بطاقة ائتمان @break
                                @case('wallet') محفظة رقمية @break
                            @endswitch
                            <a href="{{ request()->fullUrlWithQuery(['payment_method' => null]) }}" class="ml-1 text-yellow-600 hover:text-yellow-800">
                                <i class="fas fa-times"></i>
                            </a>
                        </span>
                    @endif

                    @if(request('payment_status'))
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300">
                            حالة الدفع:
                            @switch(request('payment_status'))
                                @case('paid') مدفوع @break
                                @case('unpaid') غير مدفوع @break
                            @endswitch
                            <a href="{{ request()->fullUrlWithQuery(['payment_status' => null]) }}" class="ml-1 text-orange-600 hover:text-orange-800">
                                <i class="fas fa-times"></i>
                            </a>
                        </span>
                    @endif
                </div>
            @endif

            <div class="mb-4 flex flex-col md:flex-row md:items-center md:justify-between">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2 md:mb-0">سجل الطلبات</h3>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead>
                        <tr class="bg-gray-50 dark:bg-gray-700">
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                رقم الطلب
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                العميل
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                المنتجات
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                المبلغ
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                طريقة الدفع
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                الحالة
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                تاريخ الطلب
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                الإجراءات
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($orders as $order)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                                <span class="bg-primary/10 text-primary px-2 py-1 rounded-md font-bold">#{{ $order->order_id }}</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                                <div class="flex items-center">
                                    @if($order->user && !empty($order->user->first_name) && !empty($order->user->last_name))
                                        <div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center text-primary font-bold ml-2">
                                            {{ substr($order->user->first_name, 0, 1) }}
                                        </div>
                                        <span>{{ $order->user->first_name }} {{ $order->user->last_name }}</span>
                                    @elseif($order->user && !empty($order->user->name))
                                        <div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center text-primary font-bold ml-2">
                                            {{ substr($order->user->name, 0, 1) }}
                                        </div>
                                        <span>{{ $order->user->name }}</span>
                                    @else
                                        <div class="w-8 h-8 rounded-full bg-gray-400 flex items-center justify-center text-white font-bold ml-2">
                                            ?
                                        </div>
                                        <span class="text-gray-500 dark:text-gray-400">غير محدد</span>
                                    @endif
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                                @if($order->items->count() > 0)
                                    <div class="flex flex-col">
                                        @foreach($order->items->take(2) as $item)
                                            <div class="mb-1">
                                                <span class="inline-flex items-center">
                                                    <i class="fas fa-utensils text-xs text-primary ml-1"></i>
                                                    {{ $item->menuItem->name ?? 'غير معروف' }}
                                                </span>
                                                @if($item->excluded_ingredients && count($item->excluded_ingredients) > 0)
                                                <div class="text-xs text-red-600 dark:text-red-400 mr-4 flex items-center">
                                                    <i class="fas fa-ban ml-1"></i>
                                                    <span class="font-medium">بدون:</span>
                                                    @php
                                                        $excludedNames = \App\Models\Ingredient::whereIn('ingredient_id', $item->excluded_ingredients)->pluck('name')->take(2)->toArray();
                                                    @endphp
                                                    <span class="mr-1">{{ implode('، ', $excludedNames) }}{{ count($item->excluded_ingredients) > 2 ? '...' : '' }}</span>
                                                </div>
                                                @endif
                                            </div>
                                                <span class="text-xs text-gray-500 dark:text-gray-400 mr-1">({{ $item->quantity }})</span>
                                            </span>
                                        @endforeach
                                        @if($order->items->count() > 2)
                                            <span class="text-xs text-primary mt-1">+ {{ $order->items->count() - 2 }} عناصر أخرى</span>
                                        @endif
                                    </div>
                                @else
                                    <span class="text-gray-400 dark:text-gray-500">لا توجد عناصر</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                                <span class="text-primary font-bold">{{ number_format($order->total_amount, 2) }}</span> <span class="text-xs text-gray-500 dark:text-gray-400">د.ل</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                                @if(isset($order->payments) && $order->payments->count() > 0)
                                    @if($order->payments->first()->payment_method == 'cash')
                                        <span class="inline-flex items-center">
                                            <i class="fas fa-money-bill-wave text-green-500 ml-1"></i>
                                            <span>نقدي</span>
                                        </span>
                                    @else
                                        <span class="inline-flex items-center">
                                            <i class="fas fa-credit-card text-blue-500 ml-1"></i>
                                            <span>بطاقة ائتمان</span>
                                        </span>
                                    @endif
                                @else
                                    <span class="inline-flex items-center text-gray-400 dark:text-gray-500">
                                        <i class="fas fa-clock ml-1"></i>
                                        <span>غير مدفوع</span>
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                @if($order->status == 'pending')
                                    <span class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                                        <i class="fas fa-clock ml-1"></i>
                                        <span>قيد الانتظار</span>
                                    </span>
                                @elseif($order->status == 'preparing')
                                    <span class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                        <i class="fas fa-utensils ml-1"></i>
                                        <span>قيد التحضير</span>
                                    </span>
                                @elseif($order->status == 'completed')
                                    <span class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                        <i class="fas fa-check-circle ml-1"></i>
                                        <span>مكتمل</span>
                                    </span>
                                @elseif($order->status == 'canceled')
                                    <span class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-full bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                                        <i class="fas fa-times-circle ml-1"></i>
                                        <span>ملغي</span>
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                                {{ $order->created_at->format('Y-m-d H:i') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    @if($order->status == 'pending')
                                        <form action="{{ route('employee.orders.update-status', $order->order_id) }}" method="POST" class="inline">
                                            @csrf
                                            @method('PUT')
                                            <input type="hidden" name="status" value="preparing">
                                            <button type="submit" class="p-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-900/50">
                                                <i class="fas fa-utensils"></i>
                                            </button>
                                        </form>
                                    @elseif($order->status == 'preparing')
                                        <form action="{{ route('employee.orders.update-status', $order->order_id) }}" method="POST" class="inline">
                                            @csrf
                                            @method('PUT')
                                            <input type="hidden" name="status" value="completed">
                                            <button type="submit" class="p-1 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-900/50">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                    @endif
                                    <a href="{{ route('employee.orders.show', $order->order_id) }}" class="p-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-900/50">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if($order->status == 'pending' || $order->status == 'preparing')
                                    <a href="{{ route('employee.orders.edit', $order->order_id) }}" class="p-1 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 hover:bg-yellow-200 dark:hover:bg-yellow-900/50">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                                لا توجد طلبات متاحة
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-700 dark:text-gray-300">
                    @if($orders->total() > 0)
                        عرض <span class="font-medium">{{ $orders->firstItem() }}</span> إلى <span class="font-medium">{{ $orders->lastItem() }}</span> من <span class="font-medium">{{ $orders->total() }}</span> طلب
                    @else
                        لا توجد طلبات
                    @endif
                </div>
                <div class="flex items-center space-x-2 space-x-reverse">
                    {{ $orders->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // عناصر النموذج
    const filterForm = document.getElementById('filterForm');
    const toggleFiltersBtn = document.getElementById('toggleFilters');
    const dateFromInput = document.querySelector('input[name="date_from"]');
    const dateToInput = document.querySelector('input[name="date_to"]');
    const statusSelect = document.querySelector('select[name="status"]');
    const paymentMethodSelect = document.querySelector('select[name="payment_method"]');
    const paymentStatusSelect = document.querySelector('select[name="payment_status"]');
    const searchInput = document.querySelector('input[name="search"]');

    // إظهار/إخفاء الفلاتر
    if (toggleFiltersBtn) {
        toggleFiltersBtn.addEventListener('click', function() {
            const filterSection = filterForm.parentElement;
            const isHidden = filterForm.style.display === 'none';

            if (isHidden) {
                filterForm.style.display = 'block';
                this.innerHTML = '<i class="fas fa-filter ml-1"></i>إخفاء الفلاتر';
            } else {
                filterForm.style.display = 'none';
                this.innerHTML = '<i class="fas fa-filter ml-1"></i>إظهار الفلاتر';
            }
        });
    }

    // دالة لإظهار رسالة التذكير
    function showFilterHint() {
        const filterHint = document.getElementById('filterHint');
        if (filterHint) {
            filterHint.classList.remove('hidden');
        }
    }

    // دالة لإخفاء رسالة التذكير
    function hideFilterHint() {
        const filterHint = document.getElementById('filterHint');
        if (filterHint) {
            filterHint.classList.add('hidden');
        }
    }

    // إزالة التصفية التلقائية - الآن تحدث فقط عند الضغط على زر التصفية
    // يمكن إضافة مؤشرات بصرية عند تغيير القيم
    if (dateFromInput) {
        dateFromInput.addEventListener('change', function() {
            console.log('Date from filter changed:', this.value);
            this.style.borderColor = '#3B82F6';
            showFilterHint();
        });
    }

    if (dateToInput) {
        dateToInput.addEventListener('change', function() {
            console.log('Date to filter changed:', this.value);
            this.style.borderColor = '#3B82F6';
            showFilterHint();
        });
    }

    if (statusSelect) {
        statusSelect.addEventListener('change', function() {
            console.log('Status filter changed:', this.value);
            this.style.borderColor = '#3B82F6';
            showFilterHint();
        });
    }

    if (paymentMethodSelect) {
        paymentMethodSelect.addEventListener('change', function() {
            console.log('Payment method filter changed:', this.value);
            this.style.borderColor = '#3B82F6';
            showFilterHint();
        });
    }

    if (paymentStatusSelect) {
        paymentStatusSelect.addEventListener('change', function() {
            console.log('Payment status filter changed:', this.value);
            this.style.borderColor = '#3B82F6';
            showFilterHint();
        });
    }

    // البحث مع Enter أو عند فقدان التركيز
    if (searchInput) {
        // البحث عند الضغط على Enter
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                filterForm.submit();
            }
        });

        // إضافة مؤشر بصري عند الكتابة
        searchInput.addEventListener('input', function() {
            this.style.borderColor = '#3B82F6';
            if (this.value.length > 0) {
                showFilterHint();
            }
        });

        // البحث عند فقدان التركيز (blur) إذا كان هناك نص
        searchInput.addEventListener('blur', function() {
            if (this.value.length >= 3 || this.value.length === 0) {
                // يمكن تفعيل هذا إذا أردت البحث عند فقدان التركيز
                // filterForm.submit();
            }
        });
    }

    // تحسين UX - إضافة loading state وإعادة تعيين الألوان
    if (filterForm) {
        filterForm.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري التصفية...';
                submitBtn.disabled = true;
            }

            // إعادة تعيين ألوان الحدود وإخفاء الرسالة
            const inputs = this.querySelectorAll('input, select');
            inputs.forEach(input => {
                input.style.borderColor = '';
            });
            hideFilterHint();
        });
    }

    // إضافة تأثيرات بصرية للفلاتر النشطة
    const activeFilters = document.querySelectorAll('.inline-flex.items-center.px-3.py-1.rounded-full');
    activeFilters.forEach(filter => {
        filter.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
        });

        filter.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });

    console.log('Orders filter system initialized');
});
</script>
@endpush
