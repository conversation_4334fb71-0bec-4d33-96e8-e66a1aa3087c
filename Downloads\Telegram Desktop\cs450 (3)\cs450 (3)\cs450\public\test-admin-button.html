<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر الأدمن المباشر</title>
    
    <!-- تطبيق الوضع المظلم فوراً لمنع الوميض -->
    <script>
        (function() {
            const savedTheme = localStorage.getItem('darkMode');
            console.log('🔍 الثيم المحفوظ:', savedTheme);
            
            if (savedTheme === 'true' || savedTheme === 'enabled') {
                document.documentElement.classList.add('dark');
                console.log('🌙 تم تطبيق الوضع المظلم من الإعدادات المحفوظة');
            } else if (savedTheme === 'false' || savedTheme === 'disabled') {
                document.documentElement.classList.remove('dark');
                console.log('☀️ تم تطبيق الوضع الفاتح من الإعدادات المحفوظة');
            } else {
                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                    document.documentElement.classList.add('dark');
                    localStorage.setItem('darkMode', 'true');
                    console.log('🌙 تم تطبيق الوضع المظلم من إعدادات النظام');
                } else {
                    document.documentElement.classList.remove('dark');
                    localStorage.setItem('darkMode', 'false');
                    console.log('☀️ تم تطبيق الوضع الفاتح كافتراضي');
                }
            }
        })();
    </script>

    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class'
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100 dark:bg-gray-900 min-h-screen">
    <!-- محاكاة header الأدمن الحقيقي -->
    <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 z-40 relative">
        <div class="px-6 py-4 flex justify-between items-center">
            <!-- الجانب الأيسر -->
            <div class="flex items-center space-x-4 space-x-reverse">
                <h1 class="text-xl font-bold text-gray-900 dark:text-white">
                    🧪 اختبار زر الأدمن المباشر
                </h1>
            </div>
            
            <!-- الجانب الأيمن - نفس الكود من header الأصلي -->
            <div class="flex items-center space-x-4 space-x-reverse">
                <!-- زر تبديل الثيم - نسخة طبق الأصل -->
                <button id="darkModeToggle" data-theme-toggle class="theme-toggle p-3 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group" title="تبديل الثيم">
                    <i id="darkModeIcon" class="theme-icon fas fa-moon group-hover:rotate-180 transition-transform duration-500"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- المحتوى -->
    <main class="p-8">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                    اختبار زر الوضع المظلم - نسخة الأدمن
                </h2>
                
                <!-- معلومات مباشرة -->
                <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800 mb-6">
                    <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-3">
                        <i class="fas fa-info-circle mr-2"></i>
                        حالة النظام المباشرة
                    </h3>
                    <div id="liveStatus" class="text-blue-700 dark:text-blue-300 text-sm font-mono">
                        جاري التحميل...
                    </div>
                </div>
                
                <!-- أزرار الاختبار -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <button onclick="testDirectToggle()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-toggle-on mr-2"></i>
                        اختبار مباشر
                    </button>
                    
                    <button onclick="emergencyDarkModeFix()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-wrench mr-2"></i>
                        إصلاح طارئ
                    </button>
                    
                    <button onclick="location.reload()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg transition-colors">
                        <i class="fas fa-refresh mr-2"></i>
                        إعادة تحميل
                    </button>
                </div>
                
                <!-- سجل الأحداث المباشر -->
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                        <i class="fas fa-terminal mr-2 text-green-500"></i>
                        سجل الأحداث المباشر
                    </h3>
                    <div id="liveLog" class="text-sm text-gray-600 dark:text-gray-300 max-h-60 overflow-y-auto bg-white dark:bg-gray-800 p-3 rounded border font-mono">
                        جاري التحميل...
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- تحميل النظام الموحد -->
    <script src="js/unified-dark-mode.js"></script>
    <script src="js/dark-mode-emergency-fix.js"></script>
    
    <script>
        let logEntries = [];
        
        // إضافة رسالة للسجل
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar');
            const emoji = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            
            logEntries.unshift(`[${timestamp}] ${emoji} ${message}`);
            
            if (logEntries.length > 20) {
                logEntries = logEntries.slice(0, 20);
            }
            
            const logElement = document.getElementById('liveLog');
            if (logElement) {
                logElement.innerHTML = logEntries.join('<br>');
                logElement.scrollTop = 0;
            }
        }
        
        // تحديث الحالة المباشرة
        function updateLiveStatus() {
            const isDark = document.documentElement.classList.contains('dark');
            const savedMode = localStorage.getItem('darkMode');
            const button = document.getElementById('darkModeToggle');
            const icon = document.getElementById('darkModeIcon');
            
            const statusHTML = `
                الوضع الحالي: ${isDark ? '🌙 مظلم' : '☀️ فاتح'}<br>
                القيمة المحفوظة: ${savedMode || 'غير محدد'}<br>
                الزر: ${button ? '✅ موجود' : '❌ غير موجود'}<br>
                الأيقونة: ${icon ? '✅ موجودة' : '❌ غير موجودة'}<br>
                النظام مهيأ: ${window.isDarkModeInitialized ? '✅ نعم' : '❌ لا'}<br>
                وقت التحديث: ${new Date().toLocaleTimeString('ar')}
            `;
            
            const statusElement = document.getElementById('liveStatus');
            if (statusElement) {
                statusElement.innerHTML = statusHTML;
            }
        }
        
        // اختبار مباشر للتبديل
        function testDirectToggle() {
            addLog('بدء اختبار التبديل المباشر...');
            
            const beforeMode = document.documentElement.classList.contains('dark');
            addLog(`الوضع قبل التبديل: ${beforeMode ? 'مظلم' : 'فاتح'}`);
            
            // محاولة التبديل
            if (typeof toggleUnifiedDarkMode === 'function') {
                toggleUnifiedDarkMode();
                addLog('تم استدعاء toggleUnifiedDarkMode()', 'success');
            } else {
                addLog('toggleUnifiedDarkMode غير متاح!', 'error');
                
                // تبديل يدوي
                document.documentElement.classList.toggle('dark');
                const newMode = document.documentElement.classList.contains('dark');
                localStorage.setItem('darkMode', newMode ? 'true' : 'false');
                addLog('تم التبديل اليدوي', 'warning');
            }
            
            const afterMode = document.documentElement.classList.contains('dark');
            addLog(`الوضع بعد التبديل: ${afterMode ? 'مظلم' : 'فاتح'}`);
            
            updateLiveStatus();
        }
        
        // مراقبة النقرات على الزر
        document.addEventListener('click', function(e) {
            if (e.target.closest('#darkModeToggle')) {
                addLog('تم النقر على زر الوضع المظلم في Header');
            }
        });
        
        // مراقبة تغييرات الوضع
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const isDark = document.documentElement.classList.contains('dark');
                    addLog(`تم تغيير الوضع إلى: ${isDark ? 'مظلم' : 'فاتح'}`, 'success');
                    updateLiveStatus();
                }
            });
        });
        
        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['class']
        });
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLog('تم تحميل صفحة الاختبار');
            updateLiveStatus();
            
            // تحديث دوري
            setInterval(updateLiveStatus, 3000);
        });
        
        // جعل الدالة متاحة عالمياً
        window.testDirectToggle = testDirectToggle;
    </script>
</body>
</html>
