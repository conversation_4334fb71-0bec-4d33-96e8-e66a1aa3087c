@extends('layouts.admin')

@section('title', 'تقرير مقارنة المصروفات')

@section('content')
<div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <div>
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white">تقرير مقارنة المصروفات</h2>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">مقارنة المصروفات بين السنة الحالية والسنة السابقة</p>
    </div>
    <div class="mt-4 md:mt-0">
        <a href="{{ route('admin.expenses') }}" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-arrow-right ml-2"></i>
            <span>العودة للمصروفات</span>
        </a>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
    <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start mb-6">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white">مقارنة المصروفات الشهرية</h3>
        </div>
        <div class="relative" style="height: 350px;">
            <canvas id="monthlyComparisonChart"></canvas>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start mb-6">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white">مقارنة إجمالي المصروفات</h3>
        </div>
        <div class="relative" style="height: 350px;">
            <canvas id="totalComparisonChart"></canvas>
        </div>
    </div>
</div>

<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white">مقارنة المصروفات الشهرية</h3>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
                <tr class="bg-gray-50 dark:bg-gray-700">
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        الشهر
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        {{ now()->year }} (ر.س)
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        {{ now()->year - 1 }} (ر.س)
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        التغيير (%)
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                @foreach($months as $index => $month)
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                        {{ $month }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                        {{ number_format($currentYearTotals[$index], 2) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                        {{ number_format($previousYearTotals[$index], 2) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        @php
                            $change = $previousYearTotals[$index] > 0
                                ? (($currentYearTotals[$index] - $previousYearTotals[$index]) / $previousYearTotals[$index]) * 100
                                : ($currentYearTotals[$index] > 0 ? 100 : 0);
                            $changeClass = $change > 0
                                ? 'text-red-500'
                                : ($change < 0 ? 'text-green-500' : 'text-gray-500');
                            $changeIcon = $change > 0
                                ? 'fa-arrow-up'
                                : ($change < 0 ? 'fa-arrow-down' : 'fa-minus');
                        @endphp
                        <span class="{{ $changeClass }}">
                            <i class="fas {{ $changeIcon }} ml-1"></i>
                            {{ number_format(abs($change), 1) }}%
                        </span>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>

<div class="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white">مقارنة المصروفات حسب الفئة</h3>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
                <tr class="bg-gray-50 dark:bg-gray-700">
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        الفئة
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        {{ now()->year }} (ر.س)
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        {{ now()->year - 1 }} (ر.س)
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        التغيير (%)
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                @foreach($categoryComparison as $category => $data)
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                        {{ $category }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                        {{ number_format($data['current_year'], 2) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                        {{ number_format($data['previous_year'], 2) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        @php
                            $change = $data['previous_year'] > 0
                                ? (($data['current_year'] - $data['previous_year']) / $data['previous_year']) * 100
                                : ($data['current_year'] > 0 ? 100 : 0);
                            $changeClass = $change > 0
                                ? 'text-red-500'
                                : ($change < 0 ? 'text-green-500' : 'text-gray-500');
                            $changeIcon = $change > 0
                                ? 'fa-arrow-up'
                                : ($change < 0 ? 'fa-arrow-down' : 'fa-minus');
                        @endphp
                        <span class="{{ $changeClass }}">
                            <i class="fas {{ $changeIcon }} ml-1"></i>
                            {{ number_format(abs($change), 1) }}%
                        </span>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // بيانات الرسم البياني لمقارنة المصروفات الشهرية
        const monthlyData = {
            labels: @json($months),
            datasets: [
                {
                    label: '{{ now()->year }}',
                    data: @json($currentYearTotals),
                    backgroundColor: 'rgba(59, 130, 246, 0.2)',
                    borderColor: 'rgba(59, 130, 246, 1)',
                    borderWidth: 2,
                    tension: 0.3
                },
                {
                    label: '{{ now()->year - 1 }}',
                    data: @json($previousYearTotals),
                    backgroundColor: 'rgba(245, 158, 11, 0.2)',
                    borderColor: 'rgba(245, 158, 11, 1)',
                    borderWidth: 2,
                    tension: 0.3
                }
            ]
        };

        // إعدادات الرسم البياني لمقارنة المصروفات الشهرية
        const monthlyConfig = {
            type: 'line',
            data: monthlyData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString() + ' ر.س';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    }
                }
            }
        };

        // إنشاء الرسم البياني لمقارنة المصروفات الشهرية
        new Chart(document.getElementById('monthlyComparisonChart'), monthlyConfig);

        // حساب إجمالي المصروفات للسنة الحالية والسنة السابقة
        const currentYearTotal = @json($currentYearTotals).reduce((a, b) => a + b, 0);
        const previousYearTotal = @json($previousYearTotals).reduce((a, b) => a + b, 0);

        // بيانات الرسم البياني لمقارنة إجمالي المصروفات
        const totalData = {
            labels: ['{{ now()->year }}', '{{ now()->year - 1 }}'],
            datasets: [{
                label: 'إجمالي المصروفات (ر.س)',
                data: [currentYearTotal, previousYearTotal],
                backgroundColor: [
                    'rgba(59, 130, 246, 0.7)',
                    'rgba(245, 158, 11, 0.7)'
                ],
                borderWidth: 1
            }]
        };

        // إعدادات الرسم البياني لمقارنة إجمالي المصروفات
        const totalConfig = {
            type: 'bar',
            data: totalData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString() + ' ر.س';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += parseFloat(context.raw).toLocaleString() + ' ر.س';
                                return label;
                            }
                        }
                    }
                }
            }
        };

        // إنشاء الرسم البياني لمقارنة إجمالي المصروفات
        new Chart(document.getElementById('totalComparisonChart'), totalConfig);
    });
</script>
@endsection
