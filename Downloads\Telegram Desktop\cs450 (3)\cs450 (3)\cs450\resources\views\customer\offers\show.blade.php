@extends('customer.layouts.simple')

@section('title', 'تفاصيل العرض')

@section('content')
@php
    // معالجة البيانات للتوافق مع arrays و objects
    $offerTitle = is_array($offer) ? $offer['title'] : $offer->title;
    $offerDescription = is_array($offer) ? $offer['description'] : $offer->description;
    $offerImageUrl = is_array($offer) ? ($offer['image'] ?? asset('images/default-offer.svg')) : ($offer->image_url ?? asset('images/default-offer.svg'));
    $offerType = is_array($offer) ? $offer['type'] : $offer->type;
    $offerDiscountPercentage = is_array($offer) ? ($offer['discount'] ?? null) : ($offer->discount_percentage ?? null);
    $offerDiscountAmount = is_array($offer) ? null : ($offer->discount_amount ?? null);
    $offerTermsConditions = is_array($offer) ? ($offer['terms_conditions'] ?? null) : ($offer->terms_conditions ?? null);
    $offerStartDate = is_array($offer) ? ($offer['start_date'] ?? null) : ($offer->start_date ?? null);
    $offerEndDate = is_array($offer) ? ($offer['end_date'] ?? null) : ($offer->end_date ?? null);
    $offerSlug = is_array($offer) ? $offer['slug'] : $offer->slug;
    $isActive = is_array($offer) ? $offer['is_active'] : $offer->is_active;
@endphp

<!-- المحتوى الرئيسي -->
<main class="min-h-screen pt-20">
    <div class="container mx-auto px-4 py-8">
    <!-- Header محسن -->
    <div class="mb-12 text-center">
        <!-- <nav class="text-sm text-gray-500 dark:text-gray-400 mb-6 flex items-center justify-center">
            <a href="{{ route('customer.dashboard') }}" class="hover:text-primary transition-colors">
                <i class="fas fa-home ml-1"></i>الرئيسية
            </a>
            <span class="mx-3 text-gray-300">/</span>
            <a href="{{ route('customer.offers.index') }}" class="hover:text-primary transition-colors">العروض</a>
            <span class="mx-3 text-gray-300">/</span>
            <span class="text-primary font-medium">تفاصيل العرض</span>
        </nav> -->

        <div class="relative">
            <h1 class="text-4xl md:text-5xl font-bold text-darkText dark:text-white mb-4">
                {{ $offerTitle }}
                <span class="text-primary">🎯</span>
            </h1>

            <!-- خط زخرفي -->
            <div class="flex items-center justify-center mt-6">
                <div class="h-1 w-20 bg-gradient-to-r from-primary to-accent rounded-full"></div>
                <div class="mx-4 text-primary text-2xl">⭐</div>
                <div class="h-1 w-20 bg-gradient-to-l from-primary to-accent rounded-full"></div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- صورة العرض المحسنة -->
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-gray-800 rounded-2xl overflow-hidden shadow-2xl border border-gray-100 dark:border-gray-700">
                <div class="relative overflow-hidden">
                    <img src="{{ $offerImageUrl }}"
                         alt="{{ $offerTitle }}"
                         class="w-full h-96 object-cover transition-transform duration-500 hover:scale-105">

                    <!-- تأثير التدرج -->
                    <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>

                    <!-- الشارات المحسنة -->
                    <div class="absolute top-6 left-6">
                        <span class="bg-primary/90 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
                            @if($offerType === 'حجز')
                                🍽️ {{ $offerType }}
                            @else
                                🍕 {{ $offerType ?? 'طعام' }}
                            @endif
                        </span>
                    </div>

                    @if($offerDiscountPercentage)
                        <div class="absolute top-6 right-6">
                            <span class="bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg animate-pulse">
                                🔥 خصم {{ $offerDiscountPercentage }}%
                            </span>
                        </div>
                    @elseif($offerDiscountAmount)
                        <div class="absolute top-6 right-6">
                            <span class="bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg animate-pulse">
                                🔥 خصم {{ $offerDiscountAmount }} د.ل
                            </span>
                        </div>
                    @endif
                </div>

                <div class="p-8">
                    <h2 class="text-3xl font-bold text-darkText dark:text-white mb-6 leading-tight">{{ $offerTitle }}</h2>

                    <div class="prose dark:prose-invert max-w-none">
                        <p class="text-gray-600 dark:text-gray-300 leading-relaxed">{{ $offerDescription }}</p>

                        @if($offerTermsConditions)
                            <h3 class="text-lg font-semibold text-darkText dark:text-white mt-6 mb-3">شروط وأحكام العرض:</h3>
                            <div class="text-gray-600 dark:text-gray-300">
                                @if(is_array($offerTermsConditions))
                                    <ul class="list-disc list-inside space-y-2">
                                        @foreach($offerTermsConditions as $term)
                                            <li>{{ $term }}</li>
                                        @endforeach
                                    </ul>
                                @else
                                    <p>{{ $offerTermsConditions }}</p>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات العرض والحجز المحسنة -->
        <div class="lg:col-span-1">
            <div class="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-700 rounded-2xl shadow-2xl p-8 sticky top-8 border border-gray-100 dark:border-gray-600">
                <div class="mb-8">
                    <div class="flex items-center mb-6">
                        <i class="fas fa-info-circle text-primary text-xl ml-3"></i>
                        <h3 class="text-2xl font-bold text-darkText dark:text-white">معلومات العرض</h3>
                    </div>

                    <div class="space-y-4">
                        @if($offerStartDate)
                        <div class="flex justify-between items-center p-3 bg-white/50 dark:bg-gray-700/50 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-calendar-alt text-primary ml-2"></i>
                                <span class="text-gray-600 dark:text-gray-300">تاريخ البداية:</span>
                            </div>
                            <span class="font-semibold text-darkText dark:text-white">{{ is_string($offerStartDate) ? $offerStartDate : $offerStartDate->format('Y/m/d') }}</span>
                        </div>
                        @endif

                        @if($offerEndDate)
                        <div class="flex justify-between items-center p-3 bg-white/50 dark:bg-gray-700/50 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-calendar-times text-red-500 ml-2"></i>
                                <span class="text-gray-600 dark:text-gray-300">تاريخ الانتهاء:</span>
                            </div>
                            <span class="font-semibold text-darkText dark:text-white">{{ is_string($offerEndDate) ? $offerEndDate : $offerEndDate->format('Y/m/d') }}</span>
                        </div>
                        @else
                        <div class="flex justify-between items-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                            <div class="flex items-center">
                                <i class="fas fa-infinity text-green-600 ml-2"></i>
                                <span class="text-gray-600 dark:text-gray-300">المدة:</span>
                            </div>
                            <span class="font-semibold text-green-600">عرض مفتوح</span>
                        </div>
                        @endif

                        @php
                            $originalPrice = is_array($offer) ? ($offer['original_price'] ?? null) : ($offer->original_price ?? null);
                            $discountedPrice = is_array($offer) ? ($offer['discounted_price'] ?? null) : ($offer->discounted_price ?? null);
                        @endphp

                        @if($originalPrice)
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-300">السعر الأصلي:</span>
                                <span class="line-through text-gray-500">{{ $originalPrice }} د.ل</span>
                            </div>
                        @endif

                        @if($discountedPrice)
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-300">السعر بعد الخصم:</span>
                                <span class="font-bold text-primary text-lg">{{ $discountedPrice }} د.ل</span>
                            </div>
                        @endif

                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-300">الحالة:</span>
                            <span class="font-semibold {{ $isActive ? 'text-green-600' : 'text-red-600' }}">
                                {{ $isActive ? 'متاح' : 'منتهي' }}
                            </span>
                        </div>
                    </div>
                </div>

                @if($isActive)
                    <div class="space-y-3">
                        @auth
                            @if($offer['is_in_cart'])
                                <div class="w-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-center py-3 px-4 rounded-lg border-2 border-gray-300 dark:border-gray-600">
                                    <i class="fas fa-check ml-1"></i>
                                    تم تطبيق العرض بالفعل
                                </div>
                                <a href="{{ route('customer.cart') }}"
                                   class="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-center py-3 px-4 rounded-lg transition-all duration-200 font-semibold shadow-md hover:shadow-lg transform hover:scale-105 block mt-3">
                                    <i class="fas fa-shopping-cart ml-1"></i>
                                    الذهاب للسلة
                                </a>
                            @else
                                <button onclick="applyOfferDirectlyAndGoToCart('{{ $offerSlug }}')"
                                        id="apply-offer-btn"
                                        class="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white text-center py-3 px-4 rounded-lg transition-all duration-200 font-semibold shadow-md hover:shadow-lg transform hover:scale-105">
                                    <i class="fas fa-plus ml-1"></i>
                                    تطبيق العرض والذهاب للسلة
                                </button>
                            @endif
                        @else
                            <a href="{{ route('login') }}"
                               class="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white text-center py-3 px-4 rounded-lg transition-all duration-200 font-semibold shadow-md hover:shadow-lg">
                                <i class="fas fa-sign-in-alt ml-1"></i>
                                سجل دخولك لتطبيق العرض
                            </a>
                        @endauth



                        <button onclick="shareOffer()"
                                class="w-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-darkText dark:text-white text-center py-3 px-4 rounded-lg transition font-semibold">
                            مشاركة العرض
                        </button>
                    </div>
                @else
                    <div class="text-center py-4">
                        <p class="text-red-600 font-semibold">هذا العرض غير متاح حالياً</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- عروض مشابهة -->
    <div class="mt-12">
        <h2 class="text-2xl font-bold text-darkText dark:text-white mb-6">عروض أخرى قد تهمك</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($relatedOffers as $relatedOffer)
                <div class="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition">
                    <img src="{{ $relatedOffer['image_url'] ?? asset('images/default-offer.svg') }}" alt="{{ $relatedOffer['title'] }}" class="w-full h-48 object-cover">
                    <div class="p-4">
                        <h3 class="font-bold text-darkText dark:text-white mb-2">{{ $relatedOffer['title'] }}</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-3">{{ Str::limit($relatedOffer['description'], 100) }}</p>
                        <a href="{{ route('customer.offers.show', $relatedOffer['slug']) }}"
                           class="text-primary hover:text-primary/80 font-semibold text-sm">
                            عرض التفاصيل ←
                        </a>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</div>
</main>

<!-- نموذج الحجز المنبثق -->
<div id="reservationModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6">
            <!-- رأس النموذج -->
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold text-darkText dark:text-white">حجز العرض: {{ $offerTitle }}</h3>
                <button onclick="closeReservationModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- نموذج الحجز -->
            <form id="offerReservationForm" action="{{ route('customer.reservations.store') }}" method="POST" class="space-y-4">
                @csrf
                <input type="hidden" name="offer_slug" value="{{ $offerSlug }}">
                <input type="hidden" name="offer_title" value="{{ $offerTitle }}">

                <!-- تاريخ الحجز -->
                <div>
                    <label for="reservation_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ الحجز</label>
                    <input type="date" id="reservation_date" name="reservation_date" required
                           min="{{ date('Y-m-d') }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                </div>

                <!-- وقت الحجز -->
                <div>
                    <label for="reservation_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">وقت الحجز</label>
                    <select id="reservation_time" name="reservation_time" required
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                        <option value="">اختر الوقت</option>
                        <option value="12:00">12:00 ظهراً</option>
                        <option value="12:30">12:30 ظهراً</option>
                        <option value="13:00">1:00 ظهراً</option>
                        <option value="13:30">1:30 ظهراً</option>
                        <option value="14:00">2:00 ظهراً</option>
                        <option value="14:30">2:30 ظهراً</option>
                        <option value="15:00">3:00 عصراً</option>
                        <option value="15:30">3:30 عصراً</option>
                        <option value="16:00">4:00 عصراً</option>
                        <option value="16:30">4:30 عصراً</option>
                        <option value="17:00">5:00 مساءً</option>
                        <option value="17:30">5:30 مساءً</option>
                        <option value="18:00">6:00 مساءً</option>
                        <option value="18:30">6:30 مساءً</option>
                        <option value="19:00">7:00 مساءً</option>
                        <option value="19:30">7:30 مساءً</option>
                        <option value="20:00">8:00 مساءً</option>
                        <option value="20:30">8:30 مساءً</option>
                        <option value="21:00">9:00 مساءً</option>
                        <option value="21:30">9:30 مساءً</option>
                        <option value="22:00">10:00 مساءً</option>
                    </select>
                </div>

                <!-- عدد الأشخاص -->
                <div>
                    <label for="party_size" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">عدد الأشخاص</label>
                    <select id="party_size" name="party_size" required
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                        <option value="">اختر عدد الأشخاص</option>
                        @for($i = 1; $i <= 10; $i++)
                            <option value="{{ $i }}">{{ $i }} {{ $i == 1 ? 'شخص' : 'أشخاص' }}</option>
                        @endfor
                    </select>
                </div>

                <!-- معلومات الاتصال -->
                <div>
                    <label for="contact_phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الهاتف</label>
                    <input type="tel" id="contact_phone" name="contact_phone" required
                           value="{{ auth()->user()->phone ?? '' }}"
                           placeholder="مثال: +218 91 234 5678"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                </div>

                <!-- طلبات خاصة -->
                <div>
                    <label for="special_requests" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">طلبات خاصة (اختياري)</label>
                    <textarea id="special_requests" name="special_requests" rows="3"
                              placeholder="أي طلبات خاصة للحجز..."
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"></textarea>
                </div>

                <!-- معلومات العرض -->
                @if(isset($offer['discount']) && $offer['discount'])
                    <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
                        <div class="flex items-center">
                            <i class="fas fa-tag text-green-600 ml-2"></i>
                            <span class="text-green-800 dark:text-green-200 font-semibold">
                                سيتم تطبيق خصم {{ $offerDiscountPercentage }}% على هذا الحجز
                            </span>
                        </div>
                    </div>
                @endif

                <!-- أزرار الإجراءات -->
                <div class="flex gap-3 pt-4">
                    <button type="button" onclick="closeReservationModal()"
                            class="flex-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition">
                        إلغاء
                    </button>
                    <button type="submit" id="submitReservationBtn"
                            class="flex-1 bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-lg transition font-semibold">
                        <span id="submitBtnText">تأكيد الحجز</span>
                        <i id="submitBtnLoader" class="fas fa-spinner fa-spin hidden mr-2"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openReservationModal() {
    @auth
        document.getElementById('reservationModal').classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    @else
        alert('يجب تسجيل الدخول أولاً للحجز');
        window.location.href = '{{ route("login") }}';
    @endauth
}

function closeReservationModal() {
    document.getElementById('reservationModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

function shareOffer() {
    if (navigator.share) {
        navigator.share({
            title: '{{ $offerTitle }}',
            text: '{{ $offerDescription }}',
            url: window.location.href
        });
    } else {
        // نسخ الرابط للحافظة
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('تم نسخ رابط العرض!');
        });
    }
}

// إغلاق النموذج عند النقر خارجه
document.getElementById('reservationModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeReservationModal();
    }
});

// إغلاق النموذج بمفتاح Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeReservationModal();
    }
});

// دالة تطبيق العرض مباشرة والانتقال للسلة
function applyOfferDirectlyAndGoToCart(offerSlug) {
    const button = document.getElementById('apply-offer-btn');

    // منع التطبيق المتكرر
    if (button.disabled) return;

    const originalText = button.innerHTML;

    // إظهار حالة التحميل
    button.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i> جاري التطبيق...';
    button.disabled = true;

    // إرسال طلب AJAX لتطبيق العرض
    fetch('{{ route("customer.cart.add-offer") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            offer_slug: offerSlug
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إظهار رسالة نجاح سريعة
            showSuccessMessage(data.message);

            // تغيير نص الزر
            button.innerHTML = '<i class="fas fa-check ml-1"></i> تم التطبيق - جاري التحويل...';

            // الانتقال للسلة بعد ثانية واحدة
            setTimeout(() => {
                window.location.href = '{{ route("customer.cart") }}';
            }, 1500);

        } else {
            showErrorMessage(data.message);
            button.innerHTML = originalText;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorMessage('حدث خطأ أثناء تطبيق العرض');
        button.innerHTML = originalText;
        button.disabled = false;
    });
}



// دوال إظهار الرسائل
function showSuccessMessage(message) {
    // إنشاء رسالة نجاح مؤقتة
    const alertDiv = document.createElement('div');
    alertDiv.className = 'fixed top-20 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
    alertDiv.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-check-circle ml-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(alertDiv);

    // إظهار الرسالة
    setTimeout(() => {
        alertDiv.classList.remove('translate-x-full');
    }, 100);

    // إخفاء الرسالة بعد 3 ثوان
    setTimeout(() => {
        alertDiv.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(alertDiv);
        }, 300);
    }, 3000);
}

function showErrorMessage(message) {
    // إنشاء رسالة خطأ مؤقتة
    const alertDiv = document.createElement('div');
    alertDiv.className = 'fixed top-20 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
    alertDiv.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-exclamation-circle ml-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(alertDiv);

    // إظهار الرسالة
    setTimeout(() => {
        alertDiv.classList.remove('translate-x-full');
    }, 100);

    // إخفاء الرسالة بعد 3 ثوان
    setTimeout(() => {
        alertDiv.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(alertDiv);
        }, 300);
    }, 3000);
}

// تحسين تجربة إرسال النموذج
document.getElementById('offerReservationForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitReservationBtn');
    const submitBtnText = document.getElementById('submitBtnText');
    const submitBtnLoader = document.getElementById('submitBtnLoader');

    // تعطيل الزر وإظهار التحميل
    submitBtn.disabled = true;
    submitBtnText.textContent = 'جاري الحجز...';
    submitBtnLoader.classList.remove('hidden');
});
</script>
@endsection
