<?php

/**
 * إعداد المشروع للمرة الأولى
 * يقوم بتشغيل جميع الهجرات والبذور بالترتيب الصحيح
 */

echo "🚀 بدء إعداد المشروع...\n";
echo "========================\n\n";

// التحقق من وجود ملف .env
if (!file_exists('.env')) {
    echo "❌ ملف .env غير موجود!\n";
    echo "📋 يرجى نسخ .env.example إلى .env وتعديل الإعدادات\n";
    exit(1);
}

// التحقق من الاتصال بقاعدة البيانات
echo "🔍 التحقق من الاتصال بقاعدة البيانات...\n";
try {
    $output = shell_exec('php artisan migrate:status 2>&1');
    if (strpos($output, 'could not find driver') !== false || strpos($output, 'Connection refused') !== false) {
        echo "❌ فشل الاتصال بقاعدة البيانات!\n";
        echo "📋 يرجى التحقق من إعدادات قاعدة البيانات في ملف .env\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "\n";
    exit(1);
}

echo "✅ الاتصال بقاعدة البيانات ناجح\n\n";

// إنشاء مفتاح التطبيق
echo "🔑 إنشاء مفتاح التطبيق...\n";
shell_exec('php artisan key:generate --force');
echo "✅ تم إنشاء مفتاح التطبيق\n\n";

// مسح الكاش
echo "🧹 مسح الكاش...\n";
shell_exec('php artisan cache:clear');
shell_exec('php artisan config:clear');
shell_exec('php artisan view:clear');
shell_exec('php artisan route:clear');
echo "✅ تم مسح الكاش\n\n";

// إنشاء رابط التخزين
echo "🔗 إنشاء رابط التخزين...\n";
shell_exec('php artisan storage:link');
echo "✅ تم إنشاء رابط التخزين\n\n";

// تشغيل الهجرات
echo "📊 تشغيل هجرات قاعدة البيانات...\n";
$migrateOutput = shell_exec('php artisan migrate --force 2>&1');
if (strpos($migrateOutput, 'Migrated:') !== false || strpos($migrateOutput, 'Nothing to migrate') !== false) {
    echo "✅ تم تشغيل الهجرات بنجاح\n\n";
} else {
    echo "❌ فشل في تشغيل الهجرات:\n";
    echo $migrateOutput . "\n";
    
    // محاولة إعادة تعيين قاعدة البيانات
    echo "🔄 محاولة إعادة تعيين قاعدة البيانات...\n";
    $resetOutput = shell_exec('php artisan migrate:fresh --force 2>&1');
    if (strpos($resetOutput, 'Migrated:') !== false) {
        echo "✅ تم إعادة تعيين قاعدة البيانات بنجاح\n\n";
    } else {
        echo "❌ فشل في إعادة تعيين قاعدة البيانات:\n";
        echo $resetOutput . "\n";
        exit(1);
    }
}

// تشغيل البذور
echo "🌱 تشغيل بذور البيانات...\n";
$seedOutput = shell_exec('php artisan db:seed --force 2>&1');
if (strpos($seedOutput, 'Database seeding completed successfully') !== false || 
    strpos($seedOutput, 'Seeding:') !== false) {
    echo "✅ تم تشغيل البذور بنجاح\n\n";
} else {
    echo "⚠️  تحذير: قد تكون هناك مشاكل في البذور:\n";
    echo $seedOutput . "\n";
    echo "📋 يمكنك المتابعة، لكن قد تحتاج لإضافة البيانات يدوياً\n\n";
}

// إنشاء مجلدات التخزين المطلوبة
echo "📁 إنشاء مجلدات التخزين...\n";
$directories = [
    'storage/app/public/menu',
    'storage/app/public/offers',
    'storage/app/public/profiles',
    'storage/app/public/invoices',
    'public/images'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "✅ تم إنشاء مجلد: $dir\n";
    }
}

// إنشاء صور افتراضية
echo "\n🖼️  إنشاء الصور الافتراضية...\n";
createDefaultImages();

echo "\n🎉 تم إعداد المشروع بنجاح!\n";
echo "========================\n";
echo "📋 معلومات تسجيل الدخول:\n";
echo "   البريد الإلكتروني: <EMAIL>\n";
echo "   كلمة المرور: A178a2002\n";
echo "   النوع: مدير\n\n";
echo "🌐 يمكنك الآن تشغيل الخادم:\n";
echo "   php artisan serve\n\n";

function createDefaultImages() {
    // صورة افتراضية للطعام
    $foodSvg = '<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
        <rect width="400" height="300" fill="#f3f4f6"/>
        <circle cx="200" cy="120" r="40" fill="#d1d5db"/>
        <path d="M160 120 Q200 80 240 120 Q200 160 160 120" fill="#9ca3af"/>
        <rect x="180" y="140" width="40" height="20" rx="10" fill="#6b7280"/>
        <text x="200" y="200" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#6b7280">صورة الطعام</text>
        <text x="200" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#9ca3af">غير متوفرة</text>
    </svg>';
    
    // صورة افتراضية للعروض
    $offerSvg = '<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
        <rect width="400" height="300" fill="#fef3c7"/>
        <circle cx="200" cy="120" r="50" fill="#f59e0b"/>
        <text x="200" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" fill="white" font-weight="bold">%</text>
        <text x="200" y="200" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#92400e">صورة العرض</text>
        <text x="200" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#b45309">غير متوفرة</text>
    </svg>';
    
    // صورة افتراضية للملف الشخصي
    $avatarSvg = '<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
        <rect width="200" height="200" fill="#e5e7eb"/>
        <circle cx="100" cy="80" r="30" fill="#9ca3af"/>
        <path d="M50 150 Q100 120 150 150 L150 200 L50 200 Z" fill="#9ca3af"/>
        <text x="100" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#6b7280">صورة شخصية</text>
    </svg>';
    
    file_put_contents('public/images/default-food.svg', $foodSvg);
    file_put_contents('public/images/default-offer.svg', $offerSvg);
    file_put_contents('public/images/default-avatar.svg', $avatarSvg);
    
    echo "✅ تم إنشاء الصور الافتراضية\n";
}
