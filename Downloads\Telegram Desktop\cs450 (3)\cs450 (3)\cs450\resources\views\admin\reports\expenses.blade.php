@extends('layouts.admin')

@section('title', 'تقرير المصروفات - لوحة تحكم Eat Hub')

@section('page-title', 'تقرير المصروفات')

@push('styles')
<!-- Chart.js CSS -->
<style>
    .chart-container { position: relative; height: 256px; }
    .table-container { max-height: 256px; overflow-y: auto; }
    .table-container::-webkit-scrollbar { width: 4px; }
    .table-container::-webkit-scrollbar-track { background: #f1f1f1; }
    .table-container::-webkit-scrollbar-thumb { background: #c1c1c1; border-radius: 2px; }
    .table-container::-webkit-scrollbar-thumb:hover { background: #a8a8a8; }

    @media print {
        .print\\:hidden { display: none !important; }
        .chart-container { height: 200px; }
        .table-container { max-height: none; overflow: visible; }
    }

    @media (max-width: 768px) {
        .chart-container { height: 200px; }
        .table-container { max-height: 200px; }
    }
</style>
@endpush

@section('content')
<div class="mb-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-1">💸 تقرير المصروفات</h2>
            <p class="text-gray-600 dark:text-gray-400 text-sm">المصروفات اليومية والتصنيفات التفصيلية</p>
        </div>
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 sm:space-x-reverse">
            <a href="{{ route('admin.reports') }}" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 py-2 px-4 rounded-md transition-all">
                <i class="fas fa-arrow-right ml-1"></i>
                <span>العودة للتقارير</span>
            </a>
            <button onclick="exportExpensesToExcel()" class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-md transition-all">
                <i class="fas fa-download ml-1"></i>
                <span>تصدير Excel</span>
            </button>
            <button onclick="window.print()" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md transition-all print:hidden">
                <i class="fas fa-print ml-1"></i>
                <span>طباعة</span>
            </button>
        </div>
    </div>

    <!-- فلاتر التقرير -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
        <form action="{{ route('admin.reports.expenses') }}" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="period" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">📅 الفترة الزمنية</label>
                <select id="period" name="period" onchange="toggleCustomDates(this.value)" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    <option value="week" {{ request('period') == 'week' ? 'selected' : '' }}>📅 آخر أسبوع</option>
                    <option value="month" {{ request('period') == 'month' ? 'selected' : '' }}>📅 آخر شهر</option>
                    <option value="quarter" {{ request('period') == 'quarter' ? 'selected' : '' }}>📅 آخر 3 أشهر</option>
                    <option value="year" {{ request('period') == 'year' ? 'selected' : '' }}>📅 آخر سنة</option>
                    <option value="custom" {{ request('period') == 'custom' ? 'selected' : '' }}>🎯 فترة مخصصة</option>
                </select>
            </div>
            <div id="custom_dates" class="grid grid-cols-1 md:grid-cols-2 gap-4 md:col-span-2" style="{{ request('period') == 'custom' ? '' : 'display: none;' }}">
                <div>
                    <label for="custom_start" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">📅 من تاريخ</label>
                    <input type="date" id="custom_start" name="start_date" value="{{ $startDate }}" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                </div>
                <div>
                    <label for="custom_end" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">📅 إلى تاريخ</label>
                    <input type="date" id="custom_end" name="end_date" value="{{ $endDate }}" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                </div>
            </div>
            <div class="flex items-end">
                <button type="submit" class="w-full bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-md transition-all">
                    <i class="fas fa-search ml-1"></i>
                    <span>تطبيق الفلتر</span>
                </button>
            </div>
        </form>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <!-- إجمالي المصروفات -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">💸 إجمالي المصروفات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($totalExpenses, 2) }} د.ل</p>
                </div>
                <div class="rounded-full bg-red-100 dark:bg-red-900/30 p-3">
                    <i class="fas fa-money-bill-wave text-red-500 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- عدد المصروفات -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">📋 عدد المصروفات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $dailyExpenses->sum(function($item) { return 1; }) }}</p>
                </div>
                <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3">
                    <i class="fas fa-receipt text-blue-500 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- متوسط المصروف -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">📊 متوسط المصروف</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">
                        @php
                            $count = $dailyExpenses->count();
                            $average = $count > 0 ? $totalExpenses / $count : 0;
                        @endphp
                        {{ number_format($average, 2) }} د.ل
                    </p>
                </div>
                <div class="rounded-full bg-yellow-100 dark:bg-yellow-900/30 p-3">
                    <i class="fas fa-chart-line text-yellow-500 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- أعلى فئة -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">🏆 أعلى فئة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">
                        @php
                            $topCategory = $expensesByCategory->sortByDesc('total')->first();
                            $catMap = [
                                'ingredients' => 'المكونات',
                                'utilities' => 'المرافق',
                                'salaries' => 'المرتبات',
                                'maintenance' => 'الصيانة',
                                'rent' => 'الإيجار',
                                'taxes' => 'الضرائب',
                                'fees' => 'الرسوم',
                                'purchase' => 'شراء المخزون',
                                'purchase_auto' => 'شراء المخزون (تلقائي)',
                                'other' => 'أخرى',
                                'others' => 'أخرى',
                            ];
                            $topCategoryName = $topCategory ? ($catMap[strtolower($topCategory->category)] ?? $topCategory->category) : 'لا توجد';
                        @endphp
                        {{ $topCategoryName }}
                    </p>
                </div>
                <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3">
                    <i class="fas fa-crown text-green-500 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- المخططات -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- مخطط المصروفات اليومية -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">📈 المصروفات اليومية</h3>
            </div>
            <div id="dailyExpensesChart" class="w-full h-80">
                <canvas id="dailyExpensesCanvas" height="320"></canvas>
            </div>
        </div>

        <!-- مخطط توزيع المصروفات حسب الفئة -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">🥘 توزيع المصروفات حسب الفئة</h3>
            </div>
            <div id="categoryExpensesChart" class="w-full h-80">
                <canvas id="categoryExpensesCanvas" height="320"></canvas>
            </div>
        </div>
    </div>


    <!-- المصروفات حسب الفئة -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">المصروفات حسب الفئة</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full text-sm">
                    <thead>
                        <tr class="bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                            <th class="py-2 px-3 text-right">الفئة</th>
                            <th class="py-2 px-3 text-right">المصروفات</th>
                            <th class="py-2 px-3 text-right">النسبة</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                        @if(isset($expensesByCategory) && count($expensesByCategory) > 0)
                            @foreach($expensesByCategory as $category)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                                <td class="py-3 px-3 font-medium text-gray-800 dark:text-white">
                                    @php
                                        $map = [
                                            'ingredients' => 'المكونات',
                                            'utilities' => 'المرافق',
                                            'salaries' => 'المرتبات',
                                            'maintenance' => 'الصيانة',
                                            'rent' => 'الإيجار',
                                            'taxes' => 'الضرائب',
                                            'fees' => 'الرسوم',
                                            'purchase' => 'شراء المخزون',
                                            'purchase_auto' => 'شراء المخزون (تلقائي)',
                                            'other' => 'أخرى',
                                            'others' => 'أخرى',
                                        ];
                                        $label = $map[strtolower(trim($category->category ?? ''))] ?? ($category->category ?? 'غير محدد');
                                    @endphp
                                    {{ $label }}
                                </td>
                                <td class="py-3 px-3 text-gray-600 dark:text-gray-300">{{ number_format($category->total, 2) }} د.ل</td>
                                <td class="py-3 px-3">
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <div class="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                                            <div class="bg-red-500 h-full rounded-full" style="width: {{ $totalExpenses > 0 ? ($category->total / $totalExpenses) * 100 : 0 }}%;"></div>
                                        </div>
                                        <span class="text-sm text-gray-600 dark:text-gray-400">{{ $totalExpenses > 0 ? number_format(($category->total / $totalExpenses) * 100, 1) : 0 }}%</span>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        @else
                            <tr>
                                <td colspan="3" class="py-8 text-center text-gray-500 dark:text-gray-400">
                                    <i class="fas fa-inbox text-4xl mb-2"></i>
                                    <p>لا توجد مصروفات في هذه الفترة</p>
                                </td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>

        <!-- أعلى المصروفات -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">أعلى المصروفات</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full text-sm">
                    <thead>
                        <tr class="bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                            <th class="py-2 px-3 text-right">الوصف</th>
                            <th class="py-2 px-3 text-right">الفئة</th>
                            <th class="py-2 px-3 text-right">المبلغ</th>
                            <th class="py-2 px-3 text-right">التاريخ</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                        @if(isset($topExpenses) && count($topExpenses) > 0)
                            @foreach($topExpenses as $expense)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                                <td class="py-3 px-3 font-medium text-gray-800 dark:text-white">{{ $expense->description }}</td>
                                <td class="py-3 px-3 text-gray-600 dark:text-gray-300">
                                    @php
                                        $map = [
                                            'ingredients' => 'المكونات',
                                            'utilities' => 'المرافق',
                                            'salaries' => 'المرتبات',
                                            'maintenance' => 'الصيانة',
                                            'rent' => 'الإيجار',
                                            'taxes' => 'الضرائب',
                                            'fees' => 'الرسوم',
                                            'purchase' => 'شراء المخزون',
                                            'purchase_auto' => 'شراء المخزون (تلقائي)',
                                            'other' => 'أخرى',
                                            'others' => 'أخرى',
                                        ];
                                        $label = $map[strtolower(trim($expense->category ?? ''))] ?? ($expense->category ?? 'غير محدد');
                                    @endphp
                                    {{ $label }}
                                </td>
                                <td class="py-3 px-3 text-gray-600 dark:text-gray-300">{{ number_format($expense->amount, 2) }} د.ل</td>
                                <td class="py-3 px-3 text-gray-600 dark:text-gray-300">{{ $expense->expense_date->format('Y-m-d') }}</td>
                            </tr>
                            @endforeach
                        @else
                            <tr>
                                <td colspan="4" class="py-8 text-center text-gray-500 dark:text-gray-400">
                                    <i class="fas fa-inbox text-4xl mb-2"></i>
                                    <p>لا توجد مصروفات في هذه الفترة</p>
                                </td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
    </div>



@section('scripts')
<!-- Chart.js for charts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{{ asset('js/expenses-charts.js') }}"></script>
<script>
    function toggleCustomDates(value) {
        const customDatesDiv = document.getElementById('custom_dates');
        if (value === 'custom') {
            customDatesDiv.style.display = 'grid';
        } else {
            customDatesDiv.style.display = 'none';
        }
    }

    // تمرير البيانات للمخططات
    window.expensesData = {
        daily: {!! json_encode($dailyExpenses ?? []) !!},
        category: {!! json_encode($expensesByCategory ?? []) !!}
    };

    // تهيئة المخططات عند تحميل الصفحة
    window.addEventListener('load', function() {
        setTimeout(function() {
            if (typeof initExpensesChartsChartJS === 'function') {
                initExpensesChartsChartJS();
            }
        }, 300);
    });

    // تصدير Excel
    function exportExpensesToExcel() {
        const data = [
            ['التاريخ', 'الوصف', 'الفئة', 'المبلغ', 'طريقة الدفع']
        ];

        // إضافة بيانات وهمية للتصدير (يمكن تحسينها لاحقاً)
        @if(isset($topExpenses))
            @foreach($topExpenses as $expense)
                data.push([
                    '{{ $expense->expense_date->format('Y-m-d') }}',
                    '{{ $expense->description }}',
                    '{{ $expense->category }}',
                    '{{ $expense->amount }}',
                    '{{ $expense->payment_method }}'
                ]);
            @endforeach
        @endif

        const ws = XLSX.utils.aoa_to_sheet(data);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'تقرير المصروفات');
        XLSX.writeFile(wb, 'تقرير_المصروفات_' + new Date().toISOString().split('T')[0] + '.xlsx');
    }
</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
@endsection
@endsection
