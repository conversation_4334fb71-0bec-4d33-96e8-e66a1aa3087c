/**
 * إصلاح طارئ للوضع المظلم - يمكن تشغيله من console
 */

// دالة إصلاح شاملة
window.emergencyDarkModeFix = function() {
    console.log('🚨 بدء الإصلاح الطارئ للوضع المظلم...');
    
    // 1. تنظيف localStorage من القيم المتضاربة
    const keysToClean = ['theme_preference', 'effective_theme'];
    keysToClean.forEach(key => {
        if (localStorage.getItem(key)) {
            localStorage.removeItem(key);
            console.log(`🧹 تم حذف ${key} من localStorage`);
        }
    });
    
    // 2. إعادة تعيين الوضع المظلم
    const currentMode = document.documentElement.classList.contains('dark');
    localStorage.setItem('darkMode', currentMode ? 'true' : 'false');
    console.log(`💾 تم حفظ الوضع الحالي: ${currentMode ? 'مظلم' : 'فاتح'}`);
    
    // 3. إعادة تهيئة الزر
    const button = document.getElementById('darkModeToggle');
    if (button) {
        // إزالة جميع المستمعات
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
        
        // إضافة مستمع جديد
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('🖱️ تم النقر على الزر المُصلح');
            
            // تبديل الوضع
            const html = document.documentElement;
            const wasLight = !html.classList.contains('dark');
            
            if (wasLight) {
                html.classList.add('dark');
                localStorage.setItem('darkMode', 'true');
                console.log('🌙 تم التبديل إلى الوضع المظلم');
            } else {
                html.classList.remove('dark');
                localStorage.setItem('darkMode', 'false');
                console.log('☀️ تم التبديل إلى الوضع الفاتح');
            }
            
            // تحديث الأيقونة
            const icon = document.getElementById('darkModeIcon');
            if (icon) {
                const isDark = html.classList.contains('dark');
                icon.className = isDark ? 
                    'fas fa-sun theme-icon group-hover:rotate-180 transition-transform duration-500' :
                    'fas fa-moon theme-icon group-hover:rotate-180 transition-transform duration-500';
            }
        });
        
        console.log('✅ تم إصلاح زر الوضع المظلم');
    } else {
        console.error('❌ لم يتم العثور على زر الوضع المظلم!');
    }
    
    // 4. تحديث الأيقونة
    const icon = document.getElementById('darkModeIcon');
    if (icon) {
        const isDark = document.documentElement.classList.contains('dark');
        icon.className = isDark ? 
            'fas fa-sun theme-icon group-hover:rotate-180 transition-transform duration-500' :
            'fas fa-moon theme-icon group-hover:rotate-180 transition-transform duration-500';
        console.log('🔄 تم تحديث الأيقونة');
    }
    
    console.log('✅ تم الانتهاء من الإصلاح الطارئ');
    
    // إرجاع معلومات التشخيص
    return {
        currentMode: document.documentElement.classList.contains('dark') ? 'dark' : 'light',
        savedMode: localStorage.getItem('darkMode'),
        buttonExists: !!document.getElementById('darkModeToggle'),
        iconExists: !!document.getElementById('darkModeIcon')
    };
};

// دالة تشخيص سريعة
window.quickDarkModeDiagnosis = function() {
    console.log('🔍 تشخيص سريع للوضع المظلم:');
    
    const info = {
        currentMode: document.documentElement.classList.contains('dark') ? 'مظلم' : 'فاتح',
        savedMode: localStorage.getItem('darkMode'),
        htmlClasses: document.documentElement.className,
        buttonExists: !!document.getElementById('darkModeToggle'),
        iconExists: !!document.getElementById('darkModeIcon'),
        allStorageKeys: Object.keys(localStorage).filter(key => 
            key.includes('theme') || key.includes('dark') || key.includes('mode')
        )
    };
    
    console.table(info);
    return info;
};

// دالة إعادة تعيين كاملة
window.resetDarkModeCompletely = function() {
    console.log('🔄 إعادة تعيين كاملة للوضع المظلم...');
    
    // حذف جميع المفاتيح المتعلقة بالثيم
    const themeKeys = Object.keys(localStorage).filter(key => 
        key.includes('theme') || key.includes('dark') || key.includes('mode')
    );
    
    themeKeys.forEach(key => {
        localStorage.removeItem(key);
        console.log(`🗑️ تم حذف ${key}`);
    });
    
    // إعادة تعيين إلى الوضع الفاتح
    document.documentElement.classList.remove('dark');
    localStorage.setItem('darkMode', 'false');
    
    // تحديث الأيقونة
    const icon = document.getElementById('darkModeIcon');
    if (icon) {
        icon.className = 'fas fa-moon theme-icon group-hover:rotate-180 transition-transform duration-500';
    }
    
    console.log('✅ تم إعادة التعيين إلى الوضع الفاتح');
    
    // إعادة تحميل الصفحة
    setTimeout(() => location.reload(), 1000);
};

console.log('🛠️ تم تحميل أدوات الإصلاح الطارئ للوضع المظلم');
console.log('💡 استخدم emergencyDarkModeFix() لإصلاح المشاكل');
console.log('💡 استخدم quickDarkModeDiagnosis() للتشخيص السريع');
console.log('💡 استخدم resetDarkModeCompletely() لإعادة التعيين الكاملة');
