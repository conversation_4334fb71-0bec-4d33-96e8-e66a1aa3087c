<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class EmployeeWithAdminAccessSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء مشرف وردية مع صلاحيات إدارية محدودة
        $supervisor = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'محمد',
                'last_name' => 'أحمد',
                'phone' => '+218912345001',
                'address' => 'طرابلس، ليبيا',
                'password' => Hash::make('supervisor123'),
                'user_type' => 'employee',
                'is_active' => true
            ]
        );

        // منح صلاحيات المشرف
        $supervisor->givePermissionTo([
            'dashboard.admin', // الوصول للوحة الإدارة
            'orders.view', 'orders.create', 'orders.edit', 'orders.status',
            'reservations.view', 'reservations.create', 'reservations.edit', 'reservations.status',
            'tables.view', 'tables.status',
            'payments.view', 'payments.create',
            'reports.view', 'reports.sales'
        ]);

        // إنشاء مدير مخزون مع صلاحيات إدارية
        $inventoryManager = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'فاطمة',
                'last_name' => 'محمد',
                'phone' => '+218912345002',
                'address' => 'بنغازي، ليبيا',
                'password' => Hash::make('inventory123'),
                'user_type' => 'employee',
                'is_active' => true
            ]
        );

        // منح صلاحيات مدير المخزون
        $inventoryManager->givePermissionTo([
            'dashboard.admin', // الوصول للوحة الإدارة
            'inventory.view', 'inventory.create', 'inventory.edit', 'inventory.export',
            'ingredients.view', 'ingredients.create', 'ingredients.edit',
            'menu.view', 'menu.edit',
            'reports.view', 'reports.inventory'
        ]);

        // إنشاء مدير مبيعات مع صلاحيات إدارية واسعة
        $salesManager = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'عبدالله',
                'last_name' => 'سالم',
                'phone' => '+218912345003',
                'address' => 'مصراتة، ليبيا',
                'password' => Hash::make('sales123'),
                'user_type' => 'employee',
                'is_active' => true
            ]
        );

        // منح صلاحيات مدير المبيعات
        $salesManager->givePermissionTo([
            'dashboard.admin', // الوصول للوحة الإدارة
            'orders.view', 'orders.create', 'orders.edit', 'orders.status',
            'menu.view', 'menu.create', 'menu.edit',
            'reports.view', 'reports.sales', 'reports.financial',
            'expenses.view', 'expenses.create',
            'notifications.view', 'notifications.create'
        ]);

        // إنشاء مدير مساعد مع صلاحيات شاملة (ما عدا إدارة الصلاحيات)
        $assistantManager = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'عائشة',
                'last_name' => 'علي',
                'phone' => '+218912345004',
                'address' => 'سبها، ليبيا',
                'password' => Hash::make('assistant123'),
                'user_type' => 'employee',
                'is_active' => true
            ]
        );

        // تعيين دور المدير المساعد (يحتوي على صلاحيات شاملة)
        $assistantManager->assignRole('manager');

        $this->command->info('تم إنشاء الموظفين مع صلاحيات الوصول للإدارة:');
        $this->command->line('1. مشرف الوردية: <EMAIL> / supervisor123');
        $this->command->line('2. مدير المخزون: <EMAIL> / inventory123');
        $this->command->line('3. مدير المبيعات: <EMAIL> / sales123');
        $this->command->line('4. المدير المساعد: <EMAIL> / assistant123');
        $this->command->info('جميع الموظفين يمكنهم الوصول للوحة الإدارة حسب صلاحياتهم');
    }
}
