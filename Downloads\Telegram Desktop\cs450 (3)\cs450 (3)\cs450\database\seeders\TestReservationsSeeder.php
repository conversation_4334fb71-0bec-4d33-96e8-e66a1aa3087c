<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Reservation;
use App\Models\User;
use App\Models\Table;
use Carbon\Carbon;

class TestReservationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // التأكد من وجود عملاء وطاولات
        $customers = User::where('user_type', 'customer')->take(3)->get();
        $tables = Table::take(3)->get();

        if ($customers->count() == 0 || $tables->count() == 0) {
            $this->command->info('لا توجد عملاء أو طاولات كافية لإنشاء حجوزات تجريبية');
            return;
        }

        // إنشاء حجوزات تجريبية
        $reservations = [
            [
                'user_id' => $customers[0]->user_id,
                'table_id' => $tables[0]->table_id,
                'reservation_time' => Carbon::now()->addHours(2),
                'duration' => 120,
                'party_size' => 4,
                'special_requests' => 'طاولة بجانب النافذة',
                'status' => 'confirmed',
            ],
            [
                'user_id' => $customers[1]->user_id,
                'table_id' => $tables[1]->table_id,
                'reservation_time' => Carbon::now()->addHours(4),
                'duration' => 90,
                'party_size' => 2,
                'special_requests' => 'حجز رومانسي',
                'status' => 'pending',
            ],
            [
                'user_id' => $customers[2]->user_id,
                'table_id' => $tables[2]->table_id,
                'reservation_time' => Carbon::now()->addDays(1)->setHour(19),
                'duration' => 180,
                'party_size' => 6,
                'special_requests' => 'احتفال عيد ميلاد',
                'status' => 'confirmed',
            ],
            [
                'user_id' => $customers[0]->user_id,
                'table_id' => $tables[0]->table_id,
                'reservation_time' => Carbon::now()->subHours(2),
                'duration' => 120,
                'party_size' => 3,
                'special_requests' => 'وجبة عائلية',
                'status' => 'completed',
            ],
        ];

        foreach ($reservations as $reservationData) {
            // التحقق من عدم وجود حجز مشابه
            $existing = Reservation::where('user_id', $reservationData['user_id'])
                ->where('table_id', $reservationData['table_id'])
                ->where('reservation_time', $reservationData['reservation_time'])
                ->first();
            
            if (!$existing) {
                Reservation::create($reservationData);
            }
        }

        $this->command->info('تم إنشاء حجوزات تجريبية بنجاح');
    }
}
