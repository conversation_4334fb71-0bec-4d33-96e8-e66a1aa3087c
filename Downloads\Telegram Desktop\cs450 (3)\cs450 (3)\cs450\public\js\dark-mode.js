// ملف منفصل للوضع المظلم
console.log('Dark mode script loaded');

// دالة تبديل الوضع المظلم
function toggleDarkMode() {
    console.log('toggleDarkMode called');
    
    const html = document.documentElement;
    const icon = document.getElementById('darkModeIcon');
    
    console.log('Current dark mode state:', html.classList.contains('dark'));
    console.log('Icon element:', icon);
    
    if (html.classList.contains('dark')) {
        // تبديل إلى الوضع الفاتح
        html.classList.remove('dark');
        if (icon) {
            icon.className = 'fas fa-moon';
        }
        localStorage.setItem('darkMode', 'false');
        console.log('Switched to light mode');
    } else {
        // تبديل إلى الوضع المظلم
        html.classList.add('dark');
        if (icon) {
            icon.className = 'fas fa-sun';
        }
        localStorage.setItem('darkMode', 'true');
        console.log('Switched to dark mode');
    }
}

// تحميل الإعدادات عند بدء الصفحة
function loadDarkModeSettings() {
    console.log('🔄 تحميل إعدادات الوضع المظلم...');

    const savedDarkMode = localStorage.getItem('darkMode');
    const html = document.documentElement;
    const icon = document.getElementById('darkModeIcon');

    console.log('🔍 الإعداد المحفوظ:', savedDarkMode);

    // دعم القيم القديمة والجديدة للتوافق
    if (savedDarkMode === 'true' || savedDarkMode === 'enabled') {
        html.classList.add('dark');
        if (icon) {
            icon.className = 'fas fa-sun';
        }
        // توحيد القيمة المحفوظة
        localStorage.setItem('darkMode', 'true');
        console.log('🌙 تم تطبيق الوضع المظلم من الإعدادات المحفوظة');
    } else if (savedDarkMode === 'false' || savedDarkMode === 'disabled') {
        html.classList.remove('dark');
        if (icon) {
            icon.className = 'fas fa-moon';
        }
        // توحيد القيمة المحفوظة
        localStorage.setItem('darkMode', 'false');
        console.log('☀️ تم تطبيق الوضع الفاتح من الإعدادات المحفوظة');
    } else {
        // لا يوجد إعداد محفوظ، استخدم تفضيلات النظام
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            html.classList.add('dark');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
            localStorage.setItem('darkMode', 'true');
            console.log('🌙 تم تطبيق الوضع المظلم من إعدادات النظام');
        } else {
            html.classList.remove('dark');
            if (icon) {
                icon.className = 'fas fa-moon';
            }
            localStorage.setItem('darkMode', 'false');
            console.log('☀️ تم تطبيق الوضع الفاتح كافتراضي');
        }
    }
}

// إعداد الزر عند تحميل الصفحة
function setupDarkModeButton() {
    console.log('Setting up dark mode button...');
    
    const button = document.getElementById('darkModeToggle');
    
    if (button) {
        console.log('Dark mode button found');
        
        // إزالة أي event listeners موجودة
        button.removeEventListener('click', toggleDarkMode);
        
        // إضافة event listener جديد
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Dark mode button clicked');
            toggleDarkMode();
        });
        
        console.log('Dark mode button setup complete');
    } else {
        console.error('Dark mode button not found!');
    }
}

// تشغيل الإعدادات عند تحميل DOM
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing dark mode...');
    loadDarkModeSettings();
    setupDarkModeButton();
});

// تشغيل الإعدادات عند تحميل كامل للصفحة
window.addEventListener('load', function() {
    console.log('Window loaded, re-checking dark mode...');
    loadDarkModeSettings();
    setupDarkModeButton();
});

// جعل الدالة متاحة عالمياً
window.toggleDarkMode = toggleDarkMode;
window.loadDarkModeSettings = loadDarkModeSettings;
window.setupDarkModeButton = setupDarkModeButton;

console.log('Dark mode script initialization complete');
