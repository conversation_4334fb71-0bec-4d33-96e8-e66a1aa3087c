@include('customer.partials.head')

<body class="bg-gray-50 dark:bg-gray-900">

@include('customer.partials.header')

<!-- المحتوى الرئيسي -->
<main class="min-h-screen pt-20">
    <div class="container mx-auto px-4 py-8">
        <!-- عنوان الصفحة -->
        <div class="flex items-center mb-8">
            <a href="{{ route('customer.reservations') }}"
               class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 p-2 rounded-lg transition ml-4">
                <i class="fas fa-arrow-right"></i>
            </a>
            <div>
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2">تفاصيل الحجز</h1>
                <p class="text-gray-600 dark:text-gray-400">حجز رقم #{{ $reservation->reservation_id }}</p>
            </div>
        </div>

        <div class="max-w-4xl mx-auto">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                <!-- حالة الحجز -->
                <div class="bg-gradient-to-r from-primary to-primary/80 text-white p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-2xl font-bold mb-2">حجز رقم #{{ $reservation->reservation_id }}</h2>
                            <p class="opacity-90">{{ \Carbon\Carbon::parse($reservation->reservation_time)->format('l, F j, Y') }}</p>
                        </div>
                        <div class="text-left">
                            <span class="inline-block px-4 py-2 rounded-full text-sm font-medium bg-white/20 backdrop-blur-sm">
                                @if($reservation->status == 'confirmed')
                                    <i class="fas fa-check-circle ml-1"></i>مؤكد
                                @elseif($reservation->status == 'pending')
                                    <i class="fas fa-clock ml-1"></i>في الانتظار
                                @elseif($reservation->status == 'canceled')
                                    <i class="fas fa-times-circle ml-1"></i>ملغي
                                @else
                                    {{ $reservation->status }}
                                @endif
                            </span>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الحجز -->
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <!-- معلومات التوقيت -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                                <i class="fas fa-calendar-alt text-primary ml-2"></i>
                                معلومات التوقيت
                            </h3>

                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-gray-600 dark:text-gray-400">التاريخ:</span>
                                    <span class="font-medium text-gray-800 dark:text-white">
                                        {{ \Carbon\Carbon::parse($reservation->reservation_time)->format('Y-m-d') }}
                                    </span>
                                </div>
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-gray-600 dark:text-gray-400">الوقت:</span>
                                    <span class="font-medium text-gray-800 dark:text-white">
                                        {{ \Carbon\Carbon::parse($reservation->reservation_time)->format('H:i') }}
                                    </span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">عدد الأشخاص:</span>
                                    <span class="font-medium text-gray-800 dark:text-white">
                                        {{ $reservation->party_size ?? 'غير محدد' }} أشخاص
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات الطاولة -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                                <i class="fas fa-chair text-primary ml-2"></i>
                                معلومات الطاولة
                            </h3>

                            @if($reservation->table)
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-gray-600 dark:text-gray-400">رقم الطاولة:</span>
                                    <span class="font-medium text-gray-800 dark:text-white">
                                        {{ $reservation->table->table_number }}
                                    </span>
                                </div>
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-gray-600 dark:text-gray-400">السعة:</span>
                                    <span class="font-medium text-gray-800 dark:text-white">
                                        {{ $reservation->table->capacity }} أشخاص
                                    </span>
                                </div>
                                @if($reservation->table->location)
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">الموقع:</span>
                                    <span class="font-medium text-gray-800 dark:text-white">
                                        {{ $reservation->table->location }}
                                    </span>
                                </div>
                                @endif
                            </div>
                            @else
                            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 p-4 rounded-lg">
                                <p class="text-yellow-800 dark:text-yellow-200">
                                    <i class="fas fa-exclamation-triangle ml-2"></i>
                                    لم يتم تحديد طاولة بعد
                                </p>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- معلومات العرض -->
                    @if($reservation->offer_title)
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                            <i class="fas fa-tag text-primary ml-2"></i>
                            معلومات العرض
                        </h3>
                        <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 p-4 rounded-lg">
                            <div class="space-y-3">
                                <div class="flex items-center">
                                    <i class="fas fa-gift text-green-600 ml-2"></i>
                                    <span class="font-medium text-green-800 dark:text-green-200">
                                        {{ $reservation->offer_title }}
                                    </span>
                                </div>
                                @if($reservation->contact_phone)
                                <div class="flex items-center">
                                    <i class="fas fa-phone text-green-600 ml-2"></i>
                                    <span class="text-green-700 dark:text-green-300">
                                        رقم التواصل: {{ $reservation->contact_phone }}
                                    </span>
                                </div>
                                @endif
                                @if($reservation->offer_slug)
                                <div class="flex items-center">
                                    <i class="fas fa-link text-green-600 ml-2"></i>
                                    <a href="{{ route('customer.offers.show', $reservation->offer_slug) }}"
                                       class="text-green-700 dark:text-green-300 hover:underline">
                                        عرض تفاصيل العرض
                                    </a>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- الملاحظات الخاصة -->
                    @if($reservation->special_requests)
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                            <i class="fas fa-sticky-note text-primary ml-2"></i>
                            الملاحظات الخاصة
                        </h3>
                        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 p-4 rounded-lg">
                            <p class="text-blue-800 dark:text-blue-200">{{ $reservation->special_requests }}</p>
                        </div>
                    </div>
                    @endif

                    <!-- معلومات إضافية -->
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                            <i class="fas fa-info-circle text-primary ml-2"></i>
                            معلومات إضافية
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600 dark:text-gray-400">تاريخ الحجز:</span>
                                <span class="text-gray-800 dark:text-white">
                                    {{ $reservation->created_at->format('Y-m-d H:i') }}
                                </span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600 dark:text-gray-400">آخر تحديث:</span>
                                <span class="text-gray-800 dark:text-white">
                                    {{ $reservation->updated_at->format('Y-m-d H:i') }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-6 mt-6">
                        <div class="flex flex-col sm:flex-row gap-4">
                            @if($reservation->status == 'pending' || $reservation->status == 'confirmed')
                                @if(\Carbon\Carbon::parse($reservation->reservation_time)->isFuture())
                                <form id="cancelReservationForm" action="{{ route('customer.reservations.cancel', $reservation->reservation_id) }}"
                                      method="POST"
                                      class="flex-1">
                                    @csrf
                                    @method('DELETE')
                                    <button type="button"
                                            onclick="showCancelModal()"
                                            class="w-full bg-red-500 hover:bg-red-600 text-white font-bold py-3 px-6 rounded-lg transition">
                                        <i class="fas fa-times ml-2"></i>
                                        إلغاء الحجز
                                    </button>
                                </form>
                                @endif
                            @endif

                            <a href="{{ route('customer.reservations') }}"
                               class="flex-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-bold py-3 px-6 rounded-lg transition text-center">
                                <i class="fas fa-list ml-2"></i>
                                العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد إلغاء الحجز -->
    <div id="cancelModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full transform transition-all duration-300 scale-95 opacity-0" id="cancelModalContent">
            <!-- رأس النافذة -->
            <div class="bg-gradient-to-r from-red-500 to-red-600 text-white p-6 rounded-t-2xl">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center ml-3">
                            <i class="fas fa-exclamation-triangle text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold">تأكيد إلغاء الحجز</h3>
                            <p class="text-red-100 text-sm">هذا الإجراء لا يمكن التراجع عنه</p>
                        </div>
                    </div>
                    <button onclick="hideCancelModal()" class="text-white hover:text-red-200 transition">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>

            <!-- محتوى النافذة -->
            <div class="p-6">
                <!-- معلومات الحجز -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                    <h4 class="font-bold text-gray-800 dark:text-white mb-3 flex items-center">
                        <i class="fas fa-info-circle text-blue-500 ml-2"></i>
                        تفاصيل الحجز
                    </h4>
                    <div class="space-y-2 text-sm">
                        <div class="flex items-center justify-between py-1">
                            <span class="text-gray-600 dark:text-gray-400">رقم الحجز:</span>
                            <span class="font-semibold text-gray-800 dark:text-white">#{{ $reservation->reservation_id }}</span>
                        </div>
                        <div class="flex items-center justify-between py-1">
                            <span class="text-gray-600 dark:text-gray-400">التاريخ والوقت:</span>
                            <span class="font-semibold text-gray-800 dark:text-white">{{ \Carbon\Carbon::parse($reservation->reservation_time)->format('d M Y - H:i') }}</span>
                        </div>
                        <div class="flex items-center justify-between py-1">
                            <span class="text-gray-600 dark:text-gray-400">رقم الطاولة:</span>
                            <span class="font-semibold text-gray-800 dark:text-white">#{{ $reservation->table_id }}</span>
                        </div>
                        <div class="flex items-center justify-between py-1">
                            <span class="text-gray-600 dark:text-gray-400">عدد الأشخاص:</span>
                            <span class="font-semibold text-gray-800 dark:text-white">{{ $reservation->party_size }} أشخاص</span>
                        </div>
                    </div>
                </div>

                <!-- تحذير -->
                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
                    <div class="flex items-start">
                        <i class="fas fa-exclamation-triangle text-red-500 mt-1 ml-2"></i>
                        <div>
                            <h5 class="font-bold text-red-800 dark:text-red-200 mb-1">تنبيه مهم</h5>
                            <p class="text-red-700 dark:text-red-300 text-sm">
                                بعد إلغاء الحجز، لن تتمكن من استرداده. إذا كنت تريد تعديل الحجز بدلاً من إلغائه، يمكنك الضغط على "تعديل الحجز" بدلاً من الإلغاء.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="bg-gray-50 dark:bg-gray-700 px-6 py-4 rounded-b-2xl flex flex-col sm:flex-row gap-3 justify-end">
                <button onclick="hideCancelModal()"
                        class="px-6 py-3 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium rounded-lg transition flex items-center justify-center">
                    <i class="fas fa-times ml-2"></i>
                    إلغاء العملية
                </button>
                <button onclick="proceedWithCancel()"
                        id="confirmCancelBtn"
                        class="px-6 py-3 bg-red-500 hover:bg-red-600 text-white font-bold rounded-lg transition flex items-center justify-center">
                    <i class="fas fa-trash ml-2"></i>
                    تأكيد الإلغاء
                </button>
            </div>
        </div>
    </div>
</main>

@include('customer.partials.footer')
@include('customer.partials.scripts')

<script>
function showCancelModal() {
    const modal = document.getElementById('cancelModal');
    const content = document.getElementById('cancelModalContent');

    modal.classList.remove('hidden');
    setTimeout(() => {
        content.classList.remove('scale-95', 'opacity-0');
        content.classList.add('scale-100', 'opacity-100');
    }, 10);
}

function hideCancelModal() {
    const modal = document.getElementById('cancelModal');
    const content = document.getElementById('cancelModalContent');

    content.classList.remove('scale-100', 'opacity-100');
    content.classList.add('scale-95', 'opacity-0');

    setTimeout(() => {
        modal.classList.add('hidden');
    }, 300);
}

function proceedWithCancel() {
    const form = document.getElementById('cancelReservationForm');
    const button = document.getElementById('confirmCancelBtn');

    // تغيير شكل الزر لإظهار التحميل
    button.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري الإلغاء...';
    button.disabled = true;
    button.classList.add('opacity-50');

    // إرسال طلب AJAX بدلاً من إرسال النموذج
    const formData = new FormData(form);

    fetch(form.action, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إخفاء النافذة
            hideCancelModal();

            // إظهار رسالة نجاح
            showSuccessMessage('تم إلغاء الحجز بنجاح');

            // إعادة توجيه بعد ثانيتين
            setTimeout(() => {
                window.location.href = '/customer/reservations';
            }, 2000);
        } else {
            // إظهار رسالة خطأ
            showErrorMessage(data.message || 'حدث خطأ أثناء إلغاء الحجز');

            // إعادة تعيين الزر
            button.innerHTML = '<i class="fas fa-trash ml-2"></i>تأكيد الإلغاء';
            button.disabled = false;
            button.classList.remove('opacity-50');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorMessage('حدث خطأ في الاتصال');

        // إعادة تعيين الزر
        button.innerHTML = '<i class="fas fa-trash ml-2"></i>تأكيد الإلغاء';
        button.disabled = false;
        button.classList.remove('opacity-50');
    });
}

// إغلاق النافذة عند النقر خارجها
document.getElementById('cancelModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideCancelModal();
    }
});

// إغلاق النافذة بمفتاح Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        hideCancelModal();
    }
});

// دالة إظهار رسالة النجاح
function showSuccessMessage(message) {
    // إنشاء عنصر الرسالة
    const messageDiv = document.createElement('div');
    messageDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-4 rounded-lg shadow-lg z-50 flex items-center';
    messageDiv.innerHTML = `
        <i class="fas fa-check-circle text-xl ml-3"></i>
        <span>${message}</span>
    `;

    // إضافة الرسالة للصفحة
    document.body.appendChild(messageDiv);

    // إزالة الرسالة بعد 3 ثوان
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}

// دالة إظهار رسالة الخطأ
function showErrorMessage(message) {
    // إنشاء عنصر الرسالة
    const messageDiv = document.createElement('div');
    messageDiv.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-4 rounded-lg shadow-lg z-50 flex items-center';
    messageDiv.innerHTML = `
        <i class="fas fa-exclamation-circle text-xl ml-3"></i>
        <span>${message}</span>
    `;

    // إضافة الرسالة للصفحة
    document.body.appendChild(messageDiv);

    // إزالة الرسالة بعد 5 ثوان
    setTimeout(() => {
        messageDiv.remove();
    }, 5000);
}
</script>

</body>
</html>
