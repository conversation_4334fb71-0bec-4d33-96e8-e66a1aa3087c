{{-- 
    مثال على استخدام نظام الصلاحيات الجديد في Blade Templates
    هذا مثال توضيحي فقط - ليس للاستخدام المباشر
--}}

@extends('layouts.admin')

@section('title', 'لوحة التحكم')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">لوحة التحكم</h1>
        </div>
    </div>

    {{-- قسم خاص بالمديرين فقط --}}
    @can('is-admin')
        <div class="alert alert-info">
            <i class="fas fa-crown"></i>
            مرحباً بك أيها المدير! لديك صلاحيات كاملة في النظام.
        </div>
    @endcan

    {{-- إحصائيات عامة --}}
    <div class="row mb-4">
        {{-- إحصائيات الطلبات (للجميع) --}}
        @can('orders.view')
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4>{{ $totalOrders ?? 0 }}</h4>
                                <p class="mb-0">إجمالي الطلبات</p>
                                @can('full-data-access')
                                    <small>(جميع الطلبات)</small>
                                @else
                                    <small>(طلباتك فقط)</small>
                                @endcan
                            </div>
                            <div>
                                <i class="fas fa-shopping-cart fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endcan

        {{-- إحصائيات المستخدمين (للمديرين فقط) --}}
        @can('manage-users')
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4>{{ $totalUsers ?? 0 }}</h4>
                                <p class="mb-0">إجمالي المستخدمين</p>
                            </div>
                            <div>
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endcan

        {{-- الإيرادات (للمديرين فقط) --}}
        @can('access-all-reports')
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4>${{ number_format($totalRevenue ?? 0, 2) }}</h4>
                                <p class="mb-0">إجمالي الإيرادات</p>
                            </div>
                            <div>
                                <i class="fas fa-dollar-sign fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endcan

        {{-- إحصائيات أخرى (للجميع حسب الصلاحيات) --}}
        @can('inventory.view')
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4>{{ $lowStockItems ?? 0 }}</h4>
                                <p class="mb-0">عناصر منخفضة المخزون</p>
                            </div>
                            <div>
                                <i class="fas fa-boxes fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endcan
    </div>

    {{-- أزرار الإجراءات السريعة --}}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">الإجراءات السريعة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {{-- إضافة طلب جديد --}}
                        @can('orders.create')
                            <div class="col-md-2 mb-3">
                                <a href="{{ route('admin.orders.create') }}" class="btn btn-primary btn-block">
                                    <i class="fas fa-plus"></i><br>
                                    طلب جديد
                                </a>
                            </div>
                        @endcan

                        {{-- إدارة المستخدمين (للمديرين فقط) --}}
                        @can('manage-users')
                            <div class="col-md-2 mb-3">
                                <a href="{{ route('admin.users.index') }}" class="btn btn-success btn-block">
                                    <i class="fas fa-users"></i><br>
                                    إدارة المستخدمين
                                </a>
                            </div>
                        @endcan

                        {{-- إدارة القائمة --}}
                        @can('menu.view')
                            <div class="col-md-2 mb-3">
                                <a href="{{ route('admin.menu.index') }}" class="btn btn-warning btn-block">
                                    <i class="fas fa-utensils"></i><br>
                                    إدارة القائمة
                                </a>
                            </div>
                        @endcan

                        {{-- التقارير --}}
                        @can('reports.view')
                            <div class="col-md-2 mb-3">
                                <a href="{{ route('admin.reports.index') }}" class="btn btn-info btn-block">
                                    <i class="fas fa-chart-bar"></i><br>
                                    التقارير
                                </a>
                            </div>
                        @endcan

                        {{-- إدارة المخزون --}}
                        @can('inventory.view')
                            <div class="col-md-2 mb-3">
                                <a href="{{ route('admin.inventory.index') }}" class="btn btn-secondary btn-block">
                                    <i class="fas fa-boxes"></i><br>
                                    إدارة المخزون
                                </a>
                            </div>
                        @endcan

                        {{-- الإعدادات (للمديرين فقط) --}}
                        @can('is-admin')
                            <div class="col-md-2 mb-3">
                                <a href="{{ route('admin.settings.index') }}" class="btn btn-dark btn-block">
                                    <i class="fas fa-cog"></i><br>
                                    الإعدادات
                                </a>
                            </div>
                        @endcan
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- جدول الطلبات الحديثة --}}
    @can('orders.view')
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            الطلبات الحديثة
                            @can('full-data-access')
                                <small class="text-muted">(جميع الطلبات)</small>
                            @else
                                <small class="text-muted">(طلباتك فقط)</small>
                            @endcan
                        </h5>
                        <a href="{{ route('admin.orders.index') }}" class="btn btn-sm btn-outline-primary">
                            عرض الكل
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>العميل</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($recentOrders ?? [] as $order)
                                        <tr>
                                            <td>#{{ $order->id }}</td>
                                            <td>{{ $order->user->first_name ?? 'غير محدد' }}</td>
                                            <td>${{ number_format($order->total_amount, 2) }}</td>
                                            <td>
                                                <span class="badge badge-{{ $order->status == 'completed' ? 'success' : 'warning' }}">
                                                    {{ $order->status }}
                                                </span>
                                            </td>
                                            <td>{{ $order->created_at->format('Y-m-d H:i') }}</td>
                                            <td>
                                                {{-- عرض تفاصيل الطلب --}}
                                                @can('orders.view')
                                                    <a href="{{ route('admin.orders.show', $order) }}" 
                                                       class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                @endcan

                                                {{-- تعديل الطلب --}}
                                                @can('orders.edit')
                                                    {{-- المديرون يمكنهم تعديل أي طلب --}}
                                                    @if(Gate::allows('edit-any-data') || $order->user_id == auth()->id())
                                                        <a href="{{ route('admin.orders.edit', $order) }}" 
                                                           class="btn btn-sm btn-outline-warning">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    @endif
                                                @endcan

                                                {{-- حذف الطلب --}}
                                                @can('orders.delete')
                                                    {{-- المديرون يمكنهم حذف أي طلب --}}
                                                    @if(Gate::allows('delete-any-data') || $order->user_id == auth()->id())
                                                        <button type="button" 
                                                                class="btn btn-sm btn-outline-danger"
                                                                onclick="deleteOrder({{ $order->id }})">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    @endif
                                                @endcan
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="6" class="text-center">لا توجد طلبات</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endcan

    {{-- قسم خاص بالمديرين فقط --}}
    @can('is-admin')
        <div class="row mt-4">
            <div class="col-12">
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-shield-alt"></i>
                            منطقة المدير
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>هذا القسم مرئي للمديرين فقط. يمكنك هنا:</p>
                        <ul>
                            <li>إدارة صلاحيات المستخدمين</li>
                            <li>الوصول للتقارير المالية الحساسة</li>
                            <li>تعديل إعدادات النظام</li>
                            <li>حذف أي بيانات في النظام</li>
                        </ul>
                        
                        <div class="mt-3">
                            <a href="{{ route('admin.permissions.index') }}" class="btn btn-danger">
                                <i class="fas fa-key"></i>
                                إدارة الصلاحيات
                            </a>
                            <a href="{{ route('admin.reports.financial') }}" class="btn btn-warning">
                                <i class="fas fa-chart-line"></i>
                                التقارير المالية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endcan
</div>

{{-- JavaScript للتفاعل --}}
@push('scripts')
<script>
function deleteOrder(orderId) {
    if (confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
        // إرسال طلب حذف
        fetch(`/admin/orders/${orderId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء حذف الطلب');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف الطلب');
        });
    }
}
</script>
@endpush
@endsection

{{--
    ملاحظات مهمة حول استخدام الصلاحيات في Blade:

    1. @can('permission') - للتحقق من صلاحية محددة
    2. @cannot('permission') - للتحقق من عدم وجود صلاحية
    3. @can('is-admin') - للتحقق من كون المستخدم مدير
    4. @can('full-data-access') - للوصول الكامل للبيانات
    5. Gate::allows('gate-name') - لاستخدام Gates مخصصة
    6. المديرون يمرون من جميع فحوصات @can تلقائياً
    7. استخدم الشروط المركبة للمنطق المعقد
--}}
