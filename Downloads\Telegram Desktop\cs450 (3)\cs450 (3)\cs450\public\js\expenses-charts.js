// Charts for expenses report (Chart.js)
(function () {
    function toNumber(v) {
        var n = parseFloat(v);
        return isNaN(n) ? 0 : n;
    }

    // Map category keys to Arabic names
    var catMap = {
        ingredients: "المكونات",
        utilities: "المرافق",
        salaries: "المرتبات",
        maintenance: "الصيانة",
        rent: "الإيجار",
        taxes: "الضرائب",
        fees: "الرسوم",
        purchase: "شراء المخزون",
        purchase_auto: "شراء المخزون (تلقائي)",
        other: "أخرى",
        others: "أخرى",
    };
    function t(cat) {
        if (!cat) return "غير محدد";
        var raw = String(cat).trim();
        var k = raw.toLowerCase();
        if (catMap[k]) return catMap[k];
        var isArabic = /[\u0600-\u06FF]/.test(raw);
        return isArabic ? raw : "أخرى";
    }

    function drawDailyChart(ctx, data) {
        var labels = Array.isArray(data)
            ? data.map(function (i) {
                  return i.date;
              })
            : [];
        var values = Array.isArray(data)
            ? data.map(function (i) {
                  return toNumber(i.total || i.amount || 0);
              })
            : [];
        if (labels.length === 0) {
            labels = [
                "2025-08-15",
                "2025-08-16",
                "2025-08-17",
                "2025-08-18",
                "2025-08-19",
            ];
            values = [120, 85, 200, 150, 300];
        }

        return new Chart(ctx, {
            type: "line",
            data: {
                labels: labels,
                datasets: [
                    {
                        label: "المصروفات اليومية",
                        data: values,
                        borderColor: "#EF4444",
                        backgroundColor: "rgba(239,68,68,0.2)",
                        tension: 0.3,
                    },
                ],
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        callbacks: {
                            label: function (c) {
                                return c.formattedValue + " د.ل";
                            },
                        },
                    },
                },
                scales: { y: { ticks: { callback: (v) => v + " د.ل" } } },
            },
        });
    }

    function drawCategoryChart(ctx, data) {
        var labels = Array.isArray(data)
            ? data.map(function (i) {
                  return t(i.category || i.name);
              })
            : [];
        var values = Array.isArray(data)
            ? data.map(function (i) {
                  return toNumber(i.total || i.amount);
              })
            : [];
        if (labels.length === 0) {
            labels = ["المكونات", "المرافق", "المرتبات", "الصيانة"];
            values = [450, 200, 800, 150];
        }

        return new Chart(ctx, {
            type: "doughnut",
            data: {
                labels: labels,
                datasets: [
                    {
                        data: values,
                        backgroundColor: [
                            "#EF4444",
                            "#10B981",
                            "#F59E0B",
                            "#3B82F6",
                            "#8B5CF6",
                            "#06B6D4",
                            "#F472B6",
                            "#84CC16",
                        ],
                    },
                ],
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: "bottom" },
                    tooltip: {
                        callbacks: { label: (c) => c.formattedValue + " د.ل" },
                    },
                },
            },
        });
    }

    window.initExpensesChartsChartJS = function () {
        try {
            var daily =
                window.expensesData && Array.isArray(window.expensesData.daily)
                    ? window.expensesData.daily
                    : [];
            var cat =
                window.expensesData &&
                Array.isArray(window.expensesData.category)
                    ? window.expensesData.category
                    : [];

            var dailyCanvas = document.getElementById("dailyExpensesCanvas");
            var catCanvas = document.getElementById("categoryExpensesCanvas");

            if (dailyCanvas && window.Chart) {
                drawDailyChart(dailyCanvas.getContext("2d"), daily);
            }
            if (catCanvas && window.Chart) {
                drawCategoryChart(catCanvas.getContext("2d"), cat);
            }
        } catch (e) {
            console.error("Expenses charts init error", e);
        }
    };
})();
