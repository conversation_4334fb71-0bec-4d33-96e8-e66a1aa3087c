@extends('layouts.admin')

@section('title', 'تفاصيل الطلب')

@section('content')
<div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <div>
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white">تفاصيل الطلب #{{ $order->order_id }}</h2>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">تاريخ الطلب: {{ $order->created_at->format('Y-m-d H:i') }}</p>
    </div>
    <div class="mt-4 md:mt-0 flex space-x-2 space-x-reverse">
        <a href="{{ route('admin.orders.edit', $order->order_id) }}" class="bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-edit ml-2"></i>
            <span>تعديل الطلب</span>
        </a>
        <a href="{{ route('admin.orders') }}" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-arrow-right ml-2"></i>
            <span>العودة للطلبات</span>
        </a>
    </div>
</div>

<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">معلومات العميل</h3>
        </div>
        <div class="p-4">
            @if($order->user)
                <div class="mb-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">الاسم</p>
                    <p class="text-base font-medium text-gray-800 dark:text-white">{{ $order->user->first_name }} {{ $order->user->last_name }}</p>
                </div>
                <div class="mb-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">البريد الإلكتروني</p>
                    <p class="text-base font-medium text-gray-800 dark:text-white">{{ $order->user->email }}</p>
                </div>
                <div>
                    <p class="text-sm text-gray-500 dark:text-gray-400">رقم الهاتف</p>
                    <p class="text-base font-medium text-gray-800 dark:text-white">{{ $order->user->phone ?? 'غير متوفر' }}</p>
                </div>
                <div class="mt-2">
                    <p class="text-sm text-gray-500 dark:text-gray-400">نوع العميل</p>
                    <p class="text-base font-medium text-gray-800 dark:text-white">عميل مسجل</p>
                </div>
            @else
                <div class="mb-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">الاسم</p>
                    <p class="text-base font-medium text-gray-800 dark:text-white">{{ $order->customer_name ?? 'زبون غير مسجل' }}</p>
                </div>
                <div>
                    <p class="text-sm text-gray-500 dark:text-gray-400">رقم الهاتف</p>
                    <p class="text-base font-medium text-gray-800 dark:text-white">{{ $order->customer_phone ?? 'غير متوفر' }}</p>
                </div>
                <div class="mt-2">
                    <p class="text-sm text-gray-500 dark:text-gray-400">نوع العميل</p>
                    <p class="text-base font-medium text-gray-800 dark:text-white">زبون عادي (غير مسجل)</p>
                </div>
            @endif
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">معلومات الطلب</h3>
        </div>
        <div class="p-4">
            <div class="mb-4">
                <p class="text-sm text-gray-500 dark:text-gray-400">رقم الطلب</p>
                <p class="text-base font-medium text-gray-800 dark:text-white">#{{ $order->order_id }}</p>
            </div>
            <div class="mb-4">
                <p class="text-sm text-gray-500 dark:text-gray-400">المبلغ الإجمالي</p>
                <p class="text-base font-medium text-gray-800 dark:text-white">{{ number_format($order->total_amount, 2) }} د.ل</p>
            </div>
            <div>
                <p class="text-sm text-gray-500 dark:text-gray-400">الطاولة</p>
                <p class="text-base font-medium text-gray-800 dark:text-white">{{ $order->table ? 'طاولة ' . $order->table->table_number : 'طلب خارجي' }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">حالة الطلب</h3>
        </div>
        <div class="p-4">
            <div class="mb-4">
                <p class="text-sm text-gray-500 dark:text-gray-400">الحالة الحالية</p>
                <p class="inline-flex px-2 py-1 text-sm font-semibold rounded-full
                    @if($order->status == 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100
                    @elseif($order->status == 'preparing') bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100
                    @elseif($order->status == 'completed') bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                    @elseif($order->status == 'canceled') bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                    @endif">
                    @if($order->status == 'pending') قيد الانتظار
                    @elseif($order->status == 'preparing') قيد التحضير
                    @elseif($order->status == 'completed') مكتمل
                    @elseif($order->status == 'canceled') ملغي
                    @endif
                </p>
            </div>
            <div>
                <form action="{{ route('admin.orders.update-status', $order->order_id) }}" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="mb-4">
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تغيير الحالة</label>
                        <select id="status" name="status" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                            <option value="pending" {{ $order->status == 'pending' ? 'selected' : '' }}>قيد الانتظار</option>
                            <option value="preparing" {{ $order->status == 'preparing' ? 'selected' : '' }}>قيد التحضير</option>
                            <option value="completed" {{ $order->status == 'completed' ? 'selected' : '' }}>مكتمل</option>
                            <option value="canceled" {{ $order->status == 'canceled' ? 'selected' : '' }}>ملغي</option>
                        </select>
                    </div>
                    <button type="submit" class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md transition-all">
                        تحديث الحالة
                    </button>
                    @if(!$order->user)
                        <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                            <i class="fas fa-info-circle ml-1"></i>
                            ملاحظة: لن يتم إرسال إشعار لأن هذا طلب لزبون غير مسجل
                        </div>
                    @endif
                </form>
            </div>
        </div>
    </div>
</div>

<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">عناصر الطلب</h3>
    </div>
    <div class="overflow-x-auto">
        <table class="w-full">
            <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">العنصر</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الكمية</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">السعر</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المجموع</th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                @foreach($order->items as $item)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                {{ $item->menuItem->name }}
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                {{ $item->menuItem->category }}
                            </div>
                            @if($item->excluded_ingredients && count($item->excluded_ingredients) > 0)
                            <div class="text-xs text-red-600 dark:text-red-400 mt-1 flex items-center">
                                <i class="fas fa-ban ml-1"></i>
                                <span class="font-medium">بدون:</span>
                                @php
                                    $excludedNames = \App\Models\Ingredient::whereIn('ingredient_id', $item->excluded_ingredients)->pluck('name')->toArray();
                                @endphp
                                <span class="mr-1">{{ implode('، ', $excludedNames) }}</span>
                            </div>
                            @endif
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-500 dark:text-gray-300">
                                {{ $item->quantity }}
                            </div>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-500 dark:text-gray-300">
                                {{ number_format($item->price, 2) }} د.ل
                            </div>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-500 dark:text-gray-300">
                                {{ number_format($item->price * $item->quantity, 2) }} د.ل
                            </div>
                        </td>
                    </tr>
                @endforeach
            </tbody>
            <tfoot class="bg-gray-50 dark:bg-gray-700">
                <tr>
                    <td colspan="3" class="px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-white">
                        المجموع الكلي
                    </td>
                    <td class="px-4 py-3 text-right text-sm font-bold text-gray-900 dark:text-white">
                        {{ number_format($order->total_amount, 2) }} د.ل
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>
</div>
@endsection
