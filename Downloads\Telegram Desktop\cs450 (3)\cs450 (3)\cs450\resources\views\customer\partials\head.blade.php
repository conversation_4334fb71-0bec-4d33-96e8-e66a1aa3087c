<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Eat Hub - تجربة طعام لا تنسى')</title>

    <!-- النظام الموحد للوضع المظلم -->
    <script src="{{ asset('js/unified-dark-mode.js') }}"></script>

    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#FF6B35',      // برتقالي محمر
                        secondary: '#4CAF50',    // أخضر للدلالة على الطازج
                        accent: '#FFEB3B',       // أصفر للتنبيهات والعروض
                        warmBrown: '#C8A080',    // بني دافئ للراحة
                        darkText: '#333333',     // للنصوص الداكنة
                    },
                    fontFamily: {
                        sans: ['Cairo', 'sans-serif'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.7s ease-in-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'bounce-light': 'bounceLight 3s infinite',
                        'float': 'float 3s ease-in-out infinite'
                    },
                    keyframes: {
                        fadeIn: {
                          '0%': { opacity: '0' },
                          '100%': { opacity: '1' }
                        },
                        slideUp: {
                          '0%': { transform: 'translateY(20px)', opacity: '0' },
                          '100%': { transform: 'translateY(0)', opacity: '1' }
                        },
                        bounceLight: {
                          '0%, 100%': { transform: 'translateY(0)' },
                          '50%': { transform: 'translateY(-10px)' }
                        },
                        float: {
                          '0%, 100%': { transform: 'translateY(0)' },
                          '50%': { transform: 'translateY(-10px)' }
                        }
                    }
                }
            }
        };

        // التحقق من الوضع المظلم (فقط إذا لم يكن محفوظ)
        if (!localStorage.getItem('darkMode')) {
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                document.documentElement.classList.add('dark');
            }
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (!localStorage.getItem('darkMode')) {
                if (event.matches) {
                    document.documentElement.classList.add('dark');
                } else {
                    document.documentElement.classList.remove('dark');
                }
            }
        });
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="{{ asset('css/rating-system.css') }}" rel="stylesheet">
    @include('customer.partials.styles')

    <!-- ملف الوضع المظلم المنفصل -->
    <script src="{{ asset('js/dark-mode.js') }}"></script>

    <!-- حل بديل مباشر للوضع المظلم -->
    <script>
        // حل مباشر وبسيط للوضع المظلم
        function simpleDarkModeToggle() {
            const html = document.documentElement;
            const icon = document.getElementById('darkModeIcon');

            if (html.classList.contains('dark')) {
                html.classList.remove('dark');
                if (icon) icon.className = 'fas fa-moon';
                localStorage.setItem('darkMode', 'false');
            } else {
                html.classList.add('dark');
                if (icon) icon.className = 'fas fa-sun';
                localStorage.setItem('darkMode', 'true');
            }
        }

        // جعل الدالة متاحة عالمياً
        window.simpleDarkModeToggle = simpleDarkModeToggle;

        // تطبيق الإعدادات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const saved = localStorage.getItem('darkMode');
            const html = document.documentElement;
            const icon = document.getElementById('darkModeIcon');

            if (saved === 'true') {
                html.classList.add('dark');
                if (icon) icon.className = 'fas fa-sun';
            } else {
                html.classList.remove('dark');
                if (icon) icon.className = 'fas fa-moon';
            }
        });
    </script>
</head>
<body class="bg-gray-100 dark:bg-gray-900 min-h-screen">
