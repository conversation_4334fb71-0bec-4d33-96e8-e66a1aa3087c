@extends('auth.layouts.auth')

@section('title', 'تسجيل الدخول')

@section('content')
<div id="authContainer" class="w-full max-w-md fade-in">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-primary mb-2">Eat Hub</h1>
            <p class="text-gray-600 dark:text-gray-400">مرحباً بك مجدداً</p>
        </div>

        @if(session('error'))
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                <span class="block sm:inline">{{ session('error') }}</span>
            </div>
        @endif

        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
                <span class="block sm:inline">{{ session('success') }}</span>
            </div>
        @endif

        <form action="{{ route('login') }}{{ request()->has('redirect') ? '?redirect='.request()->query('redirect') : '' }}" method="POST">
            @csrf
            @if(request()->has('redirect'))
                <input type="hidden" name="redirect" value="{{ request()->query('redirect') }}">
            @endif
            <div class="mb-6">
                <label for="email" class="block text-gray-700 dark:text-gray-300 mb-2">البريد الإلكتروني</label>
                <div class="relative">
                    <span class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 dark:text-gray-400">
                        <i class="fas fa-envelope"></i>
                    </span>
                    <input type="email" id="email" name="email" value="{{ old('email') }}" class="w-full px-4 py-3 pr-10 border rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-white border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-primary text-base" placeholder="أدخل بريدك الإلكتروني" required>
                </div>
                @error('email')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <div class="flex justify-between items-center mb-2">
                    <label for="password" class="block text-gray-700 dark:text-gray-300">كلمة المرور</label>
                    <a href="{{ route('password.request') }}" class="text-sm text-primary hover:underline">
                        نسيت كلمة المرور؟
                    </a>
                </div>
                <div class="relative">
                    <span class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 dark:text-gray-400">
                        <i class="fas fa-lock"></i>
                    </span>
                    <input type="password" id="password" name="password" class="w-full px-4 py-3 pr-10 border rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-white border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-primary text-base" placeholder="أدخل كلمة المرور" required>
                    <button type="button" class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500 dark:text-gray-400" id="togglePassword">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                @error('password')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="flex items-center mb-6">
                <input type="checkbox" id="remember" name="remember" class="w-5 h-5 text-primary bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-primary">
                <label for="remember" class="mr-2 text-gray-700 dark:text-gray-300">تذكرني</label>
            </div>

            <button type="submit" class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-3 px-4 rounded-lg transition duration-200">
                تسجيل الدخول
            </button>
        </form>

        <!-- فاصل -->
        <div class="mt-6 mb-6">
            <div class="relative">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
                </div>
                <div class="relative flex justify-center text-sm">
                    <span class="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">أو تسجيل الدخول بـ</span>
                </div>
            </div>
        </div>

        <!-- أزرار تسجيل الدخول بالحسابات الاجتماعية -->
        <div class="space-y-3">
            <!-- Google -->
            <a href="{{ route('auth.google') }}" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 transition duration-200 group">
                <svg class="w-5 h-5 ml-2" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                <span class="group-hover:text-gray-900 dark:group-hover:text-white">تسجيل الدخول بـ Google</span>
            </a>

            <!-- Facebook -->
            <a href="{{ route('auth.facebook') }}" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 transition duration-200 group">
                <svg class="w-5 h-5 ml-2" fill="#1877F2" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
                <span class="group-hover:text-gray-900 dark:group-hover:text-white">تسجيل الدخول بـ Facebook</span>
            </a>

            <!-- Apple -->
            <a href="{{ route('auth.apple') }}" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 transition duration-200 group">
                <svg class="w-5 h-5 ml-2" fill="#000000" viewBox="0 0 24 24" class="dark:fill-white">
                    <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                </svg>
                <span class="group-hover:text-gray-900 dark:group-hover:text-white">تسجيل الدخول بـ Apple</span>
            </a>
        </div>

        <div class="mt-6 text-center">
            <p class="text-gray-700 dark:text-gray-300">
                ليس لديك حساب؟
                <a href="{{ route('register') }}{{ request()->has('redirect') ? '?redirect='.request()->query('redirect') : '' }}" class="text-primary hover:underline font-medium">إنشاء حساب جديد</a>
            </p>
            @if(app()->environment('local'))
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                <a href="{{ route('auth.test') }}" class="hover:underline">🔧 اختبار OAuth (للمطورين)</a>
            </p>
            @endif
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // إظهار وإخفاء كلمة المرور
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordInput = document.getElementById('password');
        const icon = this.querySelector('i');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });

    // تأثيرات بصرية للأزرار الاجتماعية
    document.querySelectorAll('a[href*="auth/"]').forEach(button => {
        button.addEventListener('click', function(e) {
            // إضافة تأثير تحميل
            const originalText = this.querySelector('span').textContent;
            this.querySelector('span').textContent = 'جاري التحميل...';
            this.classList.add('opacity-75', 'cursor-wait');

            // في حالة فشل التحميل، استعادة النص الأصلي بعد 5 ثوان
            setTimeout(() => {
                this.querySelector('span').textContent = originalText;
                this.classList.remove('opacity-75', 'cursor-wait');
            }, 5000);
        });
    });
</script>
@endsection