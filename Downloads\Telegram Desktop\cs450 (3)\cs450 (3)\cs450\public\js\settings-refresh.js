// نظام تحديث الإعدادات البسيط
(function() {
    'use strict';
    
    let currentSettings = {};
    
    // تحميل الإعدادات من الخادم
    async function loadSettings() {
        try {
            const response = await fetch('/api/settings');
            if (response.ok) {
                const newSettings = await response.json();
                
                // التحقق من تغيير الإعدادات
                if (JSON.stringify(newSettings) !== JSON.stringify(currentSettings)) {
                    console.log('🔄 تم اكتشاف تغيير في الإعدادات');
                    currentSettings = newSettings;
                    updatePage();
                }
            }
        } catch (error) {
            console.error('خطأ في تحميل الإعدادات:', error);
        }
    }
    
    // تحديث الصفحة بالإعدادات الجديدة
    function updatePage() {
        // تحديث نسبة الضريبة في النصوص
        updateTaxRateTexts();
        
        // إعادة حساب المبالغ
        recalculateAmounts();
        
        console.log('✅ تم تحديث الصفحة بالإعدادات الجديدة');
    }
    
    // تحديث نصوص نسبة الضريبة
    function updateTaxRateTexts() {
        const taxRate = parseFloat(currentSettings.tax_rate) || 15;
        
        // تحديث النصوص التي تحتوي على نسبة الضريبة
        document.querySelectorAll('[data-tax-rate]').forEach(element => {
            const text = element.textContent;
            const newText = text.replace(/\d+%/, `${taxRate}%`);
            element.textContent = newText;
        });
        
        // تحديث النصوص العامة
        document.querySelectorAll('*').forEach(element => {
            if (element.children.length === 0) { // نص فقط
                const text = element.textContent;
                if (text.includes('الضريبة (') && text.includes('%)')) {
                    const newText = text.replace(/الضريبة \(\d+%\)/, `الضريبة (${taxRate}%)`);
                    if (newText !== text) {
                        element.textContent = newText;
                    }
                }
            }
        });
    }
    
    // إعادة حساب المبالغ
    function recalculateAmounts() {
        const taxRate = parseFloat(currentSettings.tax_rate) || 15;
        
        // تحديث المتغيرات العامة
        if (window.currentTaxRate !== undefined) {
            window.currentTaxRate = taxRate;
        }
        
        // استدعاء دوال إعادة الحساب إذا كانت موجودة
        if (typeof window.updateOrderSummary === 'function') {
            window.updateOrderSummary();
        }
        
        if (typeof window.updateCartSummary === 'function') {
            window.updateCartSummary();
        }
        
        if (typeof window.recalculateOrder === 'function') {
            window.recalculateOrder();
        }
        
        // إعادة حساب عام للعناصر المعروفة
        recalculateKnownElements();
    }
    
    // إعادة حساب العناصر المعروفة
    function recalculateKnownElements() {
        const taxRate = parseFloat(currentSettings.tax_rate) || 15;
        
        // البحث عن عناصر المجموع الفرعي
        document.querySelectorAll('[data-subtotal]').forEach(element => {
            const subtotal = parseFloat(element.dataset.subtotal) || 0;
            const tax = subtotal * (taxRate / 100);
            const total = subtotal + tax;
            
            // تحديث عنصر الضريبة
            const taxElement = document.querySelector('[data-tax-amount]');
            if (taxElement) {
                taxElement.textContent = formatCurrency(tax);
            }
            
            // تحديث عنصر المجموع
            const totalElement = document.querySelector('[data-total-amount]');
            if (totalElement) {
                totalElement.textContent = formatCurrency(total);
            }
        });
        
        // تحديث عناصر JavaScript المعروفة
        updateJavaScriptElements();
    }
    
    // تحديث عناصر JavaScript
    function updateJavaScriptElements() {
        const taxRate = parseFloat(currentSettings.tax_rate) || 15;
        
        // تحديث عناصر الطلبات
        const subtotalElement = document.getElementById('subtotalAmount');
        const taxElement = document.getElementById('taxAmount');
        const totalElement = document.getElementById('totalAmount');
        
        if (subtotalElement && taxElement && totalElement) {
            const subtotalText = subtotalElement.textContent.replace(/[^\d.]/g, '');
            const subtotal = parseFloat(subtotalText) || 0;
            
            if (subtotal > 0) {
                const tax = subtotal * (taxRate / 100);
                const total = subtotal + tax;
                
                taxElement.textContent = formatCurrency(tax);
                totalElement.textContent = formatCurrency(total);
            }
        }
    }
    
    // تنسيق العملة
    function formatCurrency(amount) {
        const currency = currentSettings.currency || 'د.ل';
        return `${amount.toFixed(2)} ${currency}`;
    }
    
    // بدء النظام
    function init() {
        // تحميل الإعدادات فوراً
        loadSettings();
        
        // تحديث كل 3 ثوان
        setInterval(loadSettings, 3000);
        
        // تحديث عند التركيز على النافذة
        window.addEventListener('focus', loadSettings);
        
        console.log('🚀 نظام تحديث الإعدادات البسيط تم تشغيله');
    }
    
    // تشغيل النظام عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // تصدير الدوال للاستخدام العام
    window.settingsRefresh = {
        loadSettings: loadSettings,
        getCurrentSettings: () => currentSettings,
        getTaxRate: () => parseFloat(currentSettings.tax_rate) || 15,
        formatCurrency: formatCurrency
    };
    
})();
