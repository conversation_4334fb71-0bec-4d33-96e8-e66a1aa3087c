<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;
use App\Models\Payment;
use App\Models\Order;
use App\Models\User;
use App\Models\Table;
use App\Models\OrderItem;
use App\Models\MenuItem;
use App\Models\Invoice;

class PaymentSeeder extends Seeder
{
    public function run()
    {
        // إنشاء مستخدمين تجريبيين إذا لم يكونوا موجودين
        $customer1 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'أحمد',
                'last_name' => 'محمد',
                'phone' => '0912345678',
                'password' => bcrypt('password'),
                'user_type' => 'customer',
                'is_active' => true
            ]
        );

        $customer2 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'فاطمة',
                'last_name' => 'علي',
                'phone' => '0923456789',
                'password' => bcrypt('password'),
                'user_type' => 'customer',
                'is_active' => true
            ]
        );

        // التأكد من وجود طاولات
        if (Table::count() == 0) {
            Table::create([
                'table_number' => 1,
                'capacity' => 4,
                'status' => 'available',
                'location' => 'منطقة عامة'
            ]);
        }

        // التأكد من وجود عناصر قائمة
        if (MenuItem::count() == 0) {
            MenuItem::create([
                'name' => 'برجر لحم',
                'description' => 'برجر لحم لذيذ',
                'price' => 25.00,
                'category' => 'main',
                'is_available' => true
            ]);
        }

        $table = Table::first();
        $menuItem = MenuItem::first();

        // إنشاء طلبات تجريبية
        $orders = [
            [
                'user_id' => $customer1->id,
                'table_id' => $table->table_id,
                'total_amount' => 75.50,
                'status' => 'completed',
                'order_type' => 'dine_in',
                'payment_method' => 'cash',
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(5)
            ],
            [
                'user_id' => $customer2->id,
                'table_id' => $table->table_id,
                'total_amount' => 120.00,
                'status' => 'completed',
                'order_type' => 'dine_in',
                'payment_method' => 'card',
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(3)
            ],
            [
                'user_id' => $customer1->id,
                'table_id' => $table->table_id,
                'total_amount' => 45.25,
                'status' => 'completed',
                'order_type' => 'takeaway',
                'payment_method' => 'cash',
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subDays(1)
            ],
            [
                'user_id' => $customer2->id,
                'table_id' => $table->table_id,
                'total_amount' => 89.75,
                'status' => 'pending',
                'order_type' => 'dine_in',
                'payment_method' => 'card',
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        foreach ($orders as $orderData) {
            $order = Order::create($orderData);

            // إضافة عناصر للطلب
            $quantity = rand(1, 3);
            OrderItem::create([
                'order_id' => $order->order_id,
                'menu_item_id' => $menuItem->item_id,
                'quantity' => $quantity,
                'price' => $menuItem->price
            ]);

            // إنشاء فاتورة للطلب
            $tax = $order->total_amount * 0.15;
            // إنشاء فاتورة للطلب
            $invoiceData = [
                'order_id' => $order->order_id,
                'total_amount' => $order->total_amount + $tax,
                'tax_amount' => $tax,
                'discount_amount' => 0,
                'payment_status' => $order->status == 'completed' ? 'paid' : 'unpaid',
                'due_date' => now()->addDays(7),
            ];

            // إضافة issued_at فقط إذا كان العمود موجود
            if (Schema::hasColumn('invoices', 'issued_at')) {
                $invoiceData['issued_at'] = $order->created_at;
            }

            $invoice = Invoice::create($invoiceData);

            // إنشاء مدفوعات للطلبات المكتملة
            if ($order->status == 'completed') {
                Payment::create([
                    'order_id' => $order->order_id,
                    'amount' => $order->total_amount,
                    'payment_method' => $order->payment_method,
                    'transaction_date' => $order->created_at
                ]);
            } elseif ($order->status == 'pending') {
                // دفعة جزئية للطلب المعلق
                Payment::create([
                    'order_id' => $order->order_id,
                    'amount' => $order->total_amount * 0.5, // 50% دفعة مقدمة
                    'payment_method' => $order->payment_method,
                    'transaction_date' => $order->created_at
                ]);
            }
        }

        // إضافة مدفوعات إضافية للتنويع
        $additionalPayments = [
            [
                'order_id' => Order::first()->order_id,
                'amount' => 25.00,
                'payment_method' => 'cash',
                'transaction_date' => now()->subHours(2)
            ],
            [
                'order_id' => Order::skip(1)->first()->order_id,
                'amount' => 50.00,
                'payment_method' => 'card',
                'transaction_date' => now()->subHours(1)
            ]
        ];

        foreach ($additionalPayments as $paymentData) {
            Payment::create($paymentData);
        }

        echo "تم إنشاء " . Payment::count() . " مدفوعة تجريبية\n";
        echo "تم إنشاء " . Order::count() . " طلب تجريبي\n";
        echo "تم إنشاء " . Invoice::count() . " فاتورة تجريبية\n";
    }
}
