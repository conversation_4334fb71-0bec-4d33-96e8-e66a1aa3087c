/**
 * نظام موحد للوضع المظلم - يضمن الثبات الكامل
 * يحل مشكلة تغيير الوضع عند كل دخول للصفحة
 */

// متغيرات عامة
let isDarkModeInitialized = false;
const STORAGE_KEY = 'darkMode';

/**
 * تطبيق الوضع المظلم فوراً
 */
function applyDarkModeImmediately() {
    const savedMode = localStorage.getItem(STORAGE_KEY);
    console.log('🔍 الوضع المحفوظ:', savedMode);
    
    // دعم القيم المختلفة للتوافق مع الأنظمة القديمة
    const isDarkMode = savedMode === 'true' || savedMode === 'enabled' || savedMode === 'dark';
    
    if (isDarkMode) {
        document.documentElement.classList.add('dark');
        console.log('🌙 تم تطبيق الوضع المظلم');
    } else if (savedMode === 'false' || savedMode === 'disabled' || savedMode === 'light') {
        document.documentElement.classList.remove('dark');
        console.log('☀️ تم تطبيق الوضع الفاتح');
    } else {
        // لا يوجد إعداد محفوظ، استخدم إعدادات النظام
        const systemPrefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        if (systemPrefersDark) {
            document.documentElement.classList.add('dark');
            localStorage.setItem(STORAGE_KEY, 'true');
            console.log('🌙 تم تطبيق الوضع المظلم من إعدادات النظام');
        } else {
            document.documentElement.classList.remove('dark');
            localStorage.setItem(STORAGE_KEY, 'false');
            console.log('☀️ تم تطبيق الوضع الفاتح كافتراضي');
        }
    }
}

/**
 * تحديث أيقونة الوضع المظلم
 */
function updateDarkModeIcon() {
    const icon = document.getElementById('darkModeIcon');
    if (icon) {
        const isDark = document.documentElement.classList.contains('dark');
        if (isDark) {
            icon.className = 'fas fa-sun theme-icon group-hover:rotate-180 transition-transform duration-500';
        } else {
            icon.className = 'fas fa-moon theme-icon group-hover:rotate-180 transition-transform duration-500';
        }
        console.log('🔄 تم تحديث أيقونة الوضع المظلم:', isDark ? 'شمس' : 'قمر');
    }
}

/**
 * تبديل الوضع المظلم
 */
function toggleUnifiedDarkMode() {
    console.log('🔄 تبديل الوضع المظلم...');
    
    const html = document.documentElement;
    const currentlyDark = html.classList.contains('dark');
    
    if (currentlyDark) {
        // التبديل إلى الوضع الفاتح
        html.classList.remove('dark');
        localStorage.setItem(STORAGE_KEY, 'false');
        console.log('☀️ تم التبديل إلى الوضع الفاتح');
    } else {
        // التبديل إلى الوضع المظلم
        html.classList.add('dark');
        localStorage.setItem(STORAGE_KEY, 'true');
        console.log('🌙 تم التبديل إلى الوضع المظلم');
    }
    
    // تحديث الأيقونة
    updateDarkModeIcon();
    
    // تحديث المخططات إذا كانت موجودة
    setTimeout(() => {
        if (typeof forceUpdateCharts === 'function') {
            forceUpdateCharts();
        }
        if (typeof updateAllCharts === 'function') {
            updateAllCharts();
        }
    }, 100);
    
    // إرسال حدث مخصص للمكونات الأخرى
    window.dispatchEvent(new CustomEvent('darkModeChanged', {
        detail: { isDark: !currentlyDark }
    }));
}

/**
 * تهيئة نظام الوضع المظلم
 */
function initUnifiedDarkMode() {
    if (isDarkModeInitialized) {
        console.log('⚠️ نظام الوضع المظلم مهيأ بالفعل');
        return;
    }

    console.log('🚀 تهيئة نظام الوضع المظلم الموحد...');

    // تطبيق الوضع المحفوظ
    applyDarkModeImmediately();

    // تحديث الأيقونة
    updateDarkModeIcon();

    // إعداد جميع أزرار التبديل الممكنة
    const toggleButtons = [
        document.getElementById('darkModeToggle'),
        document.querySelector('[data-theme-toggle]'),
        document.querySelector('.theme-toggle')
    ].filter(btn => btn !== null);

    if (toggleButtons.length > 0) {
        toggleButtons.forEach((toggleButton, index) => {
            // إزالة أي مستمعات سابقة
            const newButton = toggleButton.cloneNode(true);
            toggleButton.parentNode.replaceChild(newButton, toggleButton);

            // إضافة مستمع جديد
            newButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log(`🖱️ تم النقر على زر الوضع المظلم #${index + 1}`);
                toggleUnifiedDarkMode();
            });

            console.log(`✅ تم إعداد زر تبديل الوضع المظلم #${index + 1}`);
        });
    } else {
        console.warn('⚠️ لم يتم العثور على أي زر تبديل للوضع المظلم');

        // محاولة البحث بطريقة أخرى
        setTimeout(() => {
            const fallbackButton = document.querySelector('button[title*="تبديل"]') ||
                                 document.querySelector('button[title*="ثيم"]') ||
                                 document.querySelector('button i.fa-moon') ||
                                 document.querySelector('button i.fa-sun');

            if (fallbackButton) {
                const button = fallbackButton.closest('button');
                if (button) {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('🖱️ تم النقر على الزر البديل للوضع المظلم');
                        toggleUnifiedDarkMode();
                    });
                    console.log('✅ تم إعداد الزر البديل للوضع المظلم');
                }
            }
        }, 500);
    }
    
    // مراقبة تغييرات إعدادات النظام
    if (window.matchMedia) {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        mediaQuery.addEventListener('change', function(e) {
            const savedMode = localStorage.getItem(STORAGE_KEY);
            // فقط إذا لم يكن هناك إعداد محفوظ صريح
            if (!savedMode || savedMode === 'auto') {
                if (e.matches) {
                    document.documentElement.classList.add('dark');
                    localStorage.setItem(STORAGE_KEY, 'true');
                } else {
                    document.documentElement.classList.remove('dark');
                    localStorage.setItem(STORAGE_KEY, 'false');
                }
                updateDarkModeIcon();
                console.log('🔄 تم تحديث الوضع حسب إعدادات النظام');
            }
        });
    }
    
    isDarkModeInitialized = true;
    console.log('✅ تم تهيئة نظام الوضع المظلم الموحد بنجاح');
}

/**
 * ضمان ثبات الوضع عند تغيير الصفحة
 */
function ensureDarkModeConsistency() {
    const savedMode = localStorage.getItem(STORAGE_KEY);
    if (savedMode) {
        applyDarkModeImmediately();
        updateDarkModeIcon();
    }
}

// تشغيل النظام عند تحميل DOM
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 DOM تم تحميله، بدء تهيئة النظام...');
    initUnifiedDarkMode();
});

// تشغيل النظام عند تحميل الصفحة كاملة
window.addEventListener('load', function() {
    console.log('🌐 الصفحة تم تحميلها كاملة...');

    // التأكد من الثبات
    ensureDarkModeConsistency();

    // إعادة تهيئة إذا لم تكن مهيأة
    if (!isDarkModeInitialized) {
        console.log('🔄 إعادة تهيئة النظام...');
        initUnifiedDarkMode();
    }

    // محاولة أخيرة للعثور على الزر وربطه
    setTimeout(() => {
        const button = document.getElementById('darkModeToggle');
        if (button && !button.hasAttribute('data-unified-attached')) {
            console.log('🔗 ربط الزر بالنظام الموحد...');

            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('🖱️ تم النقر على الزر (ربط متأخر)');
                toggleUnifiedDarkMode();
            });

            button.setAttribute('data-unified-attached', 'true');
            console.log('✅ تم ربط الزر بنجاح');
        }
    }, 1000);
});

// ضمان الثبات عند العودة للصفحة من الكاش
window.addEventListener('pageshow', function(event) {
    if (event.persisted) {
        ensureDarkModeConsistency();
    }
});

// حفظ الإعدادات قبل مغادرة الصفحة
window.addEventListener('beforeunload', function() {
    const currentMode = document.documentElement.classList.contains('dark') ? 'true' : 'false';
    localStorage.setItem(STORAGE_KEY, currentMode);
});

/**
 * تشخيص حالة النظام
 */
function diagnoseDarkModeSystem() {
    console.log('🔍 تشخيص نظام الوضع المظلم:');
    console.log('- الوضع الحالي:', document.documentElement.classList.contains('dark') ? 'مظلم' : 'فاتح');
    console.log('- القيمة المحفوظة:', localStorage.getItem(STORAGE_KEY));
    console.log('- زر التبديل:', document.getElementById('darkModeToggle') ? 'موجود' : 'غير موجود');
    console.log('- أيقونة الوضع:', document.getElementById('darkModeIcon') ? 'موجودة' : 'غير موجودة');
    console.log('- النظام مهيأ:', isDarkModeInitialized ? 'نعم' : 'لا');

    // البحث عن جميع الأزرار المحتملة
    const allButtons = document.querySelectorAll('button');
    const themeButtons = Array.from(allButtons).filter(btn =>
        btn.id === 'darkModeToggle' ||
        btn.hasAttribute('data-theme-toggle') ||
        btn.classList.contains('theme-toggle') ||
        btn.querySelector('i.fa-moon') ||
        btn.querySelector('i.fa-sun')
    );

    console.log('- أزرار الثيم الموجودة:', themeButtons.length);
    themeButtons.forEach((btn, i) => {
        console.log(`  الزر ${i + 1}:`, btn.id || btn.className);
    });
}

// جعل الدوال متاحة عالمياً
window.toggleUnifiedDarkMode = toggleUnifiedDarkMode;
window.initUnifiedDarkMode = initUnifiedDarkMode;
window.ensureDarkModeConsistency = ensureDarkModeConsistency;
window.diagnoseDarkModeSystem = diagnoseDarkModeSystem;

// تشغيل التشخيص بعد التحميل
setTimeout(diagnoseDarkModeSystem, 1000);

console.log('📦 تم تحميل نظام الوضع المظلم الموحد');
