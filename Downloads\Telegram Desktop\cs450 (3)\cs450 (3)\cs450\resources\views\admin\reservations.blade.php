@extends('layouts.admin')

@section('title', 'إدارة الحجوزات')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- عنوان الصفحة -->
    <div class="mb-6 flex flex-wrap items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800 dark:text-white">إدارة الحجوزات</h1>
            <p class="text-gray-600 dark:text-gray-400">عرض وإدارة جميع حجوزات الطاولات</p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3 space-x-reverse">
            <a href="{{ route('employee.reservations.create') }}" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50">
                <i class="fas fa-plus-circle ml-2"></i>
                إنشاء حجز جديد
            </a>
        </div>
    </div>

    @if(session('success'))
    <div class="bg-green-100 border-r-4 border-green-500 text-green-700 p-4 mb-6 rounded-md dark:bg-green-900/30 dark:text-green-500 dark:border-green-500">
        <p>{{ session('success') }}</p>
    </div>
    @endif

    @if(session('error'))
    <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-6 rounded-md dark:bg-red-900/30 dark:text-red-500 dark:border-red-500">
        <p>{{ session('error') }}</p>
    </div>
    @endif

    <!-- إحصائيات سريعة -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30">
                    <i class="fas fa-calendar-check text-blue-500 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي الحجوزات</p>
                    <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ $reservations->total() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/30">
                    <i class="fas fa-check-circle text-green-500 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm text-gray-600 dark:text-gray-400">حجوزات مؤكدة</p>
                    <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ $reservations->where('status', 'confirmed')->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30">
                    <i class="fas fa-clock text-yellow-500 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm text-gray-600 dark:text-gray-400">في الانتظار</p>
                    <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ $reservations->where('status', 'pending')->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 dark:bg-red-900/30">
                    <i class="fas fa-times-circle text-red-500 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm text-gray-600 dark:text-gray-400">ملغية</p>
                    <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ $reservations->where('status', 'canceled')->count() }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
        <form method="GET" action="{{ route('admin.reservations') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البحث</label>
                <input type="text" name="search" value="{{ request('search') }}" placeholder="اسم العميل أو رقم الحجز" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحالة</label>
                <select name="status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white">
                    <option value="">جميع الحالات</option>
                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>في الانتظار</option>
                    <option value="confirmed" {{ request('status') == 'confirmed' ? 'selected' : '' }}>مؤكد</option>
                    <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>مكتمل</option>
                    <option value="canceled" {{ request('status') == 'canceled' ? 'selected' : '' }}>ملغي</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التاريخ</label>
                <input type="date" name="date" value="{{ request('date') }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white">
            </div>
            <div class="flex items-end">
                <button type="submit" class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md transition">
                    <i class="fas fa-search ml-2"></i>
                    بحث
                </button>
            </div>
        </form>
    </div>

    <!-- جدول الحجوزات -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">رقم الحجز</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">العميل</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الهاتف</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">التاريخ</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الوقت</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الطاولة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">عدد الأشخاص</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">طلب مسبق</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($reservations as $reservation)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                            #{{ $reservation->reservation_id }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {{ $reservation->user ? $reservation->user->first_name . ' ' . $reservation->user->last_name : 'غير متوفر' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {{ $reservation->user ? $reservation->user->phone : 'غير متوفر' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {{ \Carbon\Carbon::parse($reservation->reservation_time)->format('Y-m-d') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {{ \Carbon\Carbon::parse($reservation->reservation_time)->format('H:i') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {{ $reservation->table ? 'طاولة ' . $reservation->table->table_number : 'غير محددة' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {{ $reservation->party_size ?? 'غير محدد' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                @if($reservation->status == 'confirmed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                @elseif($reservation->status == 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                @elseif($reservation->status == 'completed') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                @elseif($reservation->status == 'canceled') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                                @else bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 @endif">
                                @if($reservation->status == 'confirmed') مؤكد
                                @elseif($reservation->status == 'pending') في الانتظار
                                @elseif($reservation->status == 'completed') مكتمل
                                @elseif($reservation->status == 'canceled') ملغي
                                @else {{ $reservation->status }} @endif
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            @if($reservation->preOrder && $reservation->preOrder->orderItems->count() > 0)
                                <div class="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-2 border border-orange-200 dark:border-orange-700">
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="font-medium text-orange-600 dark:text-orange-400 text-xs">{{ $reservation->preOrder->orderItems->count() }} عنصر</span>
                                        <span class="text-xs font-bold text-orange-700 dark:text-orange-300">{{ number_format($reservation->preOrder->total_amount, 2) }} د.ل</span>
                                    </div>
                                    <div class="text-xs text-gray-600 dark:text-gray-400 mb-1">
                                        @foreach($reservation->preOrder->orderItems->take(2) as $item)
                                            <div class="flex justify-between">
                                                <span>{{ $item->menuItem->name }}</span>
                                                <span>×{{ $item->quantity }}</span>
                                            </div>
                                        @endforeach
                                        @if($reservation->preOrder->orderItems->count() > 2)
                                            <div class="text-gray-500 italic">و {{ $reservation->preOrder->orderItems->count() - 2 }} عنصر آخر...</div>
                                        @endif
                                    </div>
                                    <span class="px-2 py-1 rounded-full text-xs font-medium
                                        {{ $reservation->preOrder->status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                           ($reservation->preOrder->status === 'preparing' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' :
                                            'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300') }}">
                                        {{ $reservation->preOrder->status === 'pending' ? 'في الانتظار' :
                                           ($reservation->preOrder->status === 'preparing' ? 'قيد التحضير' : 'جاهز') }}
                                    </span>
                                </div>
                            @else
                                <span class="text-gray-400 text-xs">لا يوجد طلب مسبق</span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            <div class="flex space-x-2 space-x-reverse">
                                <!-- عرض تفاصيل الحجز -->
                                <a href="{{ route('employee.reservations.show', $reservation->reservation_id) }}" 
                                   class="bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded-md transition text-xs" 
                                   title="عرض تفاصيل الحجز">
                                    <i class="fas fa-eye"></i>
                                </a>

                                <a href="{{ route('employee.reservations.edit', $reservation->reservation_id) }}" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>

                                @if($reservation->status == 'pending')
                                <form action="{{ route('employee.reservations.update-status', $reservation->reservation_id) }}" method="POST" class="inline-block">
                                    @csrf
                                    @method('PUT')
                                    <input type="hidden" name="status" value="confirmed">
                                    <button type="submit" class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300" title="تأكيد الحجز">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </form>
                                @endif

                                @if($reservation->status == 'confirmed')
                                <form action="{{ route('employee.reservations.update-status', $reservation->reservation_id) }}" method="POST" class="inline-block">
                                    @csrf
                                    @method('PUT')
                                    <input type="hidden" name="status" value="completed">
                                    <button type="submit" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" title="إكمال الحجز">
                                        <i class="fas fa-check-circle"></i>
                                    </button>
                                </form>
                                @endif

                                <form action="{{ route('employee.reservations.update-status', $reservation->reservation_id) }}" method="POST" class="inline-block">
                                    @csrf
                                    @method('PUT')
                                    <input type="hidden" name="status" value="canceled">
                                    <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300" title="إلغاء الحجز" onclick="return confirm('هل أنت متأكد من إلغاء هذا الحجز؟')">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </form>

                                <form action="{{ route('employee.reservations.delete', $reservation->reservation_id) }}" method="POST" class="inline-block" onsubmit="return confirm('هل أنت متأكد من حذف هذا الحجز؟');">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300" title="حذف">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="10" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                            لا توجد حجوزات
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <div class="mt-6">
        {{ $reservations->appends(request()->query())->links() }}
    </div>
</div>
@endsection
