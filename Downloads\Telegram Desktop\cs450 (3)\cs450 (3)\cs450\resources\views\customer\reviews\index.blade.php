@extends('customer.layouts.app')

@section('title', 'تقييماتي - Eat Hub')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Unified User Header -->
    @include('customer.partials.user-header', [
        'title' => 'تقييماتي',
        'showStats' => false
    ])

    <div class="container mx-auto px-4 py-8">
        <!-- فلاتر التقييمات -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
            <div class="flex flex-wrap gap-3 mb-4">
                <a href="{{ route('customer.reviews') }}" class="px-4 py-2 rounded-full text-sm transition {{ !request('type') ? 'bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white' }}">
                    جميع التقييمات
                </a>
                <a href="{{ route('customer.reviews', ['type' => 'orders']) }}" class="px-4 py-2 rounded-full text-sm transition {{ request('type') == 'orders' ? 'bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white' }}">
                    تقييمات الطلبات
                </a>
                <a href="{{ route('customer.reviews', ['type' => 'reservations']) }}" class="px-4 py-2 rounded-full text-sm transition {{ request('type') == 'reservations' ? 'bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white' }}">
                    تقييمات الحجوزات
                </a>
            </div>
        </div>

        @if($reviews->count() > 0)
            <div class="space-y-6">
                @foreach($reviews as $review)
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <div class="flex items-center space-x-2 space-x-reverse mb-2">
                                    @if($review->order_id)
                                        <span class="bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-3 py-1 rounded-full text-sm">
                                            طلب #{{ $review->order_id }}
                                        </span>
                                    @else
                                        <span class="bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 px-3 py-1 rounded-full text-sm">
                                            حجز #{{ $review->reservation_id }}
                                        </span>
                                    @endif
                                    <span class="text-gray-500 dark:text-gray-400 text-sm">
                                        {{ $review->created_at->format('d M Y') }}
                                    </span>
                                </div>
                                
                                <!-- التقييم -->
                                <div class="flex items-center mb-3">
                                    <div class="flex text-yellow-400 ml-2">
                                        @for($i = 1; $i <= 5; $i++)
                                            <i class="fas fa-star {{ $i <= $review->rating ? 'text-yellow-400' : 'text-gray-300' }}"></i>
                                        @endfor
                                    </div>
                                    <span class="text-gray-600 dark:text-gray-400 text-sm">
                                        ({{ $review->rating }}/5)
                                    </span>
                                </div>
                                
                                @if($review->comment)
                                    <p class="text-gray-700 dark:text-gray-300 mb-3">{{ $review->comment }}</p>
                                @endif
                                
                                @if($review->admin_response)
                                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mt-4">
                                        <div class="flex items-center mb-2">
                                            <i class="fas fa-reply text-primary ml-2"></i>
                                            <span class="font-medium text-gray-800 dark:text-white">رد الإدارة:</span>
                                        </div>
                                        <p class="text-gray-700 dark:text-gray-300">{{ $review->admin_response }}</p>
                                        @if($review->response_date)
                                            <p class="text-gray-500 dark:text-gray-400 text-sm mt-2">
                                                {{ $review->response_date->format('d M Y') }}
                                            </p>
                                        @endif
                                    </div>
                                @endif
                            </div>
                            
                            <div class="flex space-x-2 space-x-reverse">
                                @if($review->created_at->diffInDays(now()) <= 7)
                                    <button onclick="editReview('{{ $review->review_id }}', '{{ $review->order_id ? 'order' : 'reservation' }}')" class="text-primary hover:text-primary/80 text-sm">
                                        <i class="fas fa-edit ml-1"></i>تعديل
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $reviews->links() }}
            </div>
        @else
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-12 text-center">
                <i class="fas fa-star text-6xl text-gray-300 dark:text-gray-600 mb-4"></i>
                <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">لا توجد تقييمات بعد</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-6">
                    قم بطلب الطعام أو حجز طاولة ثم قيم تجربتك معنا
                </p>
                <div class="flex justify-center space-x-4 space-x-reverse">
                    <a href="{{ route('customer.orders') }}" class="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition">
                        <i class="fas fa-shopping-cart ml-2"></i>طلباتي
                    </a>
                    <a href="{{ route('customer.reservations') }}" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition">
                        <i class="fas fa-calendar ml-2"></i>حجوزاتي
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
function editReview(reviewId, type) {
    if (type === 'order') {
        // إعادة توجيه لصفحة تعديل تقييم الطلب
        window.location.href = `/customer/reviews/${reviewId}/edit`;
    } else {
        // فتح نافذة تعديل تقييم الحجز
        showReservationReviewModal(reviewId, true);
    }
}

// نسخ وظائف تقييم الحجوزات من صفحة الحجوزات
function showReservationReviewModal(reviewId, isEdit = false) {
    // نفس الكود من صفحة الحجوزات
    // يمكن تحسينه لاحقاً بوضعه في ملف منفصل
}
</script>
@endpush
