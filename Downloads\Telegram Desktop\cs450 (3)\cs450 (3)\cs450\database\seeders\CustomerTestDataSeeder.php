<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Order;
use App\Models\Reservation;
use App\Models\Notification;
use App\Models\MenuItem;
use App\Models\OrderItem;
use App\Models\Table;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class CustomerTestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء مستخدم تجريبي للعميل
        $customer = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'أحمد',
                'last_name' => 'محمد',
                'phone' => '+218912345678',
                'address' => 'طرابلس، ليبيا',
                'password' => Hash::make('password123'),
                'user_type' => 'customer',
                'is_active' => true
            ]
        );

        // إنشاء عناصر قائمة تجريبية
        $menuItems = [
            [
                'name' => 'برجر لحم أنجوس',
                'description' => 'برجر لحم بقري فاخر مع صلصة خاصة وجبنة شيدر وخضار طازجة',
                'price' => 55.00,
                'category' => 'main',
                'is_active' => true,
                'is_featured' => true,
                'image_path' => 'https://images.unsplash.com/photo-1513104890138-7c749659a591'
            ],
            [
                'name' => 'بيتزا سوبريم',
                'description' => 'بيتزا مع صلصة طماطم، جبنة موزاريلا، فلفل، زيتون، بصل، فطر وبيبروني',
                'price' => 65.00,
                'category' => 'main',
                'is_active' => true,
                'is_featured' => true,
                'image_path' => 'https://images.unsplash.com/photo-1565299624946-b28f40a0ae38'
            ],
            [
                'name' => 'سلطة سيزر بالدجاج',
                'description' => 'خس روماني، صدر دجاج مشوي، جبنة بارميزان، خبز محمص مع صلصة سيزر',
                'price' => 45.00,
                'category' => 'appetizer',
                'is_active' => true,
                'is_featured' => false,
                'image_path' => 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd'
            ],
            [
                'name' => 'عصير برتقال طازج',
                'description' => 'عصير برتقال طبيعي 100% بدون إضافات',
                'price' => 15.00,
                'category' => 'beverage',
                'is_active' => true,
                'is_featured' => false,
                'image_path' => 'https://images.unsplash.com/photo-1461023058943-07fcbe16d735'
            ]
        ];

        foreach ($menuItems as $item) {
            try {
                MenuItem::firstOrCreate(
                    ['name' => $item['name']],
                    $item
                );
            } catch (\Exception $e) {
                // تجاهل الأخطاء إذا كان الجدول غير موجود
            }
        }

        // إنشاء طاولات تجريبية
        $tables = [
            ['table_number' => 5, 'capacity' => 4, 'location' => 'منطقة النافذة', 'is_active' => true],
            ['table_number' => 8, 'capacity' => 2, 'location' => 'منطقة الحديقة', 'is_active' => true],
            ['table_number' => 12, 'capacity' => 6, 'location' => 'منطقة VIP', 'is_active' => true],
        ];

        foreach ($tables as $table) {
            try {
                Table::firstOrCreate(
                    ['table_number' => $table['table_number']],
                    $table
                );
            } catch (\Exception $e) {
                // تجاهل الأخطاء إذا كان الجدول غير موجود
            }
        }

        // إنشاء طلبات تجريبية
        try {
            $orders = [
                [
                    'user_id' => $customer->user_id,
                    'total_amount' => 85.50,
                    'status' => 'completed',
                    'created_at' => now()->subDays(3),
                    'updated_at' => now()->subDays(3),
                ],
                [
                    'user_id' => $customer->user_id,
                    'total_amount' => 120.00,
                    'status' => 'processing',
                    'created_at' => now()->subHours(2),
                    'updated_at' => now()->subHours(1),
                ],
                [
                    'user_id' => $customer->user_id,
                    'total_amount' => 75.00,
                    'status' => 'cancelled',
                    'created_at' => now()->subDays(7),
                    'updated_at' => now()->subDays(7),
                ]
            ];

            foreach ($orders as $orderData) {
                $order = Order::create($orderData);
                
                // إضافة عناصر للطلب
                $menuItem = MenuItem::first();
                if ($menuItem) {
                    OrderItem::create([
                        'order_id' => $order->order_id,
                        'menu_item_id' => $menuItem->menu_item_id,
                        'quantity' => 1,
                        'price' => $menuItem->price,
                    ]);
                }
            }
        } catch (\Exception $e) {
            echo "تعذر إنشاء الطلبات: " . $e->getMessage() . "\n";
        }

        // إنشاء حجوزات تجريبية
        try {
            $table = Table::first();
            if ($table) {
                $reservations = [
                    [
                        'user_id' => $customer->user_id,
                        'table_id' => $table->table_id,
                        'reservation_time' => now()->addDays(1)->setHour(19)->setMinute(0),
                        'duration' => 120,
                        'status' => 'confirmed',
                        'special_requests' => 'طاولة بجانب النافذة، احتفال بعيد ميلاد',
                        'created_at' => now()->subDays(2),
                    ],
                    [
                        'user_id' => $customer->user_id,
                        'table_id' => $table->table_id,
                        'reservation_time' => now()->subDays(3)->setHour(18)->setMinute(30),
                        'duration' => 120,
                        'status' => 'completed',
                        'created_at' => now()->subDays(8),
                    ]
                ];

                foreach ($reservations as $reservationData) {
                    Reservation::create($reservationData);
                }
            }
        } catch (\Exception $e) {
            echo "تعذر إنشاء الحجوزات: " . $e->getMessage() . "\n";
        }

        // إنشاء إشعارات تجريبية
        try {
            $notifications = [
                [
                    'user_id' => $customer->user_id,
                    'title' => 'تأكيد الطلب',
                    'message' => 'تم تأكيد طلبك رقم ORD-1001 وجاري تجهيزه',
                    'is_read' => false,
                    'created_at' => now()->subHours(2),
                ],
                [
                    'user_id' => $customer->user_id,
                    'title' => 'عرض خاص',
                    'message' => 'استمتع بخصم 15% على طلبك القادم باستخدام كود: SPECIAL15',
                    'is_read' => false,
                    'created_at' => now()->subDays(1),
                ],
                [
                    'user_id' => $customer->user_id,
                    'title' => 'تأكيد الحجز',
                    'message' => 'تم تأكيد حجزك لطاولة #5 غداً في تمام الساعة 7:00 مساءً',
                    'is_read' => true,
                    'created_at' => now()->subDays(2),
                ]
            ];

            foreach ($notifications as $notificationData) {
                Notification::create($notificationData);
            }
        } catch (\Exception $e) {
            echo "تعذر إنشاء الإشعارات: " . $e->getMessage() . "\n";
        }

        echo "تم إنشاء البيانات التجريبية بنجاح!\n";
        echo "بيانات تسجيل الدخول:\n";
        echo "البريد الإلكتروني: <EMAIL>\n";
        echo "كلمة المرور: password123\n";
    }
}
