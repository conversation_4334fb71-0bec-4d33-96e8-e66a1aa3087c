<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MenuItem;
use App\Models\Offer;
use App\Models\Review;
use App\Models\User;
use Carbon\Carbon;

class DemoDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء أطباق مميزة
        $menuItems = [
            [
                'name' => 'برجر لحم أنجوس الفاخر',
                'description' => 'برجر لحم بقري أنجوس مشوي مع جبنة شيدر مدخنة، خس طازج، طماطم، وصلصة خاصة',
                'price' => 65.00,
                'category' => 'main',
                'is_available' => true,
                'image_path' => null
            ],
            [
                'name' => 'بيتزا مارجريتا الإيطالية',
                'description' => 'بيتزا كلاسيكية بصلصة الطماطم الطازجة، جبنة موزاريلا، وأوراق الريحان',
                'price' => 45.00,
                'category' => 'main',
                'is_available' => true,
                'image_path' => null
            ],
            [
                'name' => 'سلطة سيزر بالدجاج',
                'description' => 'خس روماني طازج مع قطع الدجاج المشوي، جبنة بارميزان، وصلصة سيزر الأصلية',
                'price' => 35.00,
                'category' => 'appetizer',
                'is_available' => true,
                'image_path' => null
            ],
            [
                'name' => 'ستيك لحم مشوي',
                'description' => 'قطعة ستيك لحم طرية مشوية على الفحم مع البطاطس المحمرة والخضار المشكلة',
                'price' => 85.00,
                'category' => 'main',
                'is_available' => true,
                'image_path' => null
            ],
            [
                'name' => 'معكرونة ألفريدو بالدجاج',
                'description' => 'معكرونة فيتوتشيني مع صلصة الكريمة البيضاء وقطع الدجاج المتبلة',
                'price' => 55.00,
                'category' => 'main',
                'is_available' => true,
                'image_path' => null
            ],
            [
                'name' => 'تشيز كيك الفراولة',
                'description' => 'قطعة تشيز كيك كريمية مع صلصة الفراولة الطازجة وقطع الفراولة',
                'price' => 25.00,
                'category' => 'dessert',
                'is_available' => true,
                'image_path' => null
            ]
        ];

        foreach ($menuItems as $item) {
            MenuItem::firstOrCreate(
                ['name' => $item['name']],
                $item
            );
        }

        // إنشاء عروض خاصة
        $offers = [
            [
                'title' => 'خصم 30% على الوجبات العائلية',
                'slug' => 'family-meals-30-off',
                'description' => 'احصل على خصم 30% على جميع الوجبات العائلية عند طلب وجبة لأكثر من 4 أشخاص. العرض شامل المقبلات والمشروبات.',
                'image_path' => null,
                'type' => 'طعام',
                'discount_percentage' => 30.00,
                'start_date' => Carbon::now(),
                'end_date' => Carbon::now()->addDays(30),
                'is_active' => true,
                'terms_conditions' => json_encode(['العرض ساري على الوجبات العائلية فقط', 'لا يمكن دمجه مع عروض أخرى']),
                'min_order_amount' => 150.00
            ],
            [
                'title' => 'أطباق جديدة من المطبخ الآسيوي',
                'slug' => 'new-asian-dishes',
                'description' => 'اكتشف مجموعة جديدة من الأطباق الآسيوية الأصيلة مع لمسة عربية مميزة. تشكيلة واسعة من النكهات الشرقية.',
                'image_path' => null,
                'type' => 'طعام',
                'discount_percentage' => null,
                'start_date' => Carbon::now()->subDays(5),
                'end_date' => Carbon::now()->addDays(60),
                'is_active' => true,
                'terms_conditions' => json_encode(['الأطباق متاحة لفترة محدودة']),
                'min_order_amount' => null
            ],
            [
                'title' => 'ليالي الموسيقى والطعام',
                'slug' => 'music-food-nights',
                'description' => 'انضم إلينا كل خميس وجمعة لأمسيات موسيقية رائعة مع أشهى المأكولات. عروض حية وأجواء مميزة.',
                'image_path' => null,
                'type' => 'مختلط',
                'discount_percentage' => 15.00,
                'start_date' => Carbon::now()->subDays(2),
                'end_date' => Carbon::now()->addDays(90),
                'is_active' => true,
                'terms_conditions' => json_encode(['الحجز مطلوب مسبقاً', 'العرض متاح أيام الخميس والجمعة فقط']),
                'min_order_amount' => 100.00
            ]
        ];

        foreach ($offers as $offer) {
            Offer::firstOrCreate(
                ['slug' => $offer['slug']],
                $offer
            );
        }

        // إنشاء مستخدم تجريبي للتقييمات
        $testUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'عميل تجريبي',
                'password' => bcrypt('password'),
                'phone' => '0912345678',
                'role' => 'customer'
            ]
        );

        // إنشاء تقييمات تجريبية
        $reviews = [
            [
                'user_id' => $testUser->id,
                'menu_item_id' => 1,
                'rating' => 5,
                'comment' => 'برجر رائع! اللحم طري والطعم مميز جداً. أنصح به بشدة.',
                'is_approved' => true
            ],
            [
                'user_id' => $testUser->id,
                'menu_item_id' => 2,
                'rating' => 4,
                'comment' => 'بيتزا لذيذة وعجينة مقرمشة. الجبنة طازجة والطعم أصيل.',
                'is_approved' => true
            ],
            [
                'user_id' => $testUser->id,
                'menu_item_id' => 3,
                'rating' => 5,
                'comment' => 'سلطة منعشة ومشبعة. الدجاج مطبوخ بطريقة مثالية.',
                'is_approved' => true
            ]
        ];

        foreach ($reviews as $review) {
            Review::firstOrCreate(
                [
                    'user_id' => $review['user_id'],
                    'menu_item_id' => $review['menu_item_id']
                ],
                $review
            );
        }

        $this->command->info('تم إنشاء البيانات التجريبية بنجاح!');
        $this->command->info('- تم إنشاء ' . count($menuItems) . ' أطباق مميزة');
        $this->command->info('- تم إنشاء ' . count($offers) . ' عروض خاصة');
        $this->command->info('- تم إنشاء ' . count($reviews) . ' تقييمات');
    }
}
