<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class TestCustomersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء عملاء تجريبيين للاختبار
        $customers = [
            [
                'first_name' => 'أحمد',
                'last_name' => 'محمد',
                'email' => '<EMAIL>',
                'phone' => '01234567890',
                'user_type' => 'customer',
                'password' => Hash::make('password123'),
                'is_active' => true,
            ],
            [
                'first_name' => 'فاطمة',
                'last_name' => 'علي',
                'email' => '<EMAIL>',
                'phone' => '01234567891',
                'user_type' => 'customer',
                'password' => Hash::make('password123'),
                'is_active' => true,
            ],
            [
                'first_name' => 'محمد',
                'last_name' => 'حسن',
                'email' => '<EMAIL>',
                'phone' => '01234567892',
                'user_type' => 'customer',
                'password' => Hash::make('password123'),
                'is_active' => true,
            ],
            [
                'first_name' => 'عائشة',
                'last_name' => 'إبراهيم',
                'email' => '<EMAIL>',
                'phone' => '01234567893',
                'user_type' => 'customer',
                'password' => Hash::make('password123'),
                'is_active' => true,
            ],
            [
                'first_name' => 'يوسف',
                'last_name' => 'عبدالله',
                'email' => '<EMAIL>',
                'phone' => '01234567894',
                'user_type' => 'customer',
                'password' => Hash::make('password123'),
                'is_active' => true,
            ]
        ];

        foreach ($customers as $customerData) {
            // التحقق من عدم وجود العميل مسبقاً
            $existingCustomer = User::where('email', $customerData['email'])->first();
            
            if (!$existingCustomer) {
                User::create($customerData);
            }
        }
    }
}
