<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class EnhancedAdminPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        echo "🚀 إضافة صلاحيات محسنة للإدارة...\n\n";

        // إضافة صلاحيات جديدة ومتقدمة
        $newPermissions = [
            // صلاحيات النظام المتقدمة
            'system.maintenance' => 'وضع الصيانة',
            'system.cache.clear' => 'مسح الذاكرة المؤقتة',
            'system.logs.view' => 'عرض سجلات النظام',
            'system.logs.download' => 'تحميل سجلات النظام',
            'system.database.backup' => 'نسخ احتياطي لقاعدة البيانات',
            'system.database.restore' => 'استعادة قاعدة البيانات',
            'system.updates.check' => 'فحص التحديثات',
            'system.updates.install' => 'تثبيت التحديثات',

            // صلاحيات الأمان المتقدمة
            'security.audit.view' => 'عرض سجل التدقيق',
            'security.sessions.manage' => 'إدارة جلسات المستخدمين',
            'security.ip.block' => 'حظر عناوين IP',
            'security.login.attempts' => 'عرض محاولات تسجيل الدخول',
            'security.two_factor.manage' => 'إدارة المصادقة الثنائية',

            // صلاحيات التحليلات المتقدمة
            'analytics.advanced' => 'التحليلات المتقدمة',
            'analytics.realtime' => 'التحليلات الفورية',
            'analytics.export.all' => 'تصدير جميع التحليلات',
            'analytics.custom.reports' => 'إنشاء تقارير مخصصة',

            // صلاحيات إدارة المحتوى
            'content.announcements' => 'إدارة الإعلانات',
            'content.promotions' => 'إدارة العروض الترويجية',
            'content.social.media' => 'إدارة وسائل التواصل الاجتماعي',
            'content.email.templates' => 'إدارة قوالب البريد الإلكتروني',

            // صلاحيات إدارة الموظفين المتقدمة
            'staff.schedules.manage' => 'إدارة جداول العمل',
            'staff.performance.view' => 'عرض أداء الموظفين',
            'staff.training.manage' => 'إدارة التدريب',
            'staff.payroll.manage' => 'إدارة الرواتب',

            // صلاحيات إدارة العملاء المتقدمة
            'customers.loyalty.manage' => 'إدارة برنامج الولاء',
            'customers.feedback.manage' => 'إدارة ملاحظات العملاء',
            'customers.communication' => 'التواصل مع العملاء',
            'customers.data.export' => 'تصدير بيانات العملاء',

            // صلاحيات إدارة المطعم المتقدمة
            'restaurant.layout.manage' => 'إدارة تخطيط المطعم',
            'restaurant.equipment.manage' => 'إدارة المعدات',
            'restaurant.suppliers.manage' => 'إدارة الموردين',
            'restaurant.quality.control' => 'مراقبة الجودة',

            // صلاحيات التقارير المالية المتقدمة
            'finance.tax.reports' => 'تقارير الضرائب',
            'finance.profit.loss' => 'تقارير الربح والخسارة',
            'finance.cash.flow' => 'تقارير التدفق النقدي',
            'finance.budget.planning' => 'تخطيط الميزانية',

            // صلاحيات إدارة التطبيق
            'app.settings.advanced' => 'الإعدادات المتقدمة',
            'app.integrations.manage' => 'إدارة التكاملات',
            'app.api.manage' => 'إدارة واجهة برمجة التطبيقات',
            'app.webhooks.manage' => 'إدارة Webhooks',

            // صلاحيات الطوارئ
            'emergency.access' => 'الوصول في حالات الطوارئ',
            'emergency.override' => 'تجاوز القيود في الطوارئ',
            'emergency.shutdown' => 'إيقاف النظام في الطوارئ',
        ];

        echo "1️⃣ إنشاء الصلاحيات الجديدة...\n";
        foreach ($newPermissions as $name => $description) {
            Permission::firstOrCreate([
                'name' => $name,
                'guard_name' => 'web'
            ]);
            echo "✅ {$description} ({$name})\n";
        }

        echo "\n2️⃣ إنشاء أدوار إدارية متخصصة...\n";

        // إنشاء دور المدير العام (Super Admin)
        $superAdminRole = Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'web']);
        $superAdminRole->givePermissionTo(Permission::all());
        echo "✅ دور المدير العام - جميع الصلاحيات\n";

        // إنشاء دور مدير النظام (System Admin)
        $systemAdminRole = Role::firstOrCreate(['name' => 'system_admin', 'guard_name' => 'web']);
        $systemAdminPermissions = Permission::where('name', 'like', 'system.%')
            ->orWhere('name', 'like', 'security.%')
            ->orWhere('name', 'like', 'app.%')
            ->orWhere('name', 'like', 'emergency.%')
            ->pluck('name');
        $systemAdminRole->givePermissionTo($systemAdminPermissions);
        echo "✅ دور مدير النظام - صلاحيات النظام والأمان\n";

        // إنشاء دور المدير المالي (Financial Admin)
        $financialAdminRole = Role::firstOrCreate(['name' => 'financial_admin', 'guard_name' => 'web']);
        $financialAdminPermissions = Permission::where('name', 'like', 'finance.%')
            ->orWhere('name', 'like', 'payments.%')
            ->orWhere('name', 'like', 'expenses.%')
            ->orWhere('name', 'like', 'reports.financial%')
            ->pluck('name');
        $financialAdminRole->givePermissionTo($financialAdminPermissions);
        echo "✅ دور المدير المالي - صلاحيات مالية شاملة\n";

        echo "\n3️⃣ تحديث صلاحيات المديرين الحاليين...\n";

        // إعطاء جميع الصلاحيات للمديرين الحاليين
        $adminRole = Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
        $adminRole->givePermissionTo(Permission::all());

        // تحديث جميع المديرين الحاليين
        $adminUsers = User::where('user_type', 'admin')->get();
        foreach ($adminUsers as $user) {
            // إعطاء دور المدير العام
            $user->assignRole('super_admin');

            // إعطاء جميع الصلاحيات مباشرة أيضاً
            $user->givePermissionTo(Permission::all());

            echo "✅ تم تحديث صلاحيات: {$user->first_name} {$user->last_name}\n";
        }

        echo "\n4️⃣ إنشاء مدير تجريبي بصلاحيات كاملة...\n";

        $testAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'المدير',
                'last_name' => 'العام',
                'phone' => '+218912000000',
                'password' => bcrypt('superadmin123'),
                'user_type' => 'admin',
                'is_active' => true
            ]
        );

        $testAdmin->assignRole(['super_admin', 'admin']);
        $testAdmin->givePermissionTo(Permission::all());

        echo "✅ تم إنشاء المدير العام\n";
        echo "   البريد: <EMAIL>\n";
        echo "   كلمة المرور: superadmin123\n";

        echo "\n🎉 تم تحسين نظام الصلاحيات بنجاح!\n";
        echo "📊 إجمالي الصلاحيات: " . Permission::count() . "\n";
        echo "👥 إجمالي الأدوار: " . Role::count() . "\n";
        echo "🔑 المديرين مع صلاحيات كاملة: " . User::where('user_type', 'admin')->count() . "\n";
    }
}
