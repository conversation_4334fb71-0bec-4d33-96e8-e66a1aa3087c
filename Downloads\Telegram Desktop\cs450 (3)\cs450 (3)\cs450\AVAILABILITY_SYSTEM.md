# نظام التحقق من توفر المكونات

## نظرة عامة
تم تطوير نظام شامل للتحقق من توفر المكونات المطلوبة للمنتجات قبل السماح للعملاء بطلبها. هذا النظام يمنع طلب المنتجات التي لا تحتوي على مكونات كافية في المخزون.

## الميزات الجديدة

### 1. التحقق من توفر المكونات
- **في MenuItem Model**: تم إضافة methods جديدة:
  - `hasAvailableIngredients($quantity)`: للتحقق من توفر المكونات
  - `getMissingIngredients($quantity)`: للحصول على قائمة المكونات الناقصة
  - `isOrderable($quantity)`: للتحقق الشامل من إمكانية الطلب

### 2. التحقق عند إضافة للسلة
- **في CartController**: يتم التحقق من توفر المكونات قبل إضافة المنتج للسلة
- **رسائل خطأ واضحة**: تظهر للعميل سبب عدم إمكانية إضافة المنتج

### 3. التحقق عند إنشاء الطلب
- **في OrderController**: يتم التحقق من جميع المنتجات في السلة قبل إنشاء الطلب
- **منع الطلبات غير المكتملة**: لا يمكن إنشاء طلب يحتوي على منتجات غير متوفرة

### 4. فلترة القائمة للعملاء
- **في MenuController**: يتم إظهار المنتجات المتوفرة فقط للعملاء
- **تحديث تلقائي**: القائمة تتحدث تلقائياً بناءً على المخزون المتوفر

### 5. تقرير المنتجات الناقصة
- **صفحة إدارية جديدة**: `/admin/menu/low-stock-report`
- **عرض تفصيلي**: يظهر المنتجات والمكونات الناقصة لكل منتج

## كيفية الاستخدام

### للمطورين

#### 1. تشغيل Migration الجديد
```bash
php artisan migrate
```

#### 2. إضافة بيانات تجريبية
```bash
php artisan db:seed --class=TestAvailabilitySeeder
```

#### 3. اختبار النظام
1. اذهب إلى `/customer/menu` لرؤية القائمة المفلترة
2. جرب إضافة منتجات للسلة
3. اذهب إلى `/admin/menu/low-stock-report` لرؤية التقرير

### للمديرين

#### 1. مراقبة المخزون
- اذهب إلى "إدارة المخزون" لتحديث كميات المكونات
- استخدم تقرير "المنتجات الناقصة" لمعرفة ما يحتاج تجديد

#### 2. إدارة القائمة
- المنتجات تظهر/تختفي تلقائياً بناءً على توفر المكونات
- يمكن تعطيل المنتجات يدوياً من خلال `is_available`

### للعملاء

#### 1. تصفح القائمة
- ستظهر المنتجات المتوفرة فقط
- الأزرار المعطلة تشير إلى عدم التوفر

#### 2. إضافة للسلة
- رسائل واضحة عند عدم إمكانية الإضافة
- تحديث فوري لحالة الأزرار

## الملفات المحدثة

### Models
- `app/Models/MenuItem.php`: إضافة methods للتحقق من التوفر

### Controllers
- `app/Http/Controllers/CartController.php`: التحقق عند الإضافة والتحديث
- `app/Http/Controllers/OrderController.php`: التحقق عند إنشاء الطلب
- `app/Http/Controllers/MenuController.php`: فلترة القائمة وتقرير النقص

### Views
- `resources/views/customer/menu/index.blade.php`: تحسين تجربة المستخدم
- `resources/views/admin/reports/low-stock-items.blade.php`: تقرير جديد

### Database
- `database/migrations/2024_12_19_000003_add_inventory_indexes.php`: تحسين الأداء
- `database/seeders/TestAvailabilitySeeder.php`: بيانات تجريبية

### Routes
- `routes/web.php`: إضافة route للتقرير الجديد

## نصائح للاستخدام

### 1. تحسين الأداء
- تم إضافة فهارس على جدول المخزون
- استخدام eager loading للعلاقات

### 2. تجربة المستخدم
- رسائل خطأ واضحة ومفيدة
- تحديث فوري لحالة الأزرار
- إشعارات بصرية للمستخدم

### 3. إدارة المخزون
- مراقبة دورية للمكونات الناقصة
- تحديث المخزون بانتظام
- استخدام التقارير لاتخاذ قرارات الشراء

## استكشاف الأخطاء

### مشكلة: المنتجات لا تظهر للعملاء
**الحل**: تأكد من:
- `is_available = true` للمنتج
- وجود مكونات كافية في المخزون
- وجود وصفة (recipes) للمنتج

### مشكلة: رسائل خطأ عند إضافة للسلة
**الحل**: تحقق من:
- صحة العلاقات بين الجداول
- وجود بيانات في جدول المخزون
- صحة الكميات في الوصفات

## التطوير المستقبلي

### ميزات مقترحة
1. **تنبيهات تلقائية**: إشعارات عند نفاد المكونات
2. **حجز المكونات**: حجز مؤقت عند إضافة للسلة
3. **تقارير متقدمة**: تحليل استهلاك المكونات
4. **API للموردين**: ربط مع أنظمة الموردين

### تحسينات الأداء
1. **Cache**: تخزين مؤقت لنتائج التحقق
2. **Background Jobs**: معالجة التحديثات في الخلفية
3. **Real-time Updates**: تحديثات فورية عبر WebSockets
