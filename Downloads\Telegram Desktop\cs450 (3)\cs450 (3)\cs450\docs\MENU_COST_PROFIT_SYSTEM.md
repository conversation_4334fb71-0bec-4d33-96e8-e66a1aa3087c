# 💰 نظام حساب التكاليف والأرباح للمنيو

## 🎯 نظرة عامة

تم تطوير نظام شامل لربط جميع عناصر المنيو بالمكونات وحساب التكاليف والأرباح بدقة عالية. النظام يعتمد على المخزون الفعلي لحساب التكاليف ويوفر تحليلات مفصلة للربحية.

## ✨ الميزات الجديدة

### 🔗 ربط المنيو بالمكونات
- **وصفات مفصلة**: كل طبق مرتبط بمكوناته وكمياتها
- **حساب تلقائي**: التكلفة تُحسب تلقائياً من المخزون
- **تحديث فوري**: التكاليف تتحدث مع تغيير أسعار المخزون
- **مرونة عالية**: يمكن تعديل الوصفات بسهولة

### 📊 حساب التكاليف والأرباح
- **تكلفة دقيقة**: من متوسط أسعار المخزون المتاح
- **هامش الربح**: نسبة الربح لكل طبق
- **الربح الصافي**: الربح بالدينار الليبي
- **تحليل الربحية**: مقارنة الأطباق حسب الربحية

### 🎛️ لوحة إدارة التكاليف
- **عرض شامل**: جميع الأطباق وتكاليفها في مكان واحد
- **إحصائيات سريعة**: عدد الأطباق والوصفات والأرباح
- **تحليل مرئي**: مؤشرات بصرية للربحية
- **إجراءات سريعة**: روابط مباشرة للتعديل

## 🏗️ البنية التقنية

### النماذج (Models)

#### MenuItem Model - تحسينات جديدة:
```php
/**
 * حساب تكلفة المكونات للطبق الواحد من المخزون
 */
public function calculateIngredientsCost()
{
    $totalCost = 0;

    foreach ($this->recipe as $recipe) {
        // حساب متوسط تكلفة المكون من المخزون المتاح
        $avgCost = Inventory::where('ingredient_id', $recipe->ingredient_id)
            ->where('quantity', '>', 0)
            ->avg('cost_per_unit') ?? 0;
        
        $totalCost += $recipe->quantity * $avgCost;
    }

    return round($totalCost, 2);
}

/**
 * حساب هامش الربح للطبق
 */
public function calculateProfitMargin()
{
    $cost = $this->calculateIngredientsCost();
    
    if ($this->price <= 0) {
        return 0;
    }

    $profit = $this->price - $cost;
    return round(($profit / $this->price) * 100, 1);
}

/**
 * حساب الربح الصافي للطبق الواحد
 */
public function calculateProfit()
{
    return round($this->price - $this->calculateIngredientsCost(), 2);
}

/**
 * الحصول على تفاصيل التكلفة والربح
 */
public function getCostAnalysis()
{
    $ingredientsCost = $this->calculateIngredientsCost();
    $profit = $this->calculateProfit();
    $profitMargin = $this->calculateProfitMargin();

    return [
        'price' => $this->price,
        'ingredients_cost' => $ingredientsCost,
        'profit' => $profit,
        'profit_margin' => $profitMargin,
        'has_recipe' => $this->hasRecipe(),
        'cost_percentage' => $this->price > 0 ? round(($ingredientsCost / $this->price) * 100, 1) : 0
    ];
}
```

### المتحكمات (Controllers)

#### MenuController - دوال جديدة:
```php
/**
 * صفحة إدارة الوصفات والتكاليف
 */
public function recipesManagement()
{
    $menuItems = MenuItem::with(['recipe.ingredient', 'recipe.ingredient.inventory'])
        ->orderBy('name')
        ->get();

    return view('admin.menu.recipes', compact('menuItems'));
}
```

#### ReportController - تحسينات:
```php
/**
 * حساب تكلفة منتج واحد بناءً على المكونات من المخزون
 */
private function calculateProductCost($menuItemId)
{
    $menuItem = MenuItem::with('recipe')->find($menuItemId);
    
    if (!$menuItem) {
        return 0;
    }

    return $menuItem->calculateIngredientsCost();
}
```

## 📱 واجهات المستخدم

### 🏠 صفحة المنيو الرئيسية
- **عرض محسن**: التكلفة والربح لكل طبق
- **مؤشرات بصرية**: ألوان تدل على الربحية
- **تنبيهات**: للأطباق بدون وصفات
- **رابط سريع**: لإدارة التكاليف

#### مثال العرض:
```
🍕 بيتزا مارجريتا                    25.00 د.ل
   التكلفة: 8.50 د.ل
   الربح: 16.50 د.ل (66.0%)
```

### 📊 صفحة إدارة التكاليف
- **جدول شامل**: جميع الأطباق وتفاصيل التكلفة
- **إحصائيات**: عدد الأطباق، الوصفات، متوسط الربح
- **تحليل الربحية**: أكثر الأطباق ربحية
- **أطباق تحتاج مراجعة**: بدون وصفات أو ربح منخفض

#### الإحصائيات المعروضة:
- 📊 **إجمالي الأطباق**: عدد عناصر المنيو
- ✅ **لها وصفات**: الأطباق المرتبطة بمكونات
- ⚠️ **بدون وصفات**: تحتاج إضافة مكونات
- 📈 **متوسط الربح**: هامش الربح العام

### 🎨 المؤشرات البصرية

#### ألوان هامش الربح:
- 🟢 **أخضر**: أكثر من 50% (ربح ممتاز)
- 🟡 **أصفر**: 25-50% (ربح جيد)
- 🔴 **أحمر**: أقل من 25% (ربح منخفض)

#### شريط التقدم:
```
الهامش: 66.0% ████████████████░░░░
```

## 🔄 تدفق العمل

### 1. إضافة طبق جديد:
```
إضافة طبق → تحديد السعر → إضافة وصفة → ربط المكونات → حساب التكلفة تلقائياً
```

### 2. تحديث التكاليف:
```
تحديث أسعار المخزون → إعادة حساب تكاليف الأطباق تلقائياً → تحديث الأرباح
```

### 3. تحليل الربحية:
```
عرض جميع الأطباق → ترتيب حسب الربحية → تحديد الأطباق المربحة/غير المربحة
```

## 📈 التقارير والتحليلات

### تقرير الربحية:
- **أكثر الأطباق ربحية**: الأطباق ذات أعلى هامش ربح
- **أطباق تحتاج مراجعة**: ربح منخفض أو بدون وصفة
- **متوسط الربح العام**: لجميع الأطباق
- **توزيع الأطباق**: حسب فئات الربحية

### مؤشرات الأداء:
- **نسبة الأطباق المربحة**: كم طبق يحقق ربح جيد
- **متوسط التكلفة**: نسبة التكلفة من السعر
- **الأطباق بدون وصفات**: تحتاج إضافة مكونات

## 🛠️ الملفات المحدثة

### النماذج:
- `app/Models/MenuItem.php` - دوال حساب التكلفة والربح

### المتحكمات:
- `app/Http/Controllers/MenuController.php` - صفحة إدارة التكاليف
- `app/Http/Controllers/ReportController.php` - تحسين حساب التكاليف

### العروض:
- `resources/views/admin/menu/index.blade.php` - عرض التكاليف والأرباح
- `resources/views/admin/menu/show.blade.php` - تفاصيل الطبق
- `resources/views/admin/menu/recipes.blade.php` - صفحة إدارة التكاليف (جديدة)

### المسارات:
- `routes/web.php` - مسار صفحة إدارة التكاليف

## 🎯 الفوائد المحققة

### للإدارة:
- **رؤية واضحة**: للتكاليف والأرباح لكل طبق
- **قرارات مدروسة**: تسعير الأطباق بناءً على التكلفة الفعلية
- **تحسين الربحية**: تحديد الأطباق الأكثر ربحية
- **تحكم أفضل**: في هوامش الربح

### للمطعم:
- **دقة عالية**: في حساب التكاليف
- **توفير الوقت**: حسابات تلقائية
- **تقليل الهدر**: معرفة التكلفة الحقيقية
- **زيادة الأرباح**: تحسين التسعير

### للموظفين:
- **سهولة الاستخدام**: واجهات بديهية
- **معلومات واضحة**: عرض مفصل للتكاليف
- **إجراءات سريعة**: روابط مباشرة للتعديل
- **تحليلات مفيدة**: لاتخاذ القرارات

## 🔮 التطوير المستقبلي

### ميزات مقترحة:
- **تنبيهات ذكية**: للأطباق ذات الربح المنخفض
- **تحليل الاتجاهات**: تغيير التكاليف عبر الزمن
- **مقارنة الفترات**: ربحية الأطباق في فترات مختلفة
- **تحسين الوصفات**: اقتراحات لتقليل التكلفة

### تحسينات تقنية:
- **ذاكرة التخزين المؤقت**: لتسريع حسابات التكلفة
- **حسابات متوازية**: للمطاعم الكبيرة
- **تصدير التقارير**: PDF وExcel
- **واجهة برمجة التطبيقات**: للتكامل مع أنظمة أخرى

## 📞 الدعم والمساعدة

### للحصول على المساعدة:
- **التوثيق**: راجع هذا الدليل
- **الدعم الفني**: <EMAIL>
- **التدريب**: دورات تدريبية متاحة
- **المجتمع**: منتدى المستخدمين

---

## 🎉 الخلاصة

تم تطوير نظام شامل ومتطور لحساب التكاليف والأرباح يوفر:

✅ **ربط كامل** بين المنيو والمكونات  
✅ **حسابات دقيقة** للتكاليف من المخزون  
✅ **تحليلات مفصلة** للربحية  
✅ **واجهات سهلة** الاستخدام  
✅ **تقارير شاملة** لاتخاذ القرارات  

النظام جاهز للاستخدام ويوفر رؤية واضحة وشاملة لربحية المطعم! 🚀
