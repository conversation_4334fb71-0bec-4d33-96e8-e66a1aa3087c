<!-- صفحة لوحة التحكم للمستخدم المسجل -->
@auth
<div id="dashboard-page" class="page hidden">
    <div class="container mx-auto px-4 py-8">
        <div class="mb-6">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="p-6 bg-gradient-to-r from-primary/90 to-primary text-white">
                    <div class="flex flex-col md:flex-row justify-between items-center">
                        <div>
                            <h2 class="text-2xl font-bold mb-2">مرحباً، {{ auth()->user()->first_name ?? 'المستخدم' }} {{ auth()->user()->last_name ?? '' }}</h2>
                            <p class="text-white/90">نحن سعداء برؤيتك مرة أخرى!</p>
                        </div>
                        <div class="mt-4 md:mt-0">
                            <a href="#" data-page="menu" class="bg-white text-primary hover:bg-gray-100 font-bold py-2 px-6 rounded-full transition duration-200 inline-block">
                                تصفح القائمة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 p-6">
                    <div class="bg-white dark:bg-gray-700 rounded-lg shadow p-4 flex items-center">
                        <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3 ml-4">
                            <i class="fas fa-clipboard-list text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-sm">إجمالي الطلبات</p>
                            <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ isset($orderStats['totalOrders']) ? $orderStats['totalOrders'] : 8 }}</h3>
                        </div>
                    </div>
                    <div class="bg-white dark:bg-gray-700 rounded-lg shadow p-4 flex items-center">
                        <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3 ml-4">
                            <i class="fas fa-calendar-check text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-sm">حجوزات نشطة</p>
                            <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ isset($upcomingReservations) ? $upcomingReservations->count() : 1 }}</h3>
                        </div>
                    </div>
                    <div class="bg-white dark:bg-gray-700 rounded-lg shadow p-4 flex items-center">
                        <div class="rounded-full bg-purple-100 dark:bg-purple-900/30 p-3 ml-4">
                            <i class="fas fa-star text-purple-500 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-sm">تقييماتي</p>
                            <h3 class="text-2xl font-bold text-gray-800 dark:text-white">8</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <!-- آخر الطلبات -->
            <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">آخر الطلبات</h3>
                    <a href="#" data-page="orders" class="text-primary text-sm hover:underline">عرض الكل</a>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full text-sm">
                        <thead>
                            <tr class="bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                                <th class="py-2 px-3 text-right">#</th>
                                <th class="py-2 px-3 text-right">التاريخ</th>
                                <th class="py-2 px-3 text-right">المبلغ</th>
                                <th class="py-2 px-3 text-right">الحالة</th>
                                <th class="py-2 px-3 text-right">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                            @if(isset($recentOrders) && count($recentOrders) > 0)
                                @foreach($recentOrders as $order)
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                                    <td class="py-3 px-3">{{ $order->order_number ?? '#' . $order->order_id }}</td>
                                    <td class="py-3 px-3">{{ $order->created_at->diffForHumans() ?? 'قبل 3 أيام' }}</td>
                                    <td class="py-3 px-3">{{ $order->total_amount ?? '85' }} د.ل</td>
                                    <td class="py-3 px-3">
                                        <span class="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full text-xs">
                                            {{ $order->status == 'completed' ? 'مكتمل' : 'قيد التجهيز' }}
                                        </span>
                                    </td>
                                    <td class="py-3 px-3">
                                        <button class="text-primary hover:text-primary/80">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                @endforeach
                            @else
                            <!-- طلبات افتراضية -->
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                                <td class="py-3 px-3">#1254</td>
                                <td class="py-3 px-3">قبل 3 أيام</td>
                                <td class="py-3 px-3">85 ر.س</td>
                                <td class="py-3 px-3"><span class="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full text-xs">مكتمل</span></td>
                                <td class="py-3 px-3">
                                    <button class="text-primary hover:text-primary/80">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                                <td class="py-3 px-3">#1253</td>
                                <td class="py-3 px-3">قبل أسبوع</td>
                                <td class="py-3 px-3">120 ر.س</td>
                                <td class="py-3 px-3"><span class="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full text-xs">مكتمل</span></td>
                                <td class="py-3 px-3">
                                    <button class="text-primary hover:text-primary/80">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            @endif
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- حجوزات قادمة -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">حجوزات قادمة</h3>
                    <a href="#" data-page="reservations" class="text-primary text-sm hover:underline">عرض الكل</a>
                </div>
                <div class="space-y-4">
                    @if(isset($upcomingReservations) && count($upcomingReservations) > 0)
                        @foreach($upcomingReservations as $reservation)
                        <div class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border-r-4 border-blue-500">
                            <div class="flex justify-between">
                                <div>
                                    <p class="font-bold text-gray-800 dark:text-white">طاولة #{{ $reservation->table->table_number ?? '8' }}</p>
                                    <p class="text-sm text-gray-600 dark:text-gray-300">{{ $reservation->table->capacity ?? '2' }} أشخاص</p>
                                </div>
                                <div class="text-right">
                                    <p class="font-bold text-gray-800 dark:text-white">{{ $reservation->reservation_time->format('d/m H:i') ?? 'غداً 19:00' }}</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">لمدة {{ $reservation->duration ?? '60' }} دقيقة</p>
                                </div>
                            </div>
                            <div class="mt-2 flex justify-end">
                                <button class="text-red-500 hover:text-red-700 text-sm">
                                    <i class="fas fa-times ml-1"></i>إلغاء
                                </button>
                            </div>
                        </div>
                        @endforeach
                    @else
                    <!-- حجز افتراضي -->
                    <div class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border-r-4 border-blue-500">
                        <div class="flex justify-between">
                            <div>
                                <p class="font-bold text-gray-800 dark:text-white">طاولة #8</p>
                                <p class="text-sm text-gray-600 dark:text-gray-300">2 أشخاص</p>
                            </div>
                            <div class="text-right">
                                <p class="font-bold text-gray-800 dark:text-white">غداً 19:00</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">لمدة ساعة</p>
                            </div>
                        </div>
                        <div class="mt-2 flex justify-end">
                            <button class="text-red-500 hover:text-red-700 text-sm">
                                <i class="fas fa-times ml-1"></i>إلغاء
                            </button>
                        </div>
                    </div>
                    @endif

                    <div class="mt-4 flex justify-center">
                        <a href="#" data-page="reservations" class="bg-primary/10 text-primary hover:bg-primary/20 font-medium py-2 px-4 rounded-lg transition">
                            <i class="fas fa-plus ml-1"></i>حجز طاولة جديدة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- الأطباق المفضلة -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">الأطباق المفضلة</h3>
                    <a href="#" data-page="menu" class="text-primary text-sm hover:underline">اطلب الآن</a>
                </div>
                <div class="space-y-4">
                    @if(isset($orderStats['favoriteItems']) && count($orderStats['favoriteItems']) > 0)
                        @foreach($orderStats['favoriteItems'] as $item)
                        <div class="flex items-center">
                            <img src="https://images.unsplash.com/photo-1513104890138-7c749659a591?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80" alt="{{ $item->name }}" class="w-12 h-12 object-cover rounded-md ml-3">
                            <div class="flex-1">
                                <p class="font-medium text-gray-800 dark:text-white">{{ $item->name }}</p>
                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ $item->total }} مرات</p>
                            </div>
                            <button class="text-primary hover:text-primary/80 px-2 py-1 rounded-full bg-primary/10 hover:bg-primary/20">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        @endforeach
                    @else
                    <!-- أطباق افتراضية -->
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1513104890138-7c749659a591?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80" alt="برجر لحم" class="w-12 h-12 object-cover rounded-md ml-3">
                        <div class="flex-1">
                            <p class="font-medium text-gray-800 dark:text-white">برجر لحم أنجوس</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">5 مرات</p>
                        </div>
                        <button class="text-primary hover:text-primary/80 px-2 py-1 rounded-full bg-primary/10 hover:bg-primary/20">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ae38?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80" alt="بيتزا" class="w-12 h-12 object-cover rounded-md ml-3">
                        <div class="flex-1">
                            <p class="font-medium text-gray-800 dark:text-white">بيتزا سوبريم</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">3 مرات</p>
                        </div>
                        <button class="text-primary hover:text-primary/80 px-2 py-1 rounded-full bg-primary/10 hover:bg-primary/20">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    @endif
                </div>
            </div>

            <!-- الإشعارات -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">الإشعارات</h3>
                    <a href="#" data-page="notifications" class="text-primary text-sm hover:underline">عرض الكل</a>
                </div>
                <div class="space-y-4">
                    @if(isset($notifications) && count($notifications) > 0)
                        @foreach($notifications as $notification)
                    <div class="border-b border-gray-200 dark:border-gray-700 pb-3">
                        <div class="flex items-start">
                            <div class="w-2 h-2 bg-primary rounded-full mt-2 ml-3 flex-shrink-0"></div>
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-800 dark:text-white text-sm">{{ $notification->title }}</h4>
                                <p class="text-gray-600 dark:text-gray-400 text-xs mt-1">{{ $notification->message }}</p>
                                <p class="text-gray-500 dark:text-gray-500 text-xs mt-1">{{ $notification->created_at->diffForHumans() }}</p>
                            </div>
                        </div>
                    </div>
                        @endforeach
                    @else
                    <!-- إشعارات افتراضية -->
                    <div class="border-b border-gray-200 dark:border-gray-700 pb-3">
                        <div class="flex items-start">
                            <div class="w-2 h-2 bg-primary rounded-full mt-2 ml-3 flex-shrink-0"></div>
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-800 dark:text-white text-sm">تأكيد الطلب</h4>
                                <p class="text-gray-600 dark:text-gray-400 text-xs mt-1">تم تأكيد طلبك وجاري تجهيزه</p>
                                <p class="text-gray-500 dark:text-gray-500 text-xs mt-1">قبل ساعتين</p>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">إحصائيات سريعة</h3>
                </div>
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-400 text-sm">إجمالي المبلغ المنفق</span>
                        <span class="font-bold text-gray-800 dark:text-white">{{ isset($orderStats['totalSpent']) ? $orderStats['totalSpent'] : '750.25' }} د.ل</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-400 text-sm">متوسط الطلب</span>
                        <span class="font-bold text-gray-800 dark:text-white">{{ round((isset($orderStats['totalSpent']) ? $orderStats['totalSpent'] : 750.25) / (isset($orderStats['totalOrders']) ? $orderStats['totalOrders'] : 8), 2) }} د.ل</span>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
@endauth

@guest
<!-- صفحة تسجيل الدخول للمستخدمين غير المسجلين -->
<div id="dashboard-page" class="page hidden">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
            <div class="w-20 h-20 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-user-lock text-primary text-3xl"></i>
            </div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">تسجيل الدخول مطلوب</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
                يجب تسجيل الدخول للوصول إلى لوحة التحكم الخاصة بك
            </p>
            <div class="space-y-4">
                <button id="loginBtn" class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition">
                    <i class="fas fa-sign-in-alt ml-2"></i>
                    تسجيل الدخول
                </button>
                <button data-page="home" class="w-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-white font-bold py-3 px-6 rounded-lg transition">
                    <i class="fas fa-home ml-2"></i>
                    العودة للرئيسية
                </button>
            </div>
        </div>
    </div>
</div>
@endguest
