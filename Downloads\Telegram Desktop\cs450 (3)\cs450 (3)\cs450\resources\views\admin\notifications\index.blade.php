@extends('layouts.admin')

@section('title', 'إدارة الإشعارات')

@section('content')
<div id="notifications-page" class="page fade-in">
    <div class="mb-4 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h2 class="text-xl font-bold text-gray-800 dark:text-white mb-1">إدارة الإشعارات</h2>
            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <a href="{{ route('admin.dashboard') }}" class="hover:text-primary">لوحة التحكم</a>
                <i class="fas fa-chevron-left mx-2 text-xs"></i>
                <span>إدارة الإشعارات</span>
            </div>
        </div>
        <div class="mt-3 md:mt-0 flex space-x-2 space-x-reverse">
            <a href="{{ route('admin.notifications.create') }}" class="inline-flex items-center px-3 py-2 bg-primary hover:bg-primary/90 text-white rounded-md transition-colors text-sm">
                <i class="fas fa-plus ml-2"></i>
                <span>إرسال إشعار جديد</span>
            </a>
            <form action="{{ route('admin.notifications.mark-all-as-read') }}" method="POST" class="inline">
                @csrf
                <button type="submit" class="inline-flex items-center px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors text-sm">
                    <i class="fas fa-check-double ml-2"></i>
                    <span>تحديد الكل كمقروء</span>
                </button>
            </form>
            <!-- <form action="{{ route('admin.inventory.cleanup-notifications') }}" method="POST" class="inline">
                @csrf
                <button type="submit" class="inline-flex items-center px-3 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md transition-colors text-sm"
                        onclick="return confirm('هل أنت متأكد من تنظيف التنبيهات المحلولة والمكررة؟')">
                    <i class="fas fa-broom ml-2"></i>
                    <span>تنظيف التنبيهات</span>
                </button>
            </form> -->
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
        <div class="lg:col-span-3">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="flex border-b border-gray-200 dark:border-gray-700">
                    <a href="{{ route('admin.notifications') }}" class="px-6 py-3 {{ request()->routeIs('admin.notifications') && !request()->query('filter') ? 'border-b-2 border-primary text-primary font-medium' : 'text-gray-600 dark:text-gray-300 font-medium hover:text-primary' }}">
                        جميع الإشعارات
                    </a>
                    <a href="{{ route('admin.notifications', ['filter' => 'unread']) }}" class="px-6 py-3 {{ request()->query('filter') == 'unread' ? 'border-b-2 border-primary text-primary font-medium' : 'text-gray-600 dark:text-gray-300 font-medium hover:text-primary' }}">
                        غير مقروءة
                    </a>
                    <a href="{{ route('admin.notifications', ['filter' => 'read']) }}" class="px-6 py-3 {{ request()->query('filter') == 'read' ? 'border-b-2 border-primary text-primary font-medium' : 'text-gray-600 dark:text-gray-300 font-medium hover:text-primary' }}">
                        مقروءة
                    </a>
                </div>

                <div class="divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($notifications as $notification)
                        <div class="p-3 hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors {{ $notification->is_read ? '' : 'bg-blue-50 dark:bg-blue-900/10' }}">
                            <div class="flex items-start">
                                <div class="flex-shrink-0 ml-3">
                                    @if($notification->type == 'order')
                                        <div class="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-500 dark:text-blue-300">
                                            <i class="fas fa-shopping-cart text-sm"></i>
                                        </div>
                                    @elseif($notification->type == 'reservation')
                                        <div class="h-8 w-8 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center text-green-500 dark:text-green-300">
                                            <i class="fas fa-calendar-check text-sm"></i>
                                        </div>
                                    @elseif($notification->type == 'inventory')
                                        <div class="h-8 w-8 rounded-full bg-yellow-100 dark:bg-yellow-900/30 flex items-center justify-center text-yellow-500 dark:text-yellow-300">
                                            <i class="fas fa-box text-sm"></i>
                                        </div>
                                    @elseif($notification->type == 'system')
                                        <div class="h-8 w-8 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center text-purple-500 dark:text-purple-300">
                                            <i class="fas fa-cog text-sm"></i>
                                        </div>
                                    @elseif($notification->type == 'contact')
                                        <div class="h-8 w-8 rounded-full bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center text-orange-500 dark:text-orange-300">
                                            <i class="fas fa-envelope text-sm"></i>
                                        </div>
                                    @else
                                        <div class="h-8 w-8 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-300">
                                            <i class="fas fa-bell text-sm"></i>
                                        </div>
                                    @endif
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center justify-between">
                                        <h3 class="text-sm font-medium text-gray-800 dark:text-white">
                                            {{ $notification->title }}
                                        </h3>
                                        <span class="text-xs text-gray-500 dark:text-gray-400">
                                            {{ $notification->created_at->diffForHumans() }}
                                        </span>
                                    </div>
                                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-300">
                                        {{ $notification->message }}
                                    </p>
                                    @if($notification->user)
                                    <div class="mt-1 flex items-center text-xs text-gray-500 dark:text-gray-400">
                                        <i class="fas fa-user ml-1"></i>
                                        <span>{{ $notification->user->first_name }} {{ $notification->user->last_name }}</span>
                                        <span class="mx-2">•</span>
                                        <span>{{ $notification->user->email }}</span>
                                    </div>
                                    @endif
                                    <div class="mt-2 flex items-center justify-between">
                                        @if($notification->action_url)
                                            <a href="{{ $notification->action_url }}" class="text-xs text-primary hover:underline">
                                                {{ $notification->action_text ?: 'عرض التفاصيل' }}
                                            </a>
                                        @else
                                            <a href="{{ route('admin.notifications.show', $notification->notification_id) }}" class="text-xs text-primary hover:underline">
                                                عرض التفاصيل
                                            </a>
                                        @endif

                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            @if(!$notification->is_read)
                                                <form action="{{ route('admin.notifications.mark-as-read', $notification->notification_id) }}" method="POST" class="inline">
                                                    @csrf
                                                    <button type="submit" class="text-xs text-gray-500 dark:text-gray-400 hover:text-primary">
                                                        تحديد كمقروء
                                                    </button>
                                                </form>
                                            @endif
                                            <form action="{{ route('admin.notifications.delete', $notification->notification_id) }}" method="POST" class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-xs text-red-500 hover:text-red-700">
                                                    حذف
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="p-6 text-center">
                            <div class="inline-flex items-center justify-center h-12 w-12 rounded-full bg-gray-100 dark:bg-gray-700 mb-3">
                                <i class="fas fa-bell-slash text-lg text-gray-500 dark:text-gray-400"></i>
                            </div>
                            <h3 class="text-md font-medium text-gray-800 dark:text-white mb-1">لا توجد إشعارات</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                ستظهر هنا جميع الإشعارات الخاصة بك عندما تصل.
                            </p>
                        </div>
                    @endforelse
                </div>

                @if($notifications->count() > 0)
                    <div class="px-3 py-2 border-t border-gray-200 dark:border-gray-700">
                        {{ $notifications->links() }}
                    </div>
                @endif
            </div>
        </div>

        <div class="lg:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="border-b border-gray-200 dark:border-gray-700 px-4 py-3">
                    <h3 class="text-md font-bold text-gray-800 dark:text-white flex items-center">
                        <i class="fas fa-chart-pie text-primary ml-2"></i>
                        إحصائيات الإشعارات
                    </h3>
                </div>
                <div class="p-4">
                    <div class="space-y-3">
                        <div class="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-3">
                            <h4 class="text-sm font-semibold text-gray-800 dark:text-white mb-2">ملخص الإشعارات</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-600 dark:text-gray-300">إجمالي الإشعارات:</span>
                                    <span class="font-bold text-primary">{{ $stats['total'] }}</span>
                                </div>
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-600 dark:text-gray-300">غير مقروءة:</span>
                                    <span class="font-bold text-red-500">{{ $stats['unread'] }}</span>
                                </div>
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-600 dark:text-gray-300">مقروءة:</span>
                                    <span class="font-bold text-green-500">{{ $stats['read'] }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-3">
                            <h4 class="text-sm font-semibold text-gray-800 dark:text-white mb-2">حسب النوع</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-600 dark:text-gray-300">إشعارات النظام:</span>
                                    <span class="font-bold text-purple-500">{{ $stats['system'] }}</span>
                                </div>
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-600 dark:text-gray-300">إشعارات الطلبات:</span>
                                    <span class="font-bold text-blue-500">{{ $stats['order'] }}</span>
                                </div>
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-600 dark:text-gray-300">إشعارات الحجوزات:</span>
                                    <span class="font-bold text-green-500">{{ $stats['reservation'] }}</span>
                                </div>
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-600 dark:text-gray-300">إشعارات المخزون:</span>
                                    <span class="font-bold text-yellow-500">{{ $stats['inventory'] }}</span>
                                </div>
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-600 dark:text-gray-300">رسائل الاتصال:</span>
                                    <span class="font-bold text-orange-500">{{ $stats['contact'] ?? 0 }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <a href="{{ route('admin.notifications.create') }}" class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-2 px-3 rounded-md flex items-center justify-center transition-all text-sm">
                                <i class="fas fa-paper-plane ml-2"></i>
                                <span>إرسال إشعار جديد</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
