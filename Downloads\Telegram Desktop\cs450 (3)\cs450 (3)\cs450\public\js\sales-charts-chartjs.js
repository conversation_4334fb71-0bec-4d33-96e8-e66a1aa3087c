// Chart.js implementation for dynamic charts
(function () {
    function toNumber(v) {
        var n = parseFloat(v);
        return isNaN(n) ? 0 : n;
    }

    function initDaily(ctx, data) {
        var labels = data.map(function (i) {
            return i.date;
        });
        var values = data.map(function (i) {
            return toNumber(i.total_sales || i.total || i.value || i.y || 0);
        });
        if (labels.length === 0) {
            labels = ["لا توجد بيانات"];
            values = [0];
        }

        return new Chart(ctx, {
            type: "line",
            data: {
                labels: labels,
                datasets: [
                    {
                        label: "المبيعات اليومية",
                        data: values,
                        borderColor: "#3B82F6",
                        backgroundColor: "rgba(59,130,246,0.2)",
                        tension: 0.3,
                    },
                ],
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        callbacks: {
                            label: function (c) {
                                return c.formattedValue + " د.ل";
                            },
                        },
                    },
                },
                scales: { y: { ticks: { callback: (v) => v + " د.ل" } } },
            },
        });
    }

    function initCategory(ctx, data) {
        var catMap = {
            main: "الأطباق الرئيسية",
            "main course": "الأطباق الرئيسية",
            mains: "الأطباق الرئيسية",
            appetizer: "المقبلات",
            starters: "المقبلات",
            beverage: "المشروبات",
            drink: "المشروبات",
            drinks: "المشروبات",
            dessert: "الحلويات",
            sweets: "الحلويات",
            other: "أخرى",
            others: "أخرى",
        };
        function t(cat) {
            if (!cat) return "غير محدد";
            var raw = String(cat).trim();
            var k = raw.toLowerCase();
            if (catMap[k]) return catMap[k];
            var isArabic = /[\u0600-\u06FF]/.test(raw);
            return isArabic ? raw : "أخرى";
        }

        var labels = data.map(function (i) {
            return t(i.category || i.name || "غير محدد");
        });
        var values = data.map(function (i) {
            return toNumber(i.total || i.value);
        });
        if (labels.length === 0) {
            labels = ["لا توجد بيانات"];
            values = [0];
        }

        return new Chart(ctx, {
            type: "doughnut",
            data: {
                labels: labels,
                datasets: [
                    {
                        data: values,
                        backgroundColor: [
                            "#3B82F6",
                            "#10B981",
                            "#F59E0B",
                            "#EF4444",
                            "#8B5CF6",
                            "#06B6D4",
                        ],
                    },
                ],
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: "bottom" },
                    tooltip: {
                        callbacks: { label: (c) => c.formattedValue + " د.ل" },
                    },
                },
            },
        });
    }

    window.initSalesChartsChartJS = function () {
        try {
            var daily =
                window.salesData && Array.isArray(window.salesData.daily)
                    ? window.salesData.daily
                    : [];
            var cat =
                window.salesData && Array.isArray(window.salesData.category)
                    ? window.salesData.category
                    : [];

            var dailyCanvas = document.getElementById("dailySalesCanvas");
            var catCanvas = document.getElementById("categoryCanvas");

            if (dailyCanvas && window.Chart) {
                initDaily(dailyCanvas.getContext("2d"), daily);
            }
            if (catCanvas && window.Chart) {
                initCategory(catCanvas.getContext("2d"), cat);
            }
        } catch (e) {
            console.error("Chart.js init error", e);
        }
    };
})();
