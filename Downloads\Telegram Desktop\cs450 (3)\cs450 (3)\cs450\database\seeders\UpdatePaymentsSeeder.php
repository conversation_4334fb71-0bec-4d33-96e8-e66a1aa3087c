<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Payment;
use App\Models\User;

class UpdatePaymentsSeeder extends Seeder
{
    public function run()
    {
        // البحث عن مستخدم admin
        $adminUser = User::where('user_type', 'admin')->first();
        
        if ($adminUser) {
            // تحديث جميع المدفوعات التي لا تحتوي على employee_id
            $updated = Payment::whereNull('employee_id')->update([
                'employee_id' => $adminUser->user_id
            ]);
            
            $this->command->info("تم تحديث {$updated} دفعة بمعرف الموظف: {$adminUser->user_id}");
        } else {
            $this->command->error('لم يتم العثور على مستخدم admin');
        }
    }
}
