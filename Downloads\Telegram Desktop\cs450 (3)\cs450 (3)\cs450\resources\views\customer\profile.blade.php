@extends('customer.layouts.app')

@section('title', 'الملف الشخصي - Eat Hub')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Unified User Header -->
    @include('customer.partials.user-header', [
        'title' => 'الملف الشخصي',
        'showStats' => true
    ])

    <div class="container mx-auto px-4 pb-8">
        <div class="max-w-6xl mx-auto">

            <!-- Navigation Tabs -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg mb-8 overflow-hidden">
                <div class="flex flex-wrap border-b border-gray-200 dark:border-gray-700">
                    <button onclick="showTab('personal')" id="personal-tab" class="tab-button flex-1 px-6 py-4 text-center font-medium transition-colors duration-200 bg-primary text-white border-b-2 border-primary">
                        <i class="fas fa-user ml-2"></i>
                        المعلومات الشخصية
                    </button>
                    <button onclick="showTab('password')" id="password-tab" class="tab-button flex-1 px-6 py-4 text-center font-medium transition-colors duration-200 text-gray-600 dark:text-gray-400 hover:text-primary hover:bg-gray-50 dark:hover:bg-gray-700">
                        <i class="fas fa-lock ml-2"></i>
                        كلمة المرور
                    </button>
                    <button onclick="showTab('stats')" id="stats-tab" class="tab-button flex-1 px-6 py-4 text-center font-medium transition-colors duration-200 text-gray-600 dark:text-gray-400 hover:text-primary hover:bg-gray-50 dark:hover:bg-gray-700">
                        <i class="fas fa-chart-bar ml-2"></i>
                        الإحصائيات
                    </button>
                </div>
            </div>

            <!-- Tab Content -->
            <div class="space-y-8">
                <!-- Personal Information Tab -->
                <div id="personal-content" class="tab-content">
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">

                        <div class="mb-8">
                            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">المعلومات الشخصية</h2>
                            <p class="text-gray-600 dark:text-gray-400">قم بتحديث معلوماتك الشخصية هنا</p>
                        </div>

                        <!-- رسائل النجاح والخطأ -->
                        @if(session('success'))
                            <div class="mb-6 bg-green-50 dark:bg-green-900/20 border-l-4 border-green-400 p-4 rounded-lg">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-check-circle text-green-400 text-xl"></i>
                                    </div>
                                    <div class="mr-3">
                                        <p class="text-green-700 dark:text-green-300 font-medium">{{ session('success') }}</p>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($errors->any())
                            <div class="mb-6 bg-red-50 dark:bg-red-900/20 border-l-4 border-red-400 p-4 rounded-lg">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-exclamation-triangle text-red-400 text-xl"></i>
                                    </div>
                                    <div class="mr-3">
                                        <h3 class="text-red-800 dark:text-red-300 font-medium mb-2">يرجى تصحيح الأخطاء التالية:</h3>
                                        <ul class="list-disc list-inside space-y-1 text-red-700 dark:text-red-300">
                                            @foreach($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- نموذج تحديث المعلومات -->
                        <form action="{{ route('customer.profile.update') }}" method="POST" class="space-y-8">
                            @csrf
                            @method('PUT')

                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                <div class="space-y-6">
                                    <div>
                                        <label for="first_name" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                            <i class="fas fa-user ml-2 text-primary"></i>الاسم الأول
                                        </label>
                                        <input type="text" id="first_name" name="first_name" value="{{ old('first_name', auth()->user()->first_name ?? '') }}" required
                                               class="w-full px-4 py-4 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary dark:bg-gray-700 dark:text-white transition-all duration-200 @error('first_name') border-red-400 @enderror">
                                        @error('first_name')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400 flex items-center">
                                                <i class="fas fa-exclamation-circle ml-1"></i>{{ $message }}
                                            </p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="last_name" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                            <i class="fas fa-user ml-2 text-primary"></i>الاسم الأخير
                                        </label>
                                        <input type="text" id="last_name" name="last_name" value="{{ old('last_name', auth()->user()->last_name ?? '') }}" required
                                               class="w-full px-4 py-4 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary dark:bg-gray-700 dark:text-white transition-all duration-200 @error('last_name') border-red-400 @enderror">
                                        @error('last_name')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400 flex items-center">
                                                <i class="fas fa-exclamation-circle ml-1"></i>{{ $message }}
                                            </p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="phone" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                            <i class="fas fa-phone ml-2 text-primary"></i>رقم الهاتف
                                        </label>
                                        <input type="tel" id="phone" name="phone" value="{{ old('phone', auth()->user()->phone ?? '') }}" required
                                               class="w-full px-4 py-4 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary dark:bg-gray-700 dark:text-white transition-all duration-200 @error('phone') border-red-400 @enderror">
                                        @error('phone')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400 flex items-center">
                                                <i class="fas fa-exclamation-circle ml-1"></i>{{ $message }}
                                            </p>
                                        @enderror
                                    </div>
                                </div>

                                <div class="space-y-6">
                                    <div>
                                        <label for="email" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                            <i class="fas fa-envelope ml-2 text-primary"></i>البريد الإلكتروني
                                        </label>
                                        <input type="email" id="email" name="email" value="{{ old('email', auth()->user()->email ?? '') }}" required
                                               class="w-full px-4 py-4 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary dark:bg-gray-700 dark:text-white transition-all duration-200 @error('email') border-red-400 @enderror">
                                        @error('email')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400 flex items-center">
                                                <i class="fas fa-exclamation-circle ml-1"></i>{{ $message }}
                                            </p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="address" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                            <i class="fas fa-map-marker-alt ml-2 text-primary"></i>العنوان
                                        </label>
                                        <textarea id="address" name="address" rows="4"
                                                  class="w-full px-4 py-4 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary dark:bg-gray-700 dark:text-white transition-all duration-200 resize-none @error('address') border-red-400 @enderror"
                                                  placeholder="أدخل عنوانك الكامل">{{ old('address', auth()->user()->address ?? '') }}</textarea>
                                        @error('address')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400 flex items-center">
                                                <i class="fas fa-exclamation-circle ml-1"></i>{{ $message }}
                                            </p>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="flex justify-end space-x-4 space-x-reverse pt-6 border-t border-gray-200 dark:border-gray-700">
                                <button type="button" onclick="window.location.reload()"
                                        class="px-8 py-3 border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 font-medium">
                                    <i class="fas fa-times ml-2"></i>إلغاء
                                </button>
                                <button type="submit"
                                        class="px-8 py-3 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-white rounded-xl transition-all duration-200 font-medium shadow-lg hover:shadow-xl">
                                    <i class="fas fa-save ml-2"></i>حفظ التغييرات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Password Change Tab -->
                <div id="password-content" class="tab-content hidden">
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
                        <div class="mb-8">
                            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">تغيير كلمة المرور</h2>
                            <p class="text-gray-600 dark:text-gray-400">قم بتحديث كلمة المرور لحماية حسابك</p>
                        </div>

                        <!-- رسائل خاصة بكلمة المرور -->
                        @if(session('password_success'))
                            <div class="mb-6 bg-green-50 dark:bg-green-900/20 border-l-4 border-green-400 p-4 rounded-lg">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-check-circle text-green-400 text-xl"></i>
                                    </div>
                                    <div class="mr-3">
                                        <p class="text-green-700 dark:text-green-300 font-medium">{{ session('password_success') }}</p>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if(session('password_error'))
                            <div class="mb-6 bg-red-50 dark:bg-red-900/20 border-l-4 border-red-400 p-4 rounded-lg">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-exclamation-triangle text-red-400 text-xl"></i>
                                    </div>
                                    <div class="mr-3">
                                        <p class="text-red-700 dark:text-red-300 font-medium">{{ session('password_error') }}</p>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <form action="{{ route('customer.profile.update') }}" method="POST" class="space-y-8">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="update_password" value="1">

                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                <div class="space-y-6">
                                    <div>
                                        <label for="current_password" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                            <i class="fas fa-lock ml-2 text-primary"></i>كلمة المرور الحالية
                                        </label>
                                        <input type="password" id="current_password" name="current_password" required
                                               class="w-full px-4 py-4 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary dark:bg-gray-700 dark:text-white transition-all duration-200 @error('current_password') border-red-400 @enderror">
                                        @error('current_password')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400 flex items-center">
                                                <i class="fas fa-exclamation-circle ml-1"></i>{{ $message }}
                                            </p>
                                        @enderror
                                    </div>
                                </div>

                                <div class="space-y-6">
                                    <div>
                                        <label for="password" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                            <i class="fas fa-key ml-2 text-primary"></i>كلمة المرور الجديدة
                                        </label>
                                        <input type="password" id="password" name="password" required minlength="8"
                                               class="w-full px-4 py-4 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary dark:bg-gray-700 dark:text-white transition-all duration-200 @error('password') border-red-400 @enderror">
                                        @error('password')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400 flex items-center">
                                                <i class="fas fa-exclamation-circle ml-1"></i>{{ $message }}
                                            </p>
                                        @enderror
                                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400 flex items-center">
                                            <i class="fas fa-info-circle ml-1"></i>يجب أن تكون كلمة المرور 8 أحرف على الأقل
                                        </p>
                                    </div>

                                    <div>
                                        <label for="password_confirmation" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                            <i class="fas fa-check-double ml-2 text-primary"></i>تأكيد كلمة المرور
                                        </label>
                                        <input type="password" id="password_confirmation" name="password_confirmation" required
                                               class="w-full px-4 py-4 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary dark:bg-gray-700 dark:text-white transition-all duration-200 @error('password_confirmation') border-red-400 @enderror">
                                        @error('password_confirmation')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400 flex items-center">
                                                <i class="fas fa-exclamation-circle ml-1"></i>{{ $message }}
                                            </p>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
                                <button type="submit"
                                        class="px-8 py-3 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-white rounded-xl transition-all duration-200 font-medium shadow-lg hover:shadow-xl">
                                    <i class="fas fa-key ml-2"></i>تحديث كلمة المرور
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Statistics Tab -->
                <div id="stats-content" class="tab-content hidden">
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
                        <div class="mb-8">
                            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">إحصائيات الحساب</h2>
                            <p class="text-gray-600 dark:text-gray-400">نظرة عامة على نشاطك في المطعم</p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <!-- تاريخ الانضمام -->
                            <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-calendar-plus text-white text-xl"></i>
                                    </div>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">تاريخ الانضمام</h3>
                                <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                                    {{ auth()->user()->created_at ? auth()->user()->created_at->format('Y/m/d') : 'غير محدد' }}
                                </p>
                            </div>

                            <!-- إجمالي الطلبات -->
                            <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl p-6 border border-green-200 dark:border-green-700">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-shopping-bag text-white text-xl"></i>
                                    </div>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">إجمالي الطلبات</h3>
                                <p class="text-2xl font-bold text-green-600 dark:text-green-400">
                                    {{ \App\Models\Order::where('user_id', auth()->user()->user_id)->where('order_type', '!=', 'preorder')->count() }} طلب
                                </p>
                            </div>

                            <!-- إجمالي الإنفاق -->
                            <div class="bg-gradient-to-br from-primary/10 to-primary/20 dark:from-primary/20 dark:to-primary/30 rounded-xl p-6 border border-primary/30">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center">
                                        <i class="fas fa-coins text-white text-xl"></i>
                                    </div>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">إجمالي الإنفاق</h3>
                                <p class="text-2xl font-bold text-primary">
                                    {{ number_format(\App\Models\Order::where('user_id', auth()->user()->user_id)->where('status', 'completed')->where('order_type', '!=', 'preorder')->sum('total_amount'), 2) }} د.ل
                                </p>
                            </div>
                        </div>

                        <!-- Additional Stats -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                            <!-- الحجوزات النشطة -->
                            <div class="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl p-6 border border-purple-200 dark:border-purple-700">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-calendar-check text-white text-xl"></i>
                                    </div>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">الحجوزات النشطة</h3>
                                <p class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                                    {{ \App\Models\Reservation::where('user_id', auth()->user()->user_id)->where('status', 'confirmed')->where('reservation_time', '>=', now())->count() }} حجز
                                </p>
                            </div>


                        </div>

                        <!-- Quick Actions -->
                        <div class="mt-8 p-6 bg-gray-50 dark:bg-gray-700/50 rounded-xl">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">إجراءات سريعة</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <a href="{{ route('customer.orders') }}" class="flex items-center p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 border border-gray-200 dark:border-gray-600">
                                    <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center ml-3">
                                        <i class="fas fa-shopping-bag text-primary"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-800 dark:text-white">طلباتي</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">عرض جميع الطلبات</p>
                                    </div>
                                </a>
                                <a href="{{ route('customer.reservations') }}" class="flex items-center p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 border border-gray-200 dark:border-gray-600">
                                    <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center ml-3">
                                        <i class="fas fa-calendar-alt text-primary"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-800 dark:text-white">حجوزاتي</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">إدارة الحجوزات</p>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Tab functionality
function showTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });

    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('bg-primary', 'text-white', 'border-b-2', 'border-primary');
        button.classList.add('text-gray-600', 'dark:text-gray-400', 'hover:text-primary', 'hover:bg-gray-50', 'dark:hover:bg-gray-700');
    });

    // Show selected tab content
    document.getElementById(tabName + '-content').classList.remove('hidden');

    // Add active class to selected tab button
    const activeButton = document.getElementById(tabName + '-tab');
    activeButton.classList.remove('text-gray-600', 'dark:text-gray-400', 'hover:text-primary', 'hover:bg-gray-50', 'dark:hover:bg-gray-700');
    activeButton.classList.add('bg-primary', 'text-white', 'border-b-2', 'border-primary');
}

// Auto-hide success messages
document.addEventListener('DOMContentLoaded', function() {
    const successMessages = document.querySelectorAll('.bg-green-50, .bg-green-900\\/20');
    successMessages.forEach(function(message) {
        setTimeout(function() {
            message.style.transition = 'opacity 0.5s ease-out';
            message.style.opacity = '0';
            setTimeout(function() {
                message.remove();
            }, 500);
        }, 5000);
    });

    // Password validation
    const passwordField = document.getElementById('password');
    const confirmPasswordField = document.getElementById('password_confirmation');

    if (passwordField && confirmPasswordField) {
        function checkPasswordMatch() {
            if (passwordField.value && confirmPasswordField.value) {
                if (passwordField.value !== confirmPasswordField.value) {
                    confirmPasswordField.setCustomValidity('كلمة المرور غير متطابقة');
                    confirmPasswordField.classList.add('border-red-400');
                } else {
                    confirmPasswordField.setCustomValidity('');
                    confirmPasswordField.classList.remove('border-red-400');
                }
            }
        }

        passwordField.addEventListener('input', checkPasswordMatch);
        confirmPasswordField.addEventListener('input', checkPasswordMatch);
    }

    // Initialize first tab as active
    showTab('personal');
});
</script>
@endpush


