// Charts for orders report (Chart.js)
(function () {
    function toNumber(v) {
        var n = parseFloat(v);
        return isNaN(n) ? 0 : n;
    }

    function drawDailyOrdersChart(ctx, data) {
        var labels = Array.isArray(data)
            ? data.map(function (i) { return i.date; })
            : [];
        var values = Array.isArray(data)
            ? data.map(function (i) { return toNumber(i.count || i.total || 0); })
            : [];
        if (labels.length === 0) {
            // fallback demo data
            labels = ["2025-08-15", "2025-08-16", "2025-08-17", "2025-08-18", "2025-08-19"];
            values = [5, 8, 6, 10, 7];
        }

        return new Chart(ctx, {
            type: "line",
            data: {
                labels: labels,
                datasets: [{
                    label: "عدد الطلبات",
                    data: values,
                    borderColor: "#3B82F6",
                    backgroundColor: "rgba(59,130,246,0.2)",
                    tension: 0.35,
                    fill: true,
                }],
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                },
                scales: {
                    y: { beginAtZero: true, ticks: { stepSize: 1 } },
                },
            },
        });
    }

    function drawStatusChart(ctx, statusData) {
        var mapLabel = {
            pending: "قيد الانتظار",
            preparing: "قيد التحضير",
            completed: "مكتمل",
            cancelled: "ملغي",
        };
        var mapColor = {
            pending: "#F59E0B",
            preparing: "#3B82F6",
            completed: "#10B981",
            cancelled: "#EF4444",
        };

        var labels = Array.isArray(statusData)
            ? statusData.map(function (s) { return mapLabel[s.status] || String(s.status); })
            : [];
        var values = Array.isArray(statusData)
            ? statusData.map(function (s) { return toNumber(s.count || s.total || 0); })
            : [];
        var colors = Array.isArray(statusData)
            ? statusData.map(function (s) { return mapColor[s.status] || "#6B7280"; })
            : [];

        if (labels.length === 0) {
            labels = ["قيد الانتظار", "قيد التحضير", "مكتمل", "ملغي"];
            values = [4, 6, 15, 2];
            colors = ["#F59E0B", "#3B82F6", "#10B981", "#EF4444"];
        }

        return new Chart(ctx, {
            type: "doughnut",
            data: {
                labels: labels,
                datasets: [{
                    data: values,
                    backgroundColor: colors,
                    borderWidth: 2,
                    borderColor: "#ffffff",
                }],
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: "bottom" },
                },
            },
        });
    }

    window.initOrdersChartsChartJS = function () {
        try {
            var daily = (window.ordersData && Array.isArray(window.ordersData.daily)) ? window.ordersData.daily : [];
            var status = (window.ordersData && Array.isArray(window.ordersData.status)) ? window.ordersData.status : [];

            var dailyCanvas = document.getElementById("dailyOrdersCanvas");
            var statusCanvas = document.getElementById("statusOrdersCanvas");

            if (dailyCanvas && window.Chart) {
                drawDailyOrdersChart(dailyCanvas.getContext("2d"), daily);
            }
            if (statusCanvas && window.Chart) {
                drawStatusChart(statusCanvas.getContext("2d"), status);
            }
        } catch (e) {
            console.error("Orders charts init error", e);
        }
    };
})();

