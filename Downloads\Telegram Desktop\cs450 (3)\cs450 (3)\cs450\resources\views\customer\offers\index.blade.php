@extends('customer.layouts.simple')

@section('title', 'العروض المتاحة')

@section('content')

<style>
    /* تأثيرات CSS مخصصة للعروض */
    @keyframes gradient-x {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    @keyframes glow {
        0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.5); }
        50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.8); }
    }

    .animate-gradient-x {
        background-size: 200% 200%;
        animation: gradient-x 3s ease infinite;
    }

    .animate-float {
        animation: float 3s ease-in-out infinite;
    }

    .animate-glow {
        animation: glow 2s ease-in-out infinite;
    }

    .offer-card {
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .offer-card:hover {
        transform: translateY(-12px) scale(1.02);
    }

    .offer-button {
        position: relative;
        overflow: hidden;
    }

    .offer-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .offer-button:hover::before {
        left: 100%;
    }
</style>

<!-- المحتوى الرئيسي -->
<main class="min-h-screen pt-20">
    <div class="container mx-auto px-4 py-8">
    <!-- Header مع تأثير بصري محسن -->
    <div class="mb-12 text-center">

        <div class="relative">
            <h1 class="text-4xl md:text-5xl font-bold text-darkText dark:text-white mb-4">
                العروض المتاحة
                <span class="text-primary">🎉</span>
            </h1>
            <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto leading-relaxed">
                اكتشف أحدث العروض والخصومات المتاحة في مطعمنا واستمتع بأفضل الأسعار
            </p>

            <!-- خط زخرفي -->
            <div class="flex items-center justify-center mt-6">
                <div class="h-1 w-20 bg-gradient-to-r from-primary to-accent rounded-full"></div>
                <div class="mx-4 text-primary text-2xl">✨</div>
                <div class="h-1 w-20 bg-gradient-to-l from-primary to-accent rounded-full"></div>
            </div>
        </div>
    </div>

    <!-- فلاتر العروض المحسنة -->
    <div class="bg-gradient-to-r from-white to-gray-50 dark:from-gray-800 dark:to-gray-700 rounded-xl shadow-lg p-6 mb-10 border border-gray-100 dark:border-gray-600">
        <div class="flex items-center mb-4">
            <i class="fas fa-filter text-primary text-lg ml-2"></i>
            <h3 class="text-lg font-semibold text-darkText dark:text-white">فلترة العروض</h3>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- البحث -->
            <div class="relative">
                <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                <input type="text" id="searchOffers" placeholder="البحث في العروض..."
                       class="w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 hover:border-primary/50">
            </div>

            <!-- فلتر النوع -->
            <div class="relative">
                <i class="fas fa-tag absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                <select id="filterType" class="w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 hover:border-primary/50 appearance-none">
                    <option value="">جميع الأنواع</option>
                    <option value="حجز">🍽️ عروض الحجز</option>
                    <option value="طعام">🍕 عروض الطعام</option>
                </select>
                <i class="fas fa-chevron-down absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"></i>
            </div>

            <!-- فلتر الحالة -->
            <div class="relative">
                <i class="fas fa-clock absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                <select id="filterStatus" class="w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 hover:border-primary/50 appearance-none">
                    <option value="">جميع الحالات</option>
                    <option value="active">✅ متاح</option>
                    <option value="expired">❌ منتهي</option>
                </select>
                <i class="fas fa-chevron-down absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"></i>
            </div>
        </div>
    </div>

    <!-- قائمة العروض المحسنة -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="offersGrid">
        @foreach($offers as $offer)
            <div class="offer-card group bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100 dark:border-gray-700"
                 data-type="{{ is_array($offer) ? ($offer['type'] ?? 'طعام') : ($offer->type ?? 'طعام') }}"
                 data-status="{{ is_array($offer) ? ($offer['is_active'] ? 'active' : 'expired') : ($offer->is_active ? 'active' : 'expired') }}"
                 data-title="{{ is_array($offer) ? strtolower($offer['title']) : strtolower($offer->title) }}"
                 data-description="{{ is_array($offer) ? strtolower($offer['description']) : strtolower($offer->description) }}">

                <div class="relative overflow-hidden">
                    <img src="{{ is_array($offer) ? ($offer['image'] ?? asset('images/default-offer.svg')) : ($offer->image_url ?? asset('images/default-offer.svg')) }}"
                         alt="{{ is_array($offer) ? $offer['title'] : $offer->title }}"
                         class="w-full h-52 object-cover transition-transform duration-300 group-hover:scale-110">

                    <!-- تأثير التدرج -->
                    <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>

                    <!-- شارة نوع العرض -->
                    <div class="absolute top-4 left-4">
                        <span class="bg-primary/90 backdrop-blur-sm text-white px-3 py-1.5 rounded-full text-sm font-semibold shadow-lg">
                            @php
                                $offerType = is_array($offer) ? $offer['type'] : $offer->type;
                            @endphp
                            @if($offerType === 'حجز')
                                🍽️ {{ $offerType }}
                            @else
                                🍕 {{ $offerType ?? 'طعام' }}
                            @endif
                        </span>
                    </div>

                    <!-- شارة الخصم المحسنة -->
                    @php
                        $offerType = is_array($offer) ? ($offer['offer_type'] ?? null) : ($offer->offer_type ?? null);
                        $discount = is_array($offer) ? $offer['discount'] : ($offer->discount_percentage ?? $offer->discount_amount);
                    @endphp
                    @if($offerType === 'buy_get')
                        <div class="absolute top-4 right-4">
                            <span class="bg-gradient-to-r from-orange-500 to-red-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg animate-bounce">
                                🎁 عرض خاص
                            </span>
                        </div>
                    @elseif($discount)
                        <div class="absolute top-4 right-4">
                            <span class="bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg animate-pulse">
                                🔥 خصم {{ $discount }}{{ is_array($offer) && isset($offer['discount']) && !str_contains($offer['discount'], '%') ? ' د.ل' : (is_numeric($discount) ? '%' : '') }}
                            </span>
                        </div>
                    @endif

                    <!-- شارة الحالة المحسنة -->
                    <div class="absolute bottom-4 right-4">
                        @php
                            $isActive = is_array($offer) ? $offer['is_active'] : $offer->is_active;
                        @endphp
                        <span class="px-3 py-1.5 rounded-full text-sm font-semibold backdrop-blur-sm shadow-lg {{ $isActive ? 'bg-green-500/90 text-white' : 'bg-gray-500/90 text-white' }}">
                            {{ $isActive ? '✅ متاح' : '❌ منتهي' }}
                        </span>
                    </div>
                </div>

                <div class="p-6">
                    <h3 class="text-xl font-bold text-darkText dark:text-white mb-3 group-hover:text-primary transition-colors">
                        {{ is_array($offer) ? $offer['title'] : $offer->title }}
                    </h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3 leading-relaxed">
                        {{ is_array($offer) ? $offer['description'] : $offer->description }}
                    </p>

                    <!-- معلومات التاريخ المحسنة -->
                    <div class="flex justify-between items-center text-sm mb-6">
                        @php
                            $startDate = is_array($offer) ? $offer['start_date'] : $offer->start_date;
                            $endDate = is_array($offer) ? $offer['end_date'] : $offer->end_date;
                        @endphp
                        @if($startDate)
                        <div class="flex items-center text-gray-500 dark:text-gray-400">
                            <i class="fas fa-calendar-alt ml-1 text-primary"></i>
                            <span>من: {{ $startDate }}</span>
                        </div>
                        @endif
                        @if($endDate)
                        <div class="flex items-center text-gray-500 dark:text-gray-400">
                            <i class="fas fa-calendar-times ml-1 text-red-500"></i>
                            <span>إلى: {{ $endDate }}</span>
                        </div>
                        @else
                        <div class="flex items-center text-green-600 font-medium">
                            <i class="fas fa-infinity ml-1"></i>
                            <span>عرض مفتوح</span>
                        </div>
                        @endif
                    </div>

                    <!-- الأزرار المحسنة -->
                    <div class="flex gap-3">
                        <a href="{{ route('customer.offers.show', is_array($offer) ? $offer['slug'] : $offer->slug) }}"
                           class="flex-1 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-white text-center py-3 px-4 rounded-lg transition-all duration-200 font-semibold shadow-md hover:shadow-lg transform hover:scale-105">
                            <i class="fas fa-eye ml-1"></i>
                            عرض التفاصيل
                        </a>

                        @php
                            $isActive = is_array($offer) ? $offer['is_active'] : $offer->is_active;
                            $offerType = is_array($offer) ? $offer['type'] : $offer->type;
                            $offerSlug = is_array($offer) ? $offer['slug'] : $offer->slug;
                        @endphp
                        @if($isActive)
                            @auth
                                @if($offerType === 'حجز')
                                    <!-- زر الحجز المبتكر -->
                                    <div class="flex gap-2">
                                        <button onclick="applyReservationOffer('{{ $offerSlug }}', this)"
                                                class="offer-button flex-1 relative overflow-hidden bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white text-center py-4 px-6 rounded-xl transition-all duration-300 font-bold shadow-lg hover:shadow-xl transform hover:scale-105 group animate-glow">
                                            <div class="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                                            <div class="relative flex items-center justify-center">
                                                <i class="fas fa-calendar-star ml-2 text-lg"></i>
                                                <span>احجز مع العرض الخاص</span>
                                            </div>
                                            <div class="absolute -top-1 -right-1 bg-yellow-400 text-purple-900 text-xs font-bold px-2 py-1 rounded-full animate-pulse">
                                                حصري!
                                            </div>
                                        </button>
                                    </div>
                                @else
                                    <!-- زر الطلب المبتكر -->
                                    <div class="flex gap-2">
                                        <button onclick="applyOfferDirectly('{{ $offerSlug }}', this)"
                                                class="offer-button flex-1 relative overflow-hidden bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white text-center py-4 px-6 rounded-xl transition-all duration-300 font-bold shadow-lg hover:shadow-xl transform hover:scale-105 group animate-glow">
                                            <div class="absolute inset-0 bg-gradient-to-r from-emerald-300 to-teal-300 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                                            <div class="relative flex items-center justify-center">
                                                <i class="fas fa-shopping-bag ml-2 text-lg"></i>
                                                <span>اطلب الآن واستفد</span>
                                            </div>
                                            <div class="absolute -top-1 -right-1 bg-orange-400 text-emerald-900 text-xs font-bold px-2 py-1 rounded-full animate-bounce">
                                                وفر!
                                            </div>
                                        </button>
                                        <button onclick="showOfferPreview('{{ $offerSlug }}')"
                                                class="bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-4 py-4 rounded-xl transition-all duration-200 shadow-md hover:shadow-lg"
                                                title="معاينة العرض">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                @endif
                            @else
                                <!-- زر تسجيل الدخول المبتكر -->
                                <a href="{{ route('login') }}"
                                   class="flex-1 relative overflow-hidden bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white text-center py-4 px-6 rounded-xl transition-all duration-300 font-bold shadow-lg hover:shadow-xl transform hover:scale-105 group block">
                                    <div class="absolute inset-0 bg-gradient-to-r from-blue-400 to-indigo-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                                    <div class="relative flex items-center justify-center">
                                        <i class="fas fa-user-plus ml-2 text-lg"></i>
                                        <span>سجل دخولك للاستفادة</span>
                                    </div>
                                    <div class="absolute -top-1 -right-1 bg-green-400 text-blue-900 text-xs font-bold px-2 py-1 rounded-full animate-pulse">
                                        مجاني!
                                    </div>
                                </a>
                            @endauth
                        @else
                            <!-- العرض غير نشط -->
                            <div class="flex-1 bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 text-center py-4 px-6 rounded-xl font-medium">
                                <i class="fas fa-clock ml-2"></i>
                                العرض منتهي الصلاحية
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <!-- رسالة عدم وجود نتائج المحسنة -->
    <div id="noResults" class="text-center py-16 hidden">
        <div class="max-w-md mx-auto">
            <div class="text-8xl mb-6 animate-bounce">🔍</div>
            <h3 class="text-2xl font-bold text-gray-600 dark:text-gray-300 mb-4">لا توجد عروض مطابقة</h3>
            <p class="text-gray-500 dark:text-gray-400 mb-6 leading-relaxed">
                جرب تغيير معايير البحث أو الفلترة للعثور على العروض المناسبة لك
            </p>
            <button onclick="clearFilters()" class="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition-all duration-200 font-semibold">
                <i class="fas fa-refresh ml-1"></i>
                إعادة تعيين الفلاتر
            </button>
        </div>
    </div>

    <!-- إحصائيات العروض المحسنة -->
    <div class="mt-16 bg-gradient-to-br from-primary via-primary/90 to-primary/80 rounded-2xl p-8 text-white shadow-2xl relative overflow-hidden">
        <!-- تأثير الخلفية -->
        <div class="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent"></div>
        <div class="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-full -translate-y-16 translate-x-16"></div>
        <div class="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"></div>

        <div class="relative">
            <div class="text-center mb-8">
                <h2 class="text-2xl font-bold mb-2">📊 إحصائيات العروض</h2>
                <p class="text-white/80">نظرة سريعة على العروض المتاحة</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center group">
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 transition-all duration-300 hover:bg-white/20 hover:scale-105">
                        <div class="text-4xl font-bold mb-2 group-hover:scale-110 transition-transform">
                            {{ count($offers) }}
                        </div>
                        <div class="text-white/90 font-medium flex items-center justify-center">
                            <i class="fas fa-tags ml-2"></i>
                            إجمالي العروض
                        </div>
                    </div>
                </div>

                <div class="text-center group">
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 transition-all duration-300 hover:bg-white/20 hover:scale-105">
                        <div class="text-4xl font-bold mb-2 group-hover:scale-110 transition-transform text-green-300">
                            {{ collect($offers)->filter(function($offer) { return is_array($offer) ? $offer['is_active'] : $offer->is_active; })->count() }}
                        </div>
                        <div class="text-white/90 font-medium flex items-center justify-center">
                            <i class="fas fa-check-circle ml-2"></i>
                            عروض متاحة
                        </div>
                    </div>
                </div>

                <div class="text-center group">
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 transition-all duration-300 hover:bg-white/20 hover:scale-105">
                        <div class="text-4xl font-bold mb-2 group-hover:scale-110 transition-transform text-yellow-300">
                            {{ collect($offers)->filter(function($offer) {
                                $discount = is_array($offer) ? $offer['discount'] : ($offer->discount_percentage ?? $offer->discount_amount);
                                return !empty($discount);
                            })->count() }}
                        </div>
                        <div class="text-white/90 font-medium flex items-center justify-center">
                            <i class="fas fa-percentage ml-2"></i>
                            عروض بخصومات
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchOffers');
    const typeFilter = document.getElementById('filterType');
    const statusFilter = document.getElementById('filterStatus');
    const offersGrid = document.getElementById('offersGrid');
    const noResults = document.getElementById('noResults');

    function filterOffers() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedType = typeFilter.value;
        const selectedStatus = statusFilter.value;

        const offerCards = document.querySelectorAll('.offer-card');
        let visibleCount = 0;

        offerCards.forEach(card => {
            const title = card.dataset.title;
            const description = card.dataset.description;
            const type = card.dataset.type;
            const status = card.dataset.status;

            const matchesSearch = !searchTerm || title.includes(searchTerm) || description.includes(searchTerm);
            const matchesType = !selectedType || type === selectedType;
            const matchesStatus = !selectedStatus || status === selectedStatus;

            if (matchesSearch && matchesType && matchesStatus) {
                card.style.display = 'block';
                visibleCount++;
            } else {
                card.style.display = 'none';
            }
        });

        // إظهار/إخفاء رسالة عدم وجود نتائج
        if (visibleCount === 0) {
            offersGrid.style.display = 'none';
            noResults.classList.remove('hidden');
        } else {
            offersGrid.style.display = 'grid';
            noResults.classList.add('hidden');
        }
    }

    // وظيفة إعادة تعيين الفلاتر
    window.clearFilters = function() {
        searchInput.value = '';
        typeFilter.value = '';
        statusFilter.value = '';
        filterOffers();
    };

    // ربط الأحداث
    searchInput.addEventListener('input', filterOffers);
    typeFilter.addEventListener('change', filterOffers);
    statusFilter.addEventListener('change', filterOffers);

    // تأثيرات بصرية للبطاقات
    const offerCards = document.querySelectorAll('.offer-card');
    offerCards.forEach((card, index) => {
        // تأثير الظهور التدريجي
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // دالة تطبيق العرض مباشرة والانتقال للسلة
    window.applyOfferDirectly = function(offerSlug, button) {
        // منع التطبيق المتكرر
        if (button.disabled) return;

        // إظهار حالة التحميل
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i> جاري التطبيق...';
        button.disabled = true;

        // تطبيق العرض مباشرة
        fetch('{{ route("customer.cart.add-offer") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                offer_slug: offerSlug
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إظهار رسالة نجاح سريعة
                showQuickNotification('تم تطبيق العرض بنجاح! يمكنك الآن إضافة عناصر للسلة للاستفادة من الخصم', 'success');

                // تغيير الزر ليشير إلى النجاح
                button.innerHTML = '<i class="fas fa-check ml-1"></i> تم التطبيق';
                button.classList.remove('from-accent', 'to-accent/80', 'hover:from-accent/90', 'hover:to-accent/70');
                button.classList.add('from-green-500', 'to-green-600', 'cursor-default');

                // الانتقال للسلة بعد ثانيتين
                setTimeout(() => {
                    window.location.href = '{{ route("customer.cart") }}';
                }, 2000);
            } else {
                // إظهار رسالة خطأ محسنة
                if (data.existing_offer) {
                    showQuickNotification(`🚫 عذراً! لديك عرض مطبق بالفعل: "${data.existing_offer}". يرجى إلغاؤه أولاً من السلة.`, 'error');
                } else {
                    showQuickNotification(data.message, 'error');
                }
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showQuickNotification('حدث خطأ أثناء تطبيق العرض', 'error');
            button.innerHTML = originalText;
            button.disabled = false;
        });
    };

    // دالة تطبيق عروض الحجز
    window.applyReservationOffer = function(offerSlug, button) {
        // منع التطبيق المتكرر
        if (button.disabled) return;

        // إظهار حالة التحميل
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i> جاري التطبيق...';
        button.disabled = true;

        // تطبيق عرض الحجز
        fetch('{{ route("customer.cart.add-offer") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                offer_slug: offerSlug
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إظهار رسالة نجاح سريعة
                showQuickNotification('تم تطبيق عرض الحجز! سيتم تحويلك لصفحة الحجز', 'success');

                // تغيير الزر ليشير إلى النجاح
                button.innerHTML = '<i class="fas fa-check ml-1"></i> تم التطبيق';
                button.classList.remove('from-accent', 'to-accent/80', 'hover:from-accent/90', 'hover:to-accent/70');
                button.classList.add('from-green-500', 'to-green-600', 'cursor-default');

                // الانتقال لصفحة الحجز مع معرف العرض
                setTimeout(() => {
                    window.location.href = '{{ route("customer.reservations.create") }}?offer=' + offerSlug;
                }, 2000);
            } else {
                // إظهار رسالة خطأ محسنة
                if (data.existing_offer) {
                    showQuickNotification(`🚫 عذراً! لديك عرض مطبق بالفعل: "${data.existing_offer}". يرجى إلغاؤه أولاً من السلة.`, 'error');
                } else {
                    showQuickNotification(data.message, 'error');
                }
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showQuickNotification('حدث خطأ أثناء تطبيق العرض', 'error');
            button.innerHTML = originalText;
            button.disabled = false;
        });
    };

    // دالة إظهار إشعار سريع محسن
    function showQuickNotification(message, type = 'success') {
        const bgColor = type === 'success' ? 'bg-gradient-to-r from-green-500 to-emerald-600' : 'bg-gradient-to-r from-red-500 to-pink-600';
        const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';

        const notification = document.createElement('div');
        notification.className = `fixed top-20 right-4 ${bgColor} text-white px-6 py-4 rounded-xl shadow-2xl z-50 transform translate-x-full transition-all duration-500 backdrop-blur-sm`;
        notification.innerHTML = `
            <div class="flex items-center">
                <div class="bg-white bg-opacity-20 rounded-full p-2 ml-3">
                    <i class="${icon} text-lg"></i>
                </div>
                <div>
                    <div class="font-bold text-sm">تم بنجاح!</div>
                    <div class="text-xs opacity-90">${message}</div>
                </div>
            </div>
        `;

        document.body.appendChild(notification);

        // إظهار الإشعار مع تأثير
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
            notification.classList.add('animate-pulse');
        }, 100);

        // إزالة التأثير
        setTimeout(() => {
            notification.classList.remove('animate-pulse');
        }, 1000);

        // إخفاء الإشعار بعد 4 ثوان
        setTimeout(() => {
            notification.classList.add('translate-x-full', 'scale-95', 'opacity-0');
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 500);
        }, 4000);
    }

    // دالة معاينة العرض
    window.showOfferPreview = function(offerSlug) {
        // إنشاء نافذة معاينة مبتكرة
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center p-4';
        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full transform scale-95 transition-all duration-300" id="preview-content">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-bold text-gray-800 dark:text-white">معاينة العرض</h3>
                        <button onclick="closeOfferPreview()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <div class="text-center">
                        <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl p-4 mb-4">
                            <i class="fas fa-gift text-3xl mb-2"></i>
                            <div class="font-bold">عرض خاص!</div>
                            <div class="text-sm opacity-90">اطلب الآن واستفد من الخصم</div>
                        </div>
                        <div class="space-y-3">
                            <button onclick="applyOfferFromPreview('${offerSlug}', this)"
                                    class="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white py-3 px-6 rounded-xl font-bold transition-all duration-200 transform hover:scale-105">
                                <i class="fas fa-shopping-cart ml-2"></i>
                                تطبيق العرض الآن
                            </button>
                            <button onclick="closeOfferPreview()"
                                    class="w-full bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-3 px-6 rounded-xl font-medium transition-all duration-200 hover:bg-gray-300 dark:hover:bg-gray-600">
                                إلغاء
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // تأثير الظهور
        setTimeout(() => {
            modal.querySelector('#preview-content').classList.remove('scale-95');
            modal.querySelector('#preview-content').classList.add('scale-100');
        }, 50);

        // إغلاق عند النقر خارج النافذة
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeOfferPreview();
            }
        });

        // إغلاق عند الضغط على مفتاح Escape
        const escapeHandler = function(e) {
            if (e.key === 'Escape') {
                closeOfferPreview();
                document.removeEventListener('keydown', escapeHandler);
            }
        };
        document.addEventListener('keydown', escapeHandler);
    };

    // دالة تطبيق العرض من نافذة المعاينة
    window.applyOfferFromPreview = function(offerSlug, button) {
        // منع التطبيق المتكرر
        if (button.disabled) return;

        // إظهار حالة التحميل
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري التطبيق...';
        button.disabled = true;

        // تطبيق العرض
        fetch('{{ route("customer.cart.add-offer") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                offer_slug: offerSlug
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إغلاق النافذة
                closeOfferPreview();

                // إظهار رسالة نجاح
                showQuickNotification('تم تطبيق العرض بنجاح! يمكنك الآن إضافة عناصر للسلة للاستفادة من الخصم', 'success');

                // الانتقال للسلة بعد ثانيتين
                setTimeout(() => {
                    window.location.href = '{{ route("customer.cart") }}';
                }, 2000);
            } else {
                showQuickNotification(data.message, 'error');
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showQuickNotification('حدث خطأ أثناء تطبيق العرض', 'error');
            button.innerHTML = originalText;
            button.disabled = false;
        });
    };

    // دالة إغلاق معاينة العرض
    window.closeOfferPreview = function() {
        const modal = document.querySelector('.fixed.inset-0.bg-black.bg-opacity-50.backdrop-blur-sm');
        if (modal) {
            const content = modal.querySelector('#preview-content');
            if (content) {
                content.classList.remove('scale-100');
                content.classList.add('scale-95', 'opacity-0');
            }
            setTimeout(() => {
                if (modal && modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
            }, 300);
        }
    };
});
</script>
@endsection
