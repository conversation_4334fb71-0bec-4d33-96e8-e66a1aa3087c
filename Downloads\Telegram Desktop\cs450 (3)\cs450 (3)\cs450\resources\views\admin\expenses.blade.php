@extends('layouts.admin')

@section('title', 'إدارة المصروفات - لوحة تحكم Eat Hub')

@section('page-title', 'إدارة المصروفات')

@section('content')
<div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">إدارة المصروفات</h2>
    <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 sm:space-x-reverse">
        <div class="relative">
            <input type="text" placeholder="بحث..." class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary text-base">
            <button class="absolute left-3 top-2.5 text-gray-500 dark:text-gray-400">
                <i class="fas fa-search"></i>
            </button>
        </div>
        <a href="{{ route('admin.expenses.create') }}" class="bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-plus ml-2"></i>
            <span>إضافة مصروف</span>
        </a>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-6">
    <!-- إحصائية المصروفات 1 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">إجمالي المصروفات (الشهر الحالي)</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">42,120 ر.س</h3>
            </div>
            <div class="rounded-full bg-red-100 dark:bg-red-900/30 p-3">
                <i class="fas fa-money-bill-wave text-red-500 text-xl"></i>
            </div>
        </div>
    </div>
    
    <!-- إحصائية المصروفات حسب الفئة -->
    <div class="lg:col-span-3 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start mb-4">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white">المصروفات حسب الفئة (الشهر الحالي)</h3>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
            <div class="flex flex-col">
                <span class="text-gray-600 dark:text-gray-300 text-sm mb-1">المكونات</span>
                <span class="text-xl font-bold text-gray-800 dark:text-white">18,450 ر.س</span>
                <div class="mt-2 w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                    <div class="bg-blue-500 h-full rounded-full" style="width: 44%;"></div>
                </div>
                <span class="text-gray-500 dark:text-gray-400 text-xs mt-1">44%</span>
            </div>
            <div class="flex flex-col">
                <span class="text-gray-600 dark:text-gray-300 text-sm mb-1">المرافق</span>
                <span class="text-xl font-bold text-gray-800 dark:text-white">8,750 ر.س</span>
                <div class="mt-2 w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                    <div class="bg-green-500 h-full rounded-full" style="width: 21%;"></div>
                </div>
                <span class="text-gray-500 dark:text-gray-400 text-xs mt-1">21%</span>
            </div>
            <div class="flex flex-col">
                <span class="text-gray-600 dark:text-gray-300 text-sm mb-1">الرواتب</span>
                <span class="text-xl font-bold text-gray-800 dark:text-white">10,320 ر.س</span>
                <div class="mt-2 w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                    <div class="bg-yellow-500 h-full rounded-full" style="width: 24%;"></div>
                </div>
                <span class="text-gray-500 dark:text-gray-400 text-xs mt-1">24%</span>
            </div>
            <div class="flex flex-col">
                <span class="text-gray-600 dark:text-gray-300 text-sm mb-1">الصيانة</span>
                <span class="text-xl font-bold text-gray-800 dark:text-white">2,850 ر.س</span>
                <div class="mt-2 w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                    <div class="bg-red-500 h-full rounded-full" style="width: 7%;"></div>
                </div>
                <span class="text-gray-500 dark:text-gray-400 text-xs mt-1">7%</span>
            </div>
            <div class="flex flex-col">
                <span class="text-gray-600 dark:text-gray-300 text-sm mb-1">أخرى</span>
                <span class="text-xl font-bold text-gray-800 dark:text-white">1,750 ر.س</span>
                <div class="mt-2 w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                    <div class="bg-purple-500 h-full rounded-full" style="width: 4%;"></div>
                </div>
                <span class="text-gray-500 dark:text-gray-400 text-xs mt-1">4%</span>
            </div>
        </div>
    </div>
</div>

<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2 md:mb-0">سجل المصروفات</h3>
            <div class="flex space-x-2 space-x-reverse">
                <div class="relative">
                    <input type="date" class="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white border-0 rounded-md p-2 text-sm focus:outline-none">
                </div>
                <div class="relative">
                    <select class="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white border-0 rounded-md p-2 text-sm focus:outline-none">
                        <option>جميع الفئات</option>
                        <option>المكونات</option>
                        <option>المرافق</option>
                        <option>الرواتب</option>
                        <option>الصيانة</option>
                        <option>أخرى</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
    
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
                <tr class="bg-gray-50 dark:bg-gray-700">
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        الوصف
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        الفئة
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        المبلغ
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        طريقة الدفع
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        تاريخ المصروف
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        سجل بواسطة
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        الإجراءات
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                        شراء لحوم طازجة
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <span class="px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                            المكونات
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                        3,250 ر.س
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        تحويل بنكي
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        15/03/2024
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        أحمد محمد
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <a href="{{ route('admin.expenses.edit', 1) }}" class="text-blue-500 hover:text-blue-700 transition-all">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button onclick="deleteExpense(1)" class="text-red-500 hover:text-red-700 transition-all">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                        فاتورة الكهرباء
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <span class="px-2 py-1 text-xs rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                            المرافق
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                        1,850 ر.س
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        تحويل بنكي
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        10/03/2024
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        سارة خالد
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <a href="{{ route('admin.expenses.edit', 2) }}" class="text-blue-500 hover:text-blue-700 transition-all">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button onclick="deleteExpense(2)" class="text-red-500 hover:text-red-700 transition-all">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                        رواتب الموظفين
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <span class="px-2 py-1 text-xs rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                            الرواتب
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                        10,320 ر.س
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        تحويل بنكي
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        05/03/2024
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        أحمد محمد
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <a href="{{ route('admin.expenses.edit', 3) }}" class="text-blue-500 hover:text-blue-700 transition-all">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button onclick="deleteExpense(3)" class="text-red-500 hover:text-red-700 transition-all">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                        صيانة الفرن
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <span class="px-2 py-1 text-xs rounded-full bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                            الصيانة
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                        850 ر.س
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        نقدي
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        02/03/2024
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        عبدالله محمد
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <a href="{{ route('admin.expenses.edit', 4) }}" class="text-blue-500 hover:text-blue-700 transition-all">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button onclick="deleteExpense(4)" class="text-red-500 hover:text-red-700 transition-all">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                        شراء خضروات طازجة
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <span class="px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                            المكونات
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                        1,250 ر.س
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        نقدي
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        01/03/2024
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        سارة خالد
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <a href="{{ route('admin.expenses.edit', 5) }}" class="text-blue-500 hover:text-blue-700 transition-all">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button onclick="deleteExpense(5)" class="text-red-500 hover:text-red-700 transition-all">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex justify-between items-center">
            <div class="text-sm text-gray-700 dark:text-gray-300">
                عرض <span class="font-medium">1</span> إلى <span class="font-medium">5</span> من <span class="font-medium">18</span> مصروف
            </div>
            <div class="flex items-center space-x-2 space-x-reverse">
                <button class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 disabled:opacity-50">
                    السابق
                </button>
                <button class="px-3 py-1 rounded-md bg-primary text-white hover:bg-primary/90">
                    1
                </button>
                <button class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600">
                    2
                </button>
                <button class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600">
                    3
                </button>
                <button class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600">
                    التالي
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تأكيد الحذف -->
<div id="deleteModal" class="fixed inset-0 bg-black/50 z-50 hidden flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-auto">
        <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-4">تأكيد الحذف</h3>
        <p class="text-gray-600 dark:text-gray-300 mb-6">هل أنت متأكد من رغبتك في حذف هذا المصروف؟ هذا الإجراء لا يمكن التراجع عنه.</p>
        <div class="flex justify-end space-x-2 space-x-reverse">
            <button id="cancelDelete" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600">إلغاء</button>
            <form id="deleteForm" method="POST" action="">
                @csrf
                @method('DELETE')
                <button type="submit" class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">حذف</button>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    function deleteExpense(id) {
        const deleteModal = document.getElementById('deleteModal');
        const deleteForm = document.getElementById('deleteForm');
        const cancelDelete = document.getElementById('cancelDelete');
        
        deleteForm.action = "{{ route('admin.expenses.delete', '') }}/" + id;
        deleteModal.classList.remove('hidden');
        
        cancelDelete.addEventListener('click', function() {
            deleteModal.classList.add('hidden');
        });
    }
</script>
@endsection