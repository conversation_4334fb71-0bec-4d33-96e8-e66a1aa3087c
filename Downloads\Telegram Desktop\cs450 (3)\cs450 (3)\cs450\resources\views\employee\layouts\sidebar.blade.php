<!-- القائمة الجانبية للشاشات الكبيرة -->
<aside id="sidebar" class="flex flex-col w-72 bg-white dark:bg-gray-800 shadow-lg sidebar-transition z-20 border-r border-gray-200 dark:border-gray-700">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="relative">
                    <span class="text-3xl font-black bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Eat Hub</span>
                    <div class="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full animate-pulse"></div>
                </div>
                <span class="mr-3 px-3 py-1 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 text-blue-700 dark:text-blue-300 text-xs rounded-full font-semibold border border-blue-200 dark:border-blue-700">الموظفين</span>
            </div>
            @if(Auth::user()->user_type == 'admin' || Auth::user()->can('dashboard.admin'))
            <a href="{{ route('admin.dashboard') }}"
               class="p-3 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group"
               title="العودة لواجهة المدير">
                <i class="fas fa-user-shield text-lg group-hover:scale-110 transition-transform"></i>
            </a>
            @endif
            <a href="{{ route('customer.index') }}"
               class="p-3 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group"
               title="الانتقال للصفحة الرئيسية">
                <i class="fas fa-home text-lg group-hover:scale-110 transition-transform"></i>
            </a>
        </div>
    </div>

    <div class="py-6 flex flex-col flex-1 overflow-y-auto">
        <nav class="px-6 space-y-2">
            <a href="{{ route('employee.dashboard') }}" data-page="dashboard" class="nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-700 dark:hover:text-blue-300 transition-all duration-300">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-tachometer-alt text-white"></i>
                </div>
                <span class="font-semibold">لوحة التحكم</span>
            </a>

            <a href="{{ route('employee.orders') }}" data-page="orders" class="nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-700 dark:hover:text-blue-300 transition-all duration-300">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-shopping-cart text-white"></i>
                </div>
                <span class="font-semibold">إدارة الطلبات</span>
            </a>

            <a href="{{ route('employee.reservations') }}" data-page="reservations" class="nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-purple-50 dark:hover:bg-purple-900/20 hover:text-purple-700 dark:hover:text-purple-300 transition-all duration-300">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-calendar-check text-white"></i>
                </div>
                <span class="font-semibold">إدارة الحجوزات</span>
            </a>

            <a href="{{ route('employee.tables') }}" data-page="tables" class="nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-green-50 dark:hover:bg-green-900/20 hover:text-green-700 dark:hover:text-green-300 transition-all duration-300">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-chair text-white"></i>
                </div>
                <span class="font-semibold">حالة الطاولات</span>
            </a>

            <a href="{{ route('employee.payments') }}" data-page="payments" class="nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-yellow-50 dark:hover:bg-yellow-900/20 hover:text-yellow-700 dark:hover:text-yellow-300 transition-all duration-300">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-yellow-500 to-orange-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-money-bill-wave text-white"></i>
                </div>
                <span class="font-semibold">المدفوعات</span>
            </a>

            <!-- <a href="{{ route('employee.menu') }}" data-page="menu" class="nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-700 dark:hover:text-red-300 transition-all duration-300">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-red-500 to-rose-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-utensils text-white"></i>
                </div>
                <span class="font-semibold">قائمة الطعام</span>
            </a> -->

            <a href="{{ route('employee.offers.index') }}" data-page="offers" class="nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-orange-50 dark:hover:bg-orange-900/20 hover:text-orange-700 dark:hover:text-orange-300 transition-all duration-300">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-orange-500 to-amber-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-tags text-white"></i>
                </div>
                <span class="font-semibold">إدارة العروض</span>
            </a>

            <a href="{{ route('employee.notifications') }}" data-page="notifications" class="nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-indigo-50 dark:hover:bg-indigo-900/20 hover:text-indigo-700 dark:hover:text-indigo-300 transition-all duration-300">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-indigo-500 to-blue-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-bell text-white"></i>
                </div>
                <span class="font-semibold">الإشعارات</span>
                <span id="notification-badge" class="mr-auto bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold animate-pulse"></span>
            </a>

            {{-- رابط لوحة الإدارة للموظفين المخولين --}}
            @if(auth()->user()->can('dashboard.admin'))
            <div class="border-t border-gray-200 dark:border-gray-700 my-4"></div>
            <a href="{{ route('admin.dashboard') }}" class="nav-link group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-orange-50 dark:hover:bg-orange-900/20 hover:text-orange-700 dark:hover:text-orange-300 transition-all duration-300 border-2 border-orange-200 dark:border-orange-700">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-orange-500 to-yellow-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-crown text-white"></i>
                </div>
                <span class="font-bold">لوحة الإدارة</span>
                <i class="fas fa-external-link-alt text-xs mr-auto transition-colors"></i>
            </a>
            @endif
        </nav>

        <div class="mt-auto px-6 py-4">
            <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                <form action="{{ route('logout') }}" method="POST" class="w-full">
                    @csrf
                    <button type="submit" class="w-full group flex items-center px-4 py-4 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-700 dark:hover:text-red-300 transition-all duration-300">
                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-red-500 to-pink-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-sign-out-alt text-white"></i>
                        </div>
                        <span class="font-semibold">تسجيل الخروج</span>
                    </button>
                </form>
            </div>
        </div>
    </div>
</aside>

<!-- شريط القائمة للجوال -->
<div id="mobileMenu" class="md:hidden glass backdrop-blur-xl shadow-glass sidebar-transition fixed inset-0 w-80 transform -translate-x-full z-30 border-r border-white/20">
    <div class="p-6 border-b border-white/10 flex justify-between items-center">
        <div class="flex items-center">
            <div class="relative">
                <span class="text-3xl font-black gradient-text">Eat Hub</span>
                <div class="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-primary to-secondary rounded-full animate-pulse"></div>
            </div>
            @if(Auth::user()->user_type == 'admin' || Auth::user()->can('dashboard.admin'))
            <a href="{{ route('admin.dashboard') }}"
               class="mr-3 p-3 rounded-xl glass hover:neon-glow text-gray-300 hover:text-white transition-all duration-300 group"
               title="العودة لواجهة المدير">
                <i class="fas fa-user-shield text-lg group-hover:scale-110 transition-transform"></i>
            </a>
            @endif
        </div>
        <button id="closeMobileMenu" class="p-3 rounded-xl glass hover:neon-glow text-white/80 hover:text-white transition-all duration-300 group">
            <i class="fas fa-times text-xl group-hover:rotate-90 transition-transform duration-300"></i>
        </button>
    </div>

    <div class="py-4">
        <nav class="px-4 space-y-1">
            <a href="{{ route('employee.dashboard') }}" data-page="dashboard" class="mobile-nav-link flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group">
                <i class="fas fa-tachometer-alt text-primary ml-3"></i>
                <span>لوحة التحكم</span>
            </a>

            <a href="{{ route('employee.orders') }}" data-page="orders" class="mobile-nav-link flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group">
                <i class="fas fa-shopping-cart text-primary ml-3"></i>
                <span>إدارة الطلبات</span>
            </a>

            <a href="{{ route('employee.reservations') }}" data-page="reservations" class="mobile-nav-link flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group">
                <i class="fas fa-calendar-check text-primary ml-3"></i>
                <span>إدارة الحجوزات</span>
            </a>

            <a href="{{ route('employee.tables') }}" data-page="tables" class="mobile-nav-link flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group">
                <i class="fas fa-chair text-primary ml-3"></i>
                <span>حالة الطاولات</span>
            </a>

            <a href="{{ route('employee.payments') }}" data-page="payments" class="mobile-nav-link flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group">
                <i class="fas fa-money-bill-wave text-primary ml-3"></i>
                <span>المدفوعات</span>
            </a>
<!-- 
            <a href="{{ route('employee.menu') }}" data-page="menu" class="mobile-nav-link flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group">
                <i class="fas fa-utensils text-primary ml-3"></i>
                <span>قائمة الطعام</span>
            </a> -->

            <a href="{{ route('employee.offers.index') }}" data-page="offers" class="mobile-nav-link flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group">
                <i class="fas fa-tags text-primary ml-3"></i>
                <span>إدارة العروض</span>
            </a>

            <a href="{{ route('employee.notifications') }}" data-page="notifications" class="mobile-nav-link flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group">
                <i class="fas fa-bell text-primary ml-3"></i>
                <span>الإشعارات</span>
                <span class="mr-auto bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"></span>
            </a>

            {{-- رابط لوحة الإدارة للموظفين المخولين في القائمة المحمولة --}}
            @if(auth()->user()->can('dashboard.admin'))
            <div class="border-t border-gray-200 dark:border-gray-700 my-2"></div>
            <a href="{{ route('admin.dashboard') }}" class="mobile-nav-link flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-orange-50 dark:hover:bg-orange-900/20 group border-2 border-orange-200 dark:border-orange-800">
                <i class="fas fa-crown text-orange-500 ml-3"></i>
                <span class="font-semibold text-orange-600 dark:text-orange-400">لوحة الإدارة</span>
                <i class="fas fa-external-link-alt text-xs text-orange-400 mr-auto"></i>
            </a>
            @endif

            {{-- رابط الصفحة الرئيسية --}}
            <a href="{{ route('customer.index') }}" class="mobile-nav-link flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 group border-2 border-green-200 dark:border-green-800">
                <i class="fas fa-home text-green-500 ml-3"></i>
                <span class="font-semibold text-green-600 dark:text-green-400">الصفحة الرئيسية</span>
                <i class="fas fa-external-link-alt text-xs text-green-400 mr-auto"></i>
            </a>
        </nav>
    </div>
</div>