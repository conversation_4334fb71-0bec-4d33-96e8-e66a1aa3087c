@extends('layouts.admin')

@section('title', 'تقرير المبيعات - لوحة تحكم Eat Hub')

@section('page-title', 'تقرير المبيعات')

@push('styles')
<!-- ApexCharts CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/apexcharts@latest/dist/apexcharts.css">
@endpush

@section('content')
<div class="mb-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-1">📊 تقرير المبيعات</h2>
            <p class="text-gray-600 dark:text-gray-400 text-sm">المبيعات اليومية والمنتجات الأكثر مبيعاً</p>
        </div>
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 sm:space-x-reverse">
            <a href="{{ route('admin.reports') }}" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 py-2 px-4 rounded-md transition-all">
                <i class="fas fa-arrow-right ml-1"></i>
                <span>العودة للتقارير</span>
            </a>
            <button onclick="window.open('{{ route('admin.reports.export.sales') }}', '_blank')" class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-md transition-all">
                <i class="fas fa-download ml-1"></i>
                <span>تصدير</span>
            </button>
            <button onclick="window.print()" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md transition-all print:hidden">
                <i class="fas fa-print ml-1"></i>
                <span>طباعة</span>
            </button>
        </div>
    </div>

    <!-- فلاتر التقرير -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
        <form action="{{ route('admin.reports.sales') }}" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="period" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">📅 الفترة الزمنية</label>
                <select id="period" name="period" onchange="toggleCustomDates(this.value)" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    <option value="week" {{ isset($period) && $period == 'week' ? 'selected' : '' }}>📅 آخر أسبوع</option>
                    <option value="month" {{ isset($period) && $period == 'month' ? 'selected' : '' }}>📅 آخر شهر</option>
                    <option value="quarter" {{ isset($period) && $period == 'quarter' ? 'selected' : '' }}>📅 آخر 3 أشهر</option>
                    <option value="year" {{ isset($period) && $period == 'year' ? 'selected' : '' }}>📅 آخر سنة</option>
                    <option value="custom" {{ isset($period) && $period == 'custom' ? 'selected' : '' }}>🎯 فترة مخصصة</option>
                </select>
            </div>
            <div id="custom_dates" class="grid grid-cols-1 md:grid-cols-2 gap-4 md:col-span-2" style="{{ isset($period) && $period == 'custom' ? '' : 'display: none;' }}">
                <div>
                    <label for="custom_start" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">📅 من تاريخ</label>
                    <input type="date" id="custom_start" name="custom_start" value="{{ isset($startDate) ? $startDate : '' }}" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                </div>
                <div>
                    <label for="custom_end" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">📅 إلى تاريخ</label>
                    <input type="date" id="custom_end" name="custom_end" value="{{ isset($endDate) ? $endDate : '' }}" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                </div>
            </div>
            <div class="flex items-end">
                <button type="submit" class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-2 px-4 rounded-md transition-all shadow-lg">
                    <i class="fas fa-filter ml-1"></i>
                    <span>تطبيق الفلتر</span>
                </button>
            </div>
        </form>
    </div>

    <!-- مؤشر الفترة المختارة -->
    <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-700 rounded-lg p-4 mb-6 border-l-4 border-blue-500">
        <div class="flex items-center">
            <i class="fas fa-calendar-alt text-blue-500 text-xl ml-3"></i>
            <div>
                <h4 class="font-semibold text-gray-800 dark:text-white">📊 تقرير المبيعات للفترة</h4>
                <p class="text-sm text-gray-600 dark:text-gray-300">
                    من {{ \Carbon\Carbon::parse($startDate)->format('d/m/Y') }} إلى {{ \Carbon\Carbon::parse($endDate)->format('d/m/Y') }}
                    <span class="mx-2">•</span>
                    <span class="font-medium">{{ \Carbon\Carbon::parse($startDate)->diffInDays(\Carbon\Carbon::parse($endDate)) + 1 }} يوم</span>
                </p>
            </div>
        </div>
    </div>

    <!-- ملخص المبيعات -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">💰 إجمالي المبيعات</p>
                    <h3 class="text-2xl font-bold text-green-600 dark:text-green-400">{{ isset($totalSales) ? number_format($totalSales, 2) : '0.00' }} د.ل</h3>
                    <p class="text-xs text-gray-500 mt-1">
                        @if(isset($dailySales) && count($dailySales) > 1)
                            @php
                                $lastDay = $dailySales->last()->total ?? 0;
                                $prevDay = $dailySales->slice(-2, 1)->first()->total ?? 0;
                                $change = $prevDay > 0 ? (($lastDay - $prevDay) / $prevDay) * 100 : 0;
                            @endphp
                            @if($change > 0)
                                <span class="text-green-500">↗ +{{ number_format($change, 1) }}%</span>
                            @elseif($change < 0)
                                <span class="text-red-500">↘ {{ number_format($change, 1) }}%</span>
                            @else
                                <span class="text-gray-500">→ 0%</span>
                            @endif
                            من أمس
                        @endif
                    </p>
                </div>
                <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3">
                    <i class="fas fa-money-bill-wave text-green-500 text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">🛒 عدد الطلبات</p>
                    <h3 class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ isset($ordersCount) ? number_format($ordersCount) : '0' }}</h3>
                    <p class="text-xs text-gray-500 mt-1">
                        {{ isset($ordersCount) && $ordersCount > 0 ? number_format($ordersCount / max(1, (strtotime($endDate ?? date('Y-m-d')) - strtotime($startDate ?? date('Y-m-d'))) / 86400 + 1), 1) : '0' }} طلب/يوم
                    </p>
                </div>
                <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3">
                    <i class="fas fa-shopping-cart text-blue-500 text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">📊 متوسط قيمة الطلب</p>
                    <h3 class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        {{ isset($averageOrderValue) ? number_format($averageOrderValue, 2) : '0.00' }} د.ل
                    </h3>
                    <p class="text-xs text-gray-500 mt-1">لكل طلب</p>
                </div>
                <div class="rounded-full bg-purple-100 dark:bg-purple-900/30 p-3">
                    <i class="fas fa-chart-line text-purple-500 text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">🏆 أفضل منتج</p>
                    <h3 class="text-lg font-bold text-orange-600 dark:text-orange-400">
                        @if(isset($topProducts) && count($topProducts) > 0)
                            {{ $topProducts->first()->name ?? 'غير محدد' }}
                        @else
                            لا يوجد
                        @endif
                    </h3>
                    <p class="text-xs text-gray-500 mt-1">
                        @if(isset($topProducts) && count($topProducts) > 0)
                            {{ number_format($topProducts->first()->quantity ?? 0) }} قطعة
                        @endif
                    </p>
                </div>
                <div class="rounded-full bg-orange-100 dark:bg-orange-900/30 p-3">
                    <i class="fas fa-trophy text-orange-500 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- مخططات المبيعات -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- مخطط المبيعات اليومية -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">📈 المبيعات اليومية</h3>
            </div>
            <div id="dailySalesChart" class="w-full h-80">
                <canvas id="dailySalesCanvas" height="320"></canvas>
            </div>
        </div>

        <!-- مخطط المبيعات حسب الفئة -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">🥘 توزيع المبيعات حسب الفئة</h3>
            </div>
            <div id="categoryChart" class="w-full h-80">
                <canvas id="categoryCanvas" height="320"></canvas>
            </div>
        </div>
    </div>

    <!-- المبيعات حسب الفئة -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">المبيعات حسب الفئة</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full text-sm">
                    <thead>
                        <tr class="bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                            <th class="py-2 px-3 text-right">الفئة</th>
                            <th class="py-2 px-3 text-right">المبيعات</th>
                            <th class="py-2 px-3 text-right">النسبة</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                        @if(isset($salesByCategory) && count($salesByCategory) > 0)
                            @php
                                $totalCategorySales = $salesByCategory->sum('total');
                            @endphp
                            @foreach($salesByCategory as $category)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                                <td class="py-3 px-3 font-medium text-gray-800 dark:text-white">
                                    @php
                                        $map = [
                                            'main' => 'الأطباق الرئيسية',
                                            'main course' => 'الأطباق الرئيسية',
                                            'mains' => 'الأطباق الرئيسية',
                                            'appetizer' => 'المقبلات',
                                            'starters' => 'المقبلات',
                                            'beverage' => 'المشروبات',
                                            'drink' => 'المشروبات',
                                            'drinks' => 'المشروبات',
                                            'dessert' => 'الحلويات',
                                            'sweets' => 'الحلويات',
                                            'other' => 'أخرى',
                                            'others' => 'أخرى',
                                        ];
                                        $label = $map[strtolower(trim($category->category ?? ''))] ?? ($category->category ?? 'غير محدد');
                                    @endphp
                                    {{ $label }}
                                </td>
                                <td class="py-3 px-3 text-gray-600 dark:text-gray-300">{{ number_format($category->total, 2) }} د.ل</td>
                                <td class="py-3 px-3">
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <div class="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                                            <div class="bg-primary h-full rounded-full" style="width: {{ $totalCategorySales > 0 ? ($category->total / $totalCategorySales) * 100 : 0 }}%;"></div>
                                        </div>
                                        <span class="text-gray-600 dark:text-gray-300">{{ $totalCategorySales > 0 ? number_format(($category->total / $totalCategorySales) * 100, 1) : 0 }}%</span>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        @else
                            <tr>
                                <td colspan="3" class="py-4 px-3 text-center text-gray-500">لا توجد بيانات متاحة</td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>

        <!-- المنتجات الأكثر مبيعاً -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">المنتجات الأكثر مبيعاً</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full text-sm">
                    <thead>
                        <tr class="bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                            <th class="py-2 px-3 text-right">المنتج</th>
                            <th class="py-2 px-3 text-right">الكمية</th>
                            <th class="py-2 px-3 text-right">المبيعات</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                        @if(isset($topProducts) && count($topProducts) > 0)
                            @foreach($topProducts as $product)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                                <td class="py-3 px-3 font-medium text-gray-800 dark:text-white">{{ $product->name }}</td>
                                <td class="py-3 px-3 text-gray-600 dark:text-gray-300">{{ $product->quantity }}</td>
                                <td class="py-3 px-3 text-gray-600 dark:text-gray-300">{{ number_format($product->total, 2) }} د.ل</td>
                            </tr>
                            @endforeach
                        @else
                            <tr>
                                <td colspan="3" class="py-4 px-3 text-center text-gray-500">لا توجد بيانات متاحة</td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- الطلبات الأخيرة -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white">🕒 آخر الطلبات المكتملة</h3>
            <span class="text-sm text-gray-500">آخر 10 طلبات</span>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full text-sm">
                <thead>
                    <tr class="bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                        <th class="py-2 px-3 text-right">رقم الطلب</th>
                        <th class="py-2 px-3 text-right">العميل</th>
                        <th class="py-2 px-3 text-right">المبلغ</th>
                        <th class="py-2 px-3 text-right">التاريخ</th>
                        <th class="py-2 px-3 text-right">الحالة</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    @php
                        $recentOrders = \App\Models\Order::where('status', 'completed')
                            ->whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])
                            ->with('user')
                            ->orderBy('created_at', 'desc')
                            ->limit(10)
                            ->get();
                    @endphp
                    @forelse($recentOrders as $order)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                        <td class="py-3 px-3 font-medium text-gray-800 dark:text-white">#{{ $order->order_id }}</td>
                        <td class="py-3 px-3 text-gray-600 dark:text-gray-300">
                            @if($order->user)
                                {{ $order->user->first_name . ' ' . $order->user->last_name }}
                            @elseif($order->customer_name)
                                {{ $order->customer_name }}
                            @else
                                عميل مجهول
                            @endif
                        </td>
                        <td class="py-3 px-3 text-gray-600 dark:text-gray-300">{{ number_format($order->total_amount, 2) }} د.ل</td>
                        <td class="py-3 px-3 text-gray-600 dark:text-gray-300">{{ $order->created_at->format('Y-m-d H:i') }}</td>
                        <td class="py-3 px-3">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                مكتمل
                            </span>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="5" class="py-4 px-3 text-center text-gray-500">لا توجد طلبات في هذه الفترة</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- إحصائيات إضافية -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">👥 إجمالي العملاء</p>
                    <h3 class="text-2xl font-bold text-indigo-600 dark:text-indigo-400">{{ isset($totalCustomers) ? number_format($totalCustomers) : '0' }}</h3>
                    <p class="text-xs text-gray-500 mt-1">عميل فريد</p>
                </div>
                <div class="rounded-full bg-indigo-100 dark:bg-indigo-900/30 p-3">
                    <i class="fas fa-users text-indigo-500 text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">📦 إجمالي القطع</p>
                    <h3 class="text-2xl font-bold text-teal-600 dark:text-teal-400">{{ isset($totalItems) ? number_format($totalItems) : '0' }}</h3>
                    <p class="text-xs text-gray-500 mt-1">قطعة مباعة</p>
                </div>
                <div class="rounded-full bg-teal-100 dark:bg-teal-900/30 p-3">
                    <i class="fas fa-box text-teal-500 text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">⚡ متوسط القطع/طلب</p>
                    <h3 class="text-2xl font-bold text-pink-600 dark:text-pink-400">
                        {{ isset($totalItems) && isset($ordersCount) && $ordersCount > 0 ? number_format($totalItems / $ordersCount, 1) : '0' }}
                    </h3>
                    <p class="text-xs text-gray-500 mt-1">قطعة لكل طلب</p>
                </div>
                <div class="rounded-full bg-pink-100 dark:bg-pink-900/30 p-3">
                    <i class="fas fa-calculator text-pink-500 text-xl"></i>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- Chart.js for charts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{{ asset('js/sales-charts-chartjs.js') }}"></script>
<script>
    function toggleCustomDates(value) {
        const customDatesDiv = document.getElementById('custom_dates');
        if (value === 'custom') {
            customDatesDiv.style.display = 'grid';
        } else {
            customDatesDiv.style.display = 'none';
        }
    }

    // تمرير البيانات للمخططات
    window.salesData = {
        daily: {!! json_encode($dailySales ?? []) !!},
        category: {!! json_encode($salesByCategory ?? []) !!}
    };

    // تهيئة المخططات عند تحميل الصفحة
    window.addEventListener('load', function() {
        setTimeout(function() {
            if (typeof initSalesChartsChartJS === 'function') {
                initSalesChartsChartJS();
            }
        }, 300);
    });


</script>

<!-- Chart.js for simple charts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

@endsection
