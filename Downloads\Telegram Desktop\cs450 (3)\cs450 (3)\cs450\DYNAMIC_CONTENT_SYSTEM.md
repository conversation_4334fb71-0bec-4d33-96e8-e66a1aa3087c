# 🔄 نظام المحتوى الديناميكي - Eat Hub

## 📋 نظرة عامة

تم تطوير نظام محتوى ديناميكي متقدم يقوم بتحديث الأطباق المميزة والعروض تلقائياً من قاعدة البيانات مع استخدام تقنيات الكاش لتحسين الأداء.

## ✨ المميزات الجديدة

### 🎯 **الأطباق المميزة الديناميكية**
- تحديث تلقائي كل ساعة
- عرض عشوائي لتنويع المحتوى
- استخدام الكاش لتحسين الأداء
- عرض 6 أطباق مميزة في كل مرة

### 🎁 **العروض النشطة**
- جلب العروض من قاعدة البيانات
- إلغاء تفعيل العروض المنتهية تلقائياً
- عرض العروض الحالية والنشطة فقط
- دعم أنواع مختلفة من العروض (طعام، حجز، مختلط)

### 💾 **نظام الكاش الذكي**
- كاش الأطباق المميزة لمدة ساعة
- كاش العروض النشطة لمدة 30 دقيقة
- كاش التقييمات لمدة 30 دقيقة
- مسح الكاش تلقائياً يومياً

## 🛠️ الأوامر المتاحة

### تحديث المحتوى يدوياً
```bash
php artisan featured:refresh
```

### إنشاء بيانات تجريبية
```bash
php artisan db:seed --class=DemoDataSeeder
```

### تشغيل المهام المجدولة
```bash
php artisan schedule:run
```

## 📊 البيانات التجريبية

### الأطباق المميزة (6 أطباق)
1. **برجر لحم أنجوس الفاخر** - 65 د.ل
2. **بيتزا مارجريتا الإيطالية** - 45 د.ل
3. **سلطة سيزر بالدجاج** - 35 د.ل
4. **ستيك لحم مشوي** - 85 د.ل
5. **معكرونة ألفريدو بالدجاج** - 55 د.ل
6. **تشيز كيك الفراولة** - 25 د.ل

### العروض الخاصة (3 عروض)
1. **خصم 30% على الوجبات العائلية**
   - خصم 30% للطلبات أكثر من 150 د.ل
   - صالح لمدة 30 يوم

2. **أطباق جديدة من المطبخ الآسيوي**
   - أطباق جديدة ومميزة
   - صالح لمدة 60 يوم

3. **ليالي الموسيقى والطعام**
   - خصم 15% أيام الخميس والجمعة
   - صالح لمدة 90 يوم

## ⚙️ المهام المجدولة

### تحديث تلقائي كل ساعة
- تحديث الأطباق المميزة
- تحديث العروض النشطة
- مسح الكاش القديم

### مهام يومية
- إلغاء تفعيل العروض المنتهية
- مسح الكاش الكامل

## 🔧 التكوين

### إعداد Cron Job (للخادم المباشر)
```bash
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

### متغيرات البيئة
```env
CACHE_DRIVER=file
CACHE_PREFIX=eathub_
```

## 📁 الملفات المهمة

### الكنترولرز
- `app/Http/Controllers/CustomerController.php` - الصفحة الرئيسية
- `app/Http/Controllers/OfferController.php` - العروض

### النماذج
- `app/Models/MenuItem.php` - الأطباق
- `app/Models/Offer.php` - العروض
- `app/Models/Review.php` - التقييمات

### الأوامر
- `app/Console/Commands/RefreshFeaturedItems.php` - تحديث المحتوى
- `app/Console/Kernel.php` - المهام المجدولة

### البيانات التجريبية
- `database/seeders/DemoDataSeeder.php` - البيانات التجريبية

### العروض
- `resources/views/customer/sections/featured-items.blade.php` - الأطباق المميزة
- `resources/views/customer/sections/reservation-offers.blade.php` - العروض

## 🚀 كيفية الاستخدام

### 1. تشغيل البيانات التجريبية
```bash
php artisan db:seed --class=DemoDataSeeder
```

### 2. تحديث المحتوى
```bash
php artisan featured:refresh
```

### 3. زيارة الصفحة الرئيسية
```
http://localhost:8000
```

## 📈 مراقبة الأداء

### فحص الكاش
```bash
php artisan cache:clear
php artisan config:clear
```

### فحص قاعدة البيانات
```bash
php artisan migrate:status
```

## 🔄 التحديث التلقائي

النظام يقوم بـ:
1. **تحديث الأطباق المميزة** كل ساعة بشكل عشوائي
2. **تحديث العروض النشطة** كل 30 دقيقة
3. **إلغاء العروض المنتهية** يومياً في منتصف الليل
4. **مسح الكاش القديم** يومياً

## 🎨 التحسينات البصرية

- تأثيرات hover جميلة
- تدرجات لونية
- أيقونات معبرة
- تأثيرات انتقالية سلسة
- تصميم متجاوب

## 📞 الدعم

للمساعدة أو الاستفسارات، يرجى مراجعة:
- ملف `README.md` الرئيسي
- ملف `MIGRATIONS_FIXED.md` لمشاكل قاعدة البيانات
- ملف `DYNAMIC_CONTENT_SYSTEM.md` (هذا الملف)

---

**تم التطوير بواسطة:** فريق Eat Hub  
**التاريخ:** أغسطس 2025  
**الإصدار:** 2.0
