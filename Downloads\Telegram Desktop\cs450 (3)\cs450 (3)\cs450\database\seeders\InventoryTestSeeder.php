<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Ingredient;
use App\Models\Inventory;

class InventoryTestSeeder extends Seeder
{
    public function run()
    {
        // إنشاء مكونات جديدة إذا لم تكن موجودة
        $ingredients = [
            ['name' => 'طماطم', 'unit' => 'كيلو'],
            ['name' => 'بصل', 'unit' => 'كيلو'],
            ['name' => 'جبن', 'unit' => 'كيلو'],
            ['name' => 'دقيق', 'unit' => 'كيلو'],
            ['name' => 'زيت', 'unit' => 'لتر'],
        ];

        foreach ($ingredients as $ing) {
            $ingredient = Ingredient::firstOrCreate(
                ['name' => $ing['name']],
                ['unit' => $ing['unit'], 'is_active' => true]
            );
            
            // حذف المخزون القديم لهذا المكون
            Inventory::where('ingredient_id', $ingredient->ingredient_id)->delete();
            
            // إضافة مخزون متنوع
            if ($ing['name'] == 'طماطم') {
                Inventory::create([
                    'ingredient_id' => $ingredient->ingredient_id,
                    'quantity' => 50, // مخزون جيد
                    'cost_per_unit' => 2.5,
                    'expiry_date' => now()->addDays(10)
                ]);
            } elseif ($ing['name'] == 'بصل') {
                Inventory::create([
                    'ingredient_id' => $ingredient->ingredient_id,
                    'quantity' => 5, // مخزون منخفض
                    'cost_per_unit' => 1.8,
                    'expiry_date' => now()->addDays(15)
                ]);
            } elseif ($ing['name'] == 'جبن') {
                Inventory::create([
                    'ingredient_id' => $ingredient->ingredient_id,
                    'quantity' => 0, // نفد من المخزون
                    'cost_per_unit' => 15.0,
                    'expiry_date' => now()->addDays(5)
                ]);
            } elseif ($ing['name'] == 'دقيق') {
                Inventory::create([
                    'ingredient_id' => $ingredient->ingredient_id,
                    'quantity' => 25, // مخزون جيد
                    'cost_per_unit' => 3.2,
                    'expiry_date' => now()->addDays(30)
                ]);
            } elseif ($ing['name'] == 'زيت') {
                Inventory::create([
                    'ingredient_id' => $ingredient->ingredient_id,
                    'quantity' => 8, // مخزون منخفض
                    'cost_per_unit' => 12.0,
                    'expiry_date' => now()->addDays(60)
                ]);
            }
        }

        echo "تم إضافة البيانات التجريبية للمخزون بنجاح!\n";
    }
}
