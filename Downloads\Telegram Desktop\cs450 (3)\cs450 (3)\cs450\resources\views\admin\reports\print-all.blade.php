<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقرير الشامل - Eat Hub</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #e74c3c;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .period {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 10px;
        }
        
        .header .generated {
            font-size: 0.9em;
            color: #999;
        }
        
        .report-section {
            margin-bottom: 40px;
            page-break-inside: avoid;
        }
        
        .section-title {
            background: #f8f9fa;
            padding: 15px;
            border-right: 5px solid #e74c3c;
            margin-bottom: 20px;
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .table tr:hover {
            background: #f8f9fa;
        }
        
        .positive {
            color: #27ae60;
        }
        
        .negative {
            color: #e74c3c;
        }
        
        .footer {
            margin-top: 50px;
            text-align: center;
            padding-top: 20px;
            border-top: 2px solid #eee;
            color: #666;
            font-size: 0.9em;
        }
        
        @media print {
            body {
                font-size: 12px;
            }
            
            .container {
                padding: 10px;
            }
            
            .report-section {
                page-break-inside: avoid;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🍽️ Eat Hub - التقرير الشامل</h1>
            <div class="period">
                الفترة: {{ \Carbon\Carbon::parse($data['period']['start'])->format('Y/m/d') }} - {{ \Carbon\Carbon::parse($data['period']['end'])->format('Y/m/d') }}
            </div>
            <div class="generated">
                تم إنشاء التقرير في: {{ now()->format('Y/m/d H:i') }}
            </div>
        </div>

        <!-- التقرير المالي -->
        <div class="report-section">
            <div class="section-title">💰 التقرير المالي</div>
            <div class="stats-grid">
                @if(isset($data['financial']['sales']['total']))
                <div class="stat-card">
                    <div class="stat-value positive">{{ number_format($data['financial']['sales']['total'], 2) }} د.ل</div>
                    <div class="stat-label">إجمالي المبيعات</div>
                </div>
                @endif

                @if(isset($data['financial']['expenses']['total']))
                <div class="stat-card">
                    <div class="stat-value negative">{{ number_format($data['financial']['expenses']['total'], 2) }} د.ل</div>
                    <div class="stat-label">إجمالي المصروفات</div>
                </div>
                @endif

                @if(isset($data['financial']['profits']['net']))
                <div class="stat-card">
                    <div class="stat-value {{ $data['financial']['profits']['net'] >= 0 ? 'positive' : 'negative' }}">
                        {{ number_format($data['financial']['profits']['net'], 2) }} د.ل
                    </div>
                    <div class="stat-label">صافي الربح</div>
                </div>
                @endif

                @if(isset($data['financial']['profits']['net_margin']))
                <div class="stat-card">
                    <div class="stat-value">{{ number_format($data['financial']['profits']['net_margin'], 1) }}%</div>
                    <div class="stat-label">هامش الربح</div>
                </div>
                @endif
            </div>
        </div>

        <!-- تقرير المبيعات -->
        <div class="report-section">
            <div class="section-title">📊 تقرير المبيعات</div>
            <div class="stats-grid">
                @if(isset($data['sales']['summary']['total_orders']))
                <div class="stat-card">
                    <div class="stat-value">{{ number_format($data['sales']['summary']['total_orders']) }}</div>
                    <div class="stat-label">إجمالي الطلبات</div>
                </div>
                @endif

                @if(isset($data['sales']['summary']['average_order_value']))
                <div class="stat-card">
                    <div class="stat-value">{{ number_format($data['sales']['summary']['average_order_value'], 2) }} د.ل</div>
                    <div class="stat-label">متوسط قيمة الطلب</div>
                </div>
                @endif

                @if(isset($data['sales']['summary']['total_items_sold']))
                <div class="stat-card">
                    <div class="stat-value">{{ number_format($data['sales']['summary']['total_items_sold']) }}</div>
                    <div class="stat-label">إجمالي الأصناف المباعة</div>
                </div>
                @endif

                @if(isset($data['sales']['summary']['daily_average']))
                <div class="stat-card">
                    <div class="stat-value">{{ number_format($data['sales']['summary']['daily_average'], 2) }} د.ل</div>
                    <div class="stat-label">متوسط المبيعات اليومية</div>
                </div>
                @endif
            </div>
        </div>

        <!-- تقرير الطلبات -->
        <div class="report-section">
            <div class="section-title">🛒 تقرير الطلبات</div>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">{{ $data['orders']['total_orders'] }}</div>
                    <div class="stat-label">إجمالي الطلبات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value positive">{{ $data['orders']['completed_orders'] }}</div>
                    <div class="stat-label">الطلبات المكتملة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: #f39c12;">{{ $data['orders']['pending_orders'] }}</div>
                    <div class="stat-label">الطلبات المعلقة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value negative">{{ $data['orders']['cancelled_orders'] }}</div>
                    <div class="stat-label">الطلبات الملغية</div>
                </div>
            </div>
        </div>

        <!-- تقرير المخزون -->
        <div class="report-section">
            <div class="section-title">📦 تقرير المخزون</div>
            <div class="stats-grid">
                @if(isset($data['inventory']['summary']['total_items']))
                <div class="stat-card">
                    <div class="stat-value">{{ number_format($data['inventory']['summary']['total_items']) }}</div>
                    <div class="stat-label">إجمالي الأصناف</div>
                </div>
                @endif

                @if(isset($data['inventory']['summary']['low_stock_items']))
                <div class="stat-card">
                    <div class="stat-value negative">{{ number_format($data['inventory']['summary']['low_stock_items']) }}</div>
                    <div class="stat-label">أصناف منخفضة المخزون</div>
                </div>
                @endif

                @if(isset($data['inventory']['summary']['out_of_stock_items']))
                <div class="stat-card">
                    <div class="stat-value">{{ number_format($data['inventory']['summary']['out_of_stock_items']) }}</div>
                    <div class="stat-label">أصناف نفدت من المخزون</div>
                </div>
                @endif

                @if(isset($data['inventory']['summary']['total_value']))
                <div class="stat-card">
                    <div class="stat-value">{{ number_format($data['inventory']['summary']['total_value'], 2) }} د.ل</div>
                    <div class="stat-label">قيمة المخزون الإجمالية</div>
                </div>
                @endif
            </div>
        </div>

        <!-- تقرير المصروفات -->
        <div class="report-section">
            <div class="section-title">💸 تقرير المصروفات</div>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value negative">{{ number_format($data['expenses']['total_expenses'], 2) }} د.ل</div>
                    <div class="stat-label">إجمالي المصروفات</div>
                </div>
            </div>
            
            @if(isset($data['expenses']['expenses_by_category']) && $data['expenses']['expenses_by_category']->count() > 0)
            <table class="table">
                <thead>
                    <tr>
                        <th>الفئة</th>
                        <th>المبلغ</th>
                        <th>النسبة</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data['expenses']['expenses_by_category'] as $expense)
                    <tr>
                        <td>{{ $expense->category }}</td>
                        <td>{{ number_format($expense->total, 2) }} د.ل</td>
                        <td>{{ number_format(($expense->total / $data['expenses']['total_expenses']) * 100, 1) }}%</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
            @endif
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام Eat Hub لإدارة المطاعم</p>
            <p>جميع الحقوق محفوظة © {{ date('Y') }}</p>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند تحميل الصفحة
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
