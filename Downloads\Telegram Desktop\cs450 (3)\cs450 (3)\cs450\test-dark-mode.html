<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام الموحد للوضع المظلم</title>
    
    <!-- تطبيق الوضع المظلم فوراً لمنع الوميض -->
    <script>
        (function() {
            const savedTheme = localStorage.getItem('darkMode');
            console.log('🔍 الثيم المحفوظ:', savedTheme);
            
            if (savedTheme === 'true' || savedTheme === 'enabled') {
                document.documentElement.classList.add('dark');
                console.log('🌙 تم تطبيق الوضع المظلم من الإعدادات المحفوظة');
            } else if (savedTheme === 'false' || savedTheme === 'disabled') {
                document.documentElement.classList.remove('dark');
                console.log('☀️ تم تطبيق الوضع الفاتح من الإعدادات المحفوظة');
            } else {
                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                    document.documentElement.classList.add('dark');
                    localStorage.setItem('darkMode', 'true');
                    console.log('🌙 تم تطبيق الوضع المظلم من إعدادات النظام');
                } else {
                    document.documentElement.classList.remove('dark');
                    localStorage.setItem('darkMode', 'false');
                    console.log('☀️ تم تطبيق الوضع الفاتح كافتراضي');
                }
            }
        })();
    </script>
    
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class'
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100 min-h-screen">
    <div class="container mx-auto p-8">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                    اختبار النظام الموحد للوضع المظلم
                </h1>
                
                <!-- زر تبديل الوضع المظلم -->
                <button id="darkModeToggle" class="p-3 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group">
                    <i id="darkModeIcon" class="fas fa-moon group-hover:rotate-180 transition-transform duration-500"></i>
                </button>
            </div>
            
            <div class="space-y-4">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h2 class="text-lg font-semibold mb-2">معلومات الوضع الحالي:</h2>
                    <div id="themeInfo" class="text-sm text-gray-600 dark:text-gray-300">
                        جاري التحميل...
                    </div>
                </div>
                
                <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                    <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">
                        <i class="fas fa-info-circle mr-2"></i>
                        تعليمات الاختبار:
                    </h3>
                    <ul class="text-blue-700 dark:text-blue-300 space-y-1">
                        <li>• اضغط على زر الوضع المظلم لتبديل الوضع</li>
                        <li>• أعد تحميل الصفحة للتأكد من ثبات الإعدادات</li>
                        <li>• افتح علامة تبويب جديدة للتأكد من مشاركة الإعدادات</li>
                        <li>• تحقق من console للرسائل التشخيصية</li>
                    </ul>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                        <h4 class="font-semibold text-green-800 dark:text-green-200">الوضع الفاتح</h4>
                        <p class="text-green-600 dark:text-green-300 text-sm">خلفية فاتحة، نص داكن</p>
                    </div>
                    
                    <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
                        <h4 class="font-semibold text-purple-800 dark:text-purple-200">الوضع المظلم</h4>
                        <p class="text-purple-600 dark:text-purple-300 text-sm">خلفية مظلمة، نص فاتح</p>
                    </div>
                </div>
                
                <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
                    <h4 class="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
                        <i class="fas fa-cog mr-2"></i>
                        إعدادات localStorage:
                    </h4>
                    <div id="storageInfo" class="text-yellow-700 dark:text-yellow-300 text-sm font-mono">
                        جاري التحميل...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- النظام الموحد للوضع المظلم -->
    <script src="js/unified-dark-mode.js"></script>
    
    <script>
        // تحديث معلومات الوضع
        function updateThemeInfo() {
            const isDark = document.documentElement.classList.contains('dark');
            const savedMode = localStorage.getItem('darkMode');
            
            document.getElementById('themeInfo').innerHTML = `
                <strong>الوضع الحالي:</strong> ${isDark ? 'مظلم 🌙' : 'فاتح ☀️'}<br>
                <strong>القيمة المحفوظة:</strong> ${savedMode || 'غير محدد'}<br>
                <strong>وقت التحديث:</strong> ${new Date().toLocaleTimeString('ar')}
            `;
            
            document.getElementById('storageInfo').innerHTML = `
                darkMode: "${localStorage.getItem('darkMode') || 'غير موجود'}"<br>
                theme_preference: "${localStorage.getItem('theme_preference') || 'غير موجود'}"<br>
                effective_theme: "${localStorage.getItem('effective_theme') || 'غير موجود'}"
            `;
        }
        
        // تحديث المعلومات عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            updateThemeInfo();
            
            // تحديث المعلومات عند تغيير الوضع
            window.addEventListener('darkModeChanged', updateThemeInfo);
            
            // تحديث المعلومات كل ثانية للمراقبة
            setInterval(updateThemeInfo, 1000);
        });
    </script>
</body>
</html>
