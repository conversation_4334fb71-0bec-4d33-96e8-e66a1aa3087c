@extends('layouts.admin')

@section('title', 'إدارة صلاحيات الموظفين')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <!-- Header -->
        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">إدارة صلاحيات الموظفين</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">منح أو إزالة الصلاحيات الإدارية للموظفين</p>
                </div>
                <div class="flex space-x-2 space-x-reverse">
                    <a href="{{ route('admin.permissions.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة للصلاحيات
                    </a>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="p-6">
            @if($employees->count() > 0)
                <div class="grid gap-6">
                    @foreach($employees as $employee)
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
                            <!-- Employee Info -->
                            <div class="flex justify-between items-start mb-4">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                        {{ $employee->first_name }} {{ $employee->last_name }}
                                    </h3>
                                    <p class="text-gray-600 dark:text-gray-400">{{ $employee->email }}</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $employee->phone }}</p>
                                </div>
                                <div class="flex space-x-2 space-x-reverse">
                                    @if($employee->can('dashboard.admin'))
                                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                                            <i class="fas fa-check ml-1"></i>
                                            لديه صلاحيات إدارية
                                        </span>
                                    @else
                                        <span class="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium">
                                            <i class="fas fa-times ml-1"></i>
                                            موظف عادي
                                        </span>
                                    @endif
                                </div>
                            </div>

                            <!-- Current Permissions -->
                            <div class="mb-4">
                                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-2">الصلاحيات الحالية:</h4>
                                <div class="flex flex-wrap gap-2">
                                    @php
                                        $userPermissions = $employee->getAllPermissions()->pluck('name')->toArray();
                                    @endphp
                                    @if(count($userPermissions) > 0)
                                        @foreach($userPermissions as $permission)
                                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                                                {{ $permission }}
                                            </span>
                                        @endforeach
                                    @else
                                        <span class="text-gray-500 dark:text-gray-400 text-sm">لا توجد صلاحيات إضافية</span>
                                    @endif
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="flex space-x-3 space-x-reverse">
                                @if(!$employee->can('dashboard.admin'))
                                    <!-- Grant Admin Access -->
                                    <form action="{{ route('admin.permissions.employees.grant', $employee->user_id) }}" method="POST" class="inline">
                                        @csrf
                                        <button type="submit" 
                                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md transition-colors"
                                                onclick="return confirm('هل أنت متأكد من إعطاء صلاحيات إدارية لهذا الموظف؟')">
                                            <i class="fas fa-user-shield ml-2"></i>
                                            إعطاء صلاحيات إدارية
                                        </button>
                                    </form>
                                @else
                                    <!-- Revoke Admin Access -->
                                    <form action="{{ route('admin.permissions.employees.revoke', $employee->user_id) }}" method="POST" class="inline">
                                        @csrf
                                        <button type="submit" 
                                                class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md transition-colors"
                                                onclick="return confirm('هل أنت متأكد من إزالة الصلاحيات الإدارية من هذا الموظف؟')">
                                            <i class="fas fa-user-times ml-2"></i>
                                            إزالة الصلاحيات الإدارية
                                        </button>
                                    </form>
                                @endif

                                <!-- Edit Detailed Permissions -->
                                <a href="{{ route('admin.permissions.edit-user', $employee->user_id) }}" 
                                   class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors">
                                    <i class="fas fa-edit ml-2"></i>
                                    تعديل الصلاحيات التفصيلية
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-12">
                    <i class="fas fa-users text-gray-400 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">لا يوجد موظفين</h3>
                    <p class="text-gray-600 dark:text-gray-400">لا يوجد موظفين نشطين في النظام حالياً</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Permission Categories Info -->
    <div class="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4">فئات الصلاحيات المتاحة</h2>
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            @foreach($permissionCategories as $category => $permissions)
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2 capitalize">
                        @switch($category)
                            @case('dashboard') لوحة التحكم @break
                            @case('orders') الطلبات @break
                            @case('reservations') الحجوزات @break
                            @case('menu') القائمة @break
                            @case('inventory') المخزون @break
                            @case('reports') التقارير @break
                            @case('users') المستخدمين @break
                            @case('settings') الإعدادات @break
                            @default {{ $category }}
                        @endswitch
                    </h3>
                    <div class="space-y-1">
                        @foreach($permissions as $permission)
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                • {{ $permission }}
                            </div>
                        @endforeach
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات تفاعلية
    const cards = document.querySelectorAll('.bg-gray-50');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.classList.add('shadow-md');
        });
        card.addEventListener('mouseleave', function() {
            this.classList.remove('shadow-md');
        });
    });
});
</script>
@endsection
