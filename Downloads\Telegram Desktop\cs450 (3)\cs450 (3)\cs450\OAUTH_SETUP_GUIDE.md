# دليل إعداد OAuth للحسابات الاجتماعية - Eat Hub

## 🚀 نظرة عامة

تم تفعيل التسجيل بالحسابات الاجتماعية في Eat Hub! الآن يمكن للمستخدمين تسجيل الدخول باستخدام:

- **🔍 Google** - الأكثر شيوعاً واستخداماً
- **📘 Facebook** - شائع في المنطقة العربية  
- **🍎 Apple** - مطلوب لتطبيقات iOS

## ✅ ما تم تنفيذه

### 1. **الكود والملفات**
- ✅ Laravel Socialite مثبت ومُعد
- ✅ SocialAuthController جاهز
- ✅ Routes للحسابات الاجتماعية
- ✅ Database migrations للحقول الجديدة
- ✅ User model محدث
- ✅ واجهات تسجيل الدخول والتسجيل محدثة

### 2. **الأمان والمعالجة**
- ✅ معالجة الأخطاء
- ✅ إنشاء حسابات جديدة تلقائياً
- ✅ ربط الحسابات الموجودة
- ✅ إعادة التوجيه حسب نوع المستخدم
- ✅ حفظ الصور الشخصية

## 🔧 خطوات الإعداد المطلوبة

### 1. إعداد Google OAuth

#### أ. إنشاء مشروع Google
1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
2. أنشئ مشروع جديد أو اختر موجود
3. فعّل **Google+ API** أو **Google Sign-In API**

#### ب. إنشاء OAuth Client
1. **APIs & Services** → **Credentials**
2. **Create Credentials** → **OAuth 2.0 Client ID**
3. **Application type**: Web Application
4. **Name**: Eat Hub - Google Login
5. **Authorized redirect URIs**:
   ```
   http://127.0.0.1:8000/auth/google/callback
   http://localhost:8000/auth/google/callback
   https://yourdomain.com/auth/google/callback
   ```

#### ج. تحديث .env
```env
GOOGLE_CLIENT_ID=your_actual_google_client_id_here
GOOGLE_CLIENT_SECRET=your_actual_google_client_secret_here
```

### 2. إعداد Facebook OAuth

#### أ. إنشاء تطبيق Facebook
1. اذهب إلى [Facebook Developers](https://developers.facebook.com/)
2. **My Apps** → **Create App**
3. اختر **Consumer** أو **Business**
4. أدخل اسم التطبيق: "Eat Hub"

#### ب. إضافة Facebook Login
1. **Add Product** → **Facebook Login**
2. **Settings** → **Valid OAuth Redirect URIs**:
   ```
   http://127.0.0.1:8000/auth/facebook/callback
   http://localhost:8000/auth/facebook/callback
   https://yourdomain.com/auth/facebook/callback
   ```

#### ج. تحديث .env
```env
FACEBOOK_CLIENT_ID=your_facebook_app_id_here
FACEBOOK_CLIENT_SECRET=your_facebook_app_secret_here
```

### 3. إعداد Apple OAuth (اختياري - للمطورين المتقدمين)

#### أ. Apple Developer Account
1. تحتاج حساب Apple Developer مدفوع ($99/سنة)
2. اذهب إلى [Apple Developer](https://developer.apple.com/)

#### ب. إنشاء App ID
1. **Certificates, Identifiers & Profiles**
2. **Identifiers** → **App IDs** → **+**
3. فعّل **Sign In with Apple**

#### ج. إنشاء Services ID
1. **Identifiers** → **Services IDs** → **+**
2. **Identifier**: com.eathub.signin
3. **Configure** → **Sign In with Apple**
4. **Primary App ID**: اختر App ID المُنشأ
5. **Web Domain**: yourdomain.com
6. **Return URLs**: https://yourdomain.com/auth/apple/callback

#### د. تحديث .env
```env
APPLE_CLIENT_ID=com.eathub.signin
APPLE_CLIENT_SECRET=generated_jwt_token_here
```

## 🧪 الاختبار

### 1. اختبار محلي
```bash
# تشغيل الخادم
php artisan serve

# اختبار الصفحات
http://127.0.0.1:8000/login
http://127.0.0.1:8000/register
```

### 2. اختبار الروابط المباشرة
```bash
# Google
http://127.0.0.1:8000/auth/google

# Facebook  
http://127.0.0.1:8000/auth/facebook

# Apple
http://127.0.0.1:8000/auth/apple
```

## 🎯 سير العمل

### للمستخدمين الجدد:
1. ينقر على "تسجيل الدخول بـ Google"
2. يُحول لصفحة Google للموافقة
3. يعود للموقع مع البيانات
4. يتم إنشاء حساب جديد تلقائياً
5. يُسجل دخوله ويُحول للوحة التحكم

### للمستخدمين الموجودين:
1. ينقر على "تسجيل الدخول بـ Google"
2. يتم ربط حساب Google بحسابه الموجود
3. يُسجل دخوله مباشرة

## ⚠️ ملاحظات مهمة

### الأمان
- **لا تشارك** Client Secret أبداً
- استخدم **HTTPS** في الإنتاج
- **اختبر** كل مقدم خدمة قبل النشر

### البيئات
- **التطوير**: استخدم localhost/127.0.0.1
- **الإنتاج**: استخدم النطاق الحقيقي
- **اختبار**: استخدم نطاق فرعي للاختبار

### المتطلبات
- **Google**: مجاني، سهل الإعداد
- **Facebook**: مجاني، يحتاج مراجعة للتطبيقات العامة
- **Apple**: يحتاج حساب مطور مدفوع

## 🔍 استكشاف الأخطاء

### "Invalid redirect URI"
```bash
# تأكد من إضافة الرابط الصحيح في إعدادات OAuth
# تحقق من عدم وجود مسافات أو أخطاء إملائية
```

### "Invalid client"
```bash
# تأكد من صحة Client ID و Client Secret في .env
# تأكد من تفعيل الخدمة في لوحة التحكم
```

### "Access denied"
```bash
# تأكد من طلب الصلاحيات المناسبة
# تحقق من حالة التطبيق (Development/Production)
```

## 📞 الدعم

للمساعدة:
- [Laravel Socialite Docs](https://laravel.com/docs/socialite)
- [Google OAuth Docs](https://developers.google.com/identity/protocols/oauth2)
- [Facebook Login Docs](https://developers.facebook.com/docs/facebook-login/)

---

**🎉 بمجرد إعداد المفاتيح، ستعمل الحسابات الاجتماعية فوراً!**
