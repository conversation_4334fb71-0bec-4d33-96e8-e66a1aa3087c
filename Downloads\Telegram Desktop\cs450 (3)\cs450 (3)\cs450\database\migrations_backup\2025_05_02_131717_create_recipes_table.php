<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        Schema::create('recipes', function (Blueprint $table) {
            $table->id('recipe_id');
            $table->foreignId('menu_item_id')->constrained('menu_items')->onDelete('cascade');
            $table->foreignId('ingredient_id')->constrained('ingredients')->onDelete('cascade');
            $table->decimal('quantity', 10, 2);
            $table->timestamps();
            
            $table->unique(['menu_item_id', 'ingredient_id']);
        });

        // إضافة constraint يدوياً
        DB::statement('ALTER TABLE recipes ADD CONSTRAINT quantity_positive CHECK (quantity > 0)');
    }

    public function down()
    {
        Schema::dropIfExists('recipes');
    }
};