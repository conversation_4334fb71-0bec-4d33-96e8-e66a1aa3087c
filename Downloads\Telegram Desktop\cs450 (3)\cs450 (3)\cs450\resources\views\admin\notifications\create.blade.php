@extends('layouts.admin')

@section('title', 'إرسال إشعار جديد')

@section('content')
<div id="create-notification-page" class="page fade-in">
    <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">إرسال إشعار جديد</h2>
            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <a href="{{ route('admin.dashboard') }}" class="hover:text-primary">لوحة التحكم</a>
                <i class="fas fa-chevron-left mx-2 text-xs"></i>
                <a href="{{ route('admin.notifications') }}" class="hover:text-primary">الإشعارات</a>
                <i class="fas fa-chevron-left mx-2 text-xs"></i>
                <span>إرسال إشعار جديد</span>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white flex items-center">
                <i class="fas fa-bell text-primary ml-2"></i>
                تفاصيل الإشعار
            </h3>
        </div>
        <div class="p-6">
            <form action="{{ route('admin.notifications.send') }}" method="POST">
                @csrf

                @if ($errors->any())
                <div class="mb-6 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 p-4 rounded-md">
                    <ul class="list-disc list-inside">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
                @endif

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="md:col-span-2">
                        <label for="recipients" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المستلمون <span class="text-red-500">*</span></label>
                        <select name="recipients" id="recipients" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('recipients') border-red-500 @enderror" required>
                            <option value="">اختر المستلمين</option>
                            <option value="all" {{ old('recipients') == 'all' ? 'selected' : '' }}>جميع المستخدمين</option>
                            <option value="admins" {{ old('recipients') == 'admins' ? 'selected' : '' }}>المسؤولين فقط</option>
                            <option value="employees" {{ old('recipients') == 'employees' ? 'selected' : '' }}>الموظفين فقط</option>
                            <option value="staff" {{ old('recipients') == 'staff' ? 'selected' : '' }}>طاقم العمل (المسؤولين والموظفين)</option>
                            <option value="customers" {{ old('recipients') == 'customers' ? 'selected' : '' }}>العملاء فقط</option>
                            <option value="specific" {{ old('recipients') == 'specific' ? 'selected' : '' }}>مستخدم محدد</option>
                        </select>
                        @error('recipients')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div id="specificUserContainer" class="md:col-span-2 {{ old('recipients') == 'specific' ? '' : 'hidden' }}">
                        <label for="user_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اختر المستخدم <span class="text-red-500">*</span></label>
                        <select name="user_id" id="user_id" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('user_id') border-red-500 @enderror">
                            <option value="">اختر المستخدم</option>
                            @foreach($users as $user)
                                <option value="{{ $user->user_id }}" {{ old('user_id') == $user->user_id ? 'selected' : '' }}>
                                    {{ $user->first_name }} {{ $user->last_name }} ({{ $user->email }}) - {{ $user->user_type == 'admin' ? 'مسؤول' : ($user->user_type == 'employee' ? 'موظف' : 'عميل') }}
                                </option>
                            @endforeach
                        </select>
                        @error('user_id')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="md:col-span-2">
                        <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع الإشعار <span class="text-red-500">*</span></label>
                        <select name="type" id="type" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('type') border-red-500 @enderror" required>
                            <option value="">اختر النوع</option>
                            <option value="system" {{ old('type') == 'system' ? 'selected' : '' }}>إشعار نظام</option>
                            <option value="order" {{ old('type') == 'order' ? 'selected' : '' }}>إشعار طلب</option>
                            <option value="reservation" {{ old('type') == 'reservation' ? 'selected' : '' }}>إشعار حجز</option>
                            <option value="inventory" {{ old('type') == 'inventory' ? 'selected' : '' }}>إشعار مخزون</option>
                        </select>
                        @error('type')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="md:col-span-2">
                        <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">عنوان الإشعار <span class="text-red-500">*</span></label>
                        <input type="text" name="title" id="title" value="{{ old('title') }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('title') border-red-500 @enderror" required>
                        @error('title')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="md:col-span-2">
                        <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نص الإشعار <span class="text-red-500">*</span></label>
                        <textarea name="message" id="message" rows="4" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('message') border-red-500 @enderror" required>{{ old('message') }}</textarea>
                        @error('message')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="action_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رابط الإجراء</label>
                        <input type="text" name="action_url" id="action_url" value="{{ old('action_url') }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('action_url') border-red-500 @enderror">
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">رابط الصفحة التي سيتم توجيه المستخدم إليها عند النقر على الإشعار (اختياري)</p>
                        @error('action_url')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="action_text" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نص الإجراء</label>
                        <input type="text" name="action_text" id="action_text" value="{{ old('action_text') }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('action_text') border-red-500 @enderror">
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">النص الذي سيظهر على زر الإجراء (اختياري)</p>
                        @error('action_text')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="flex justify-end space-x-3 space-x-reverse">
                    <a href="{{ route('admin.notifications') }}" class="px-4 py-2 bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-400 dark:hover:bg-gray-600 transition-colors">
                        إلغاء
                    </a>
                    <button type="submit" class="px-4 py-2 bg-primary hover:bg-primary/90 text-white rounded-md transition-colors">
                        إرسال الإشعار
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const recipientsSelect = document.getElementById('recipients');
        const specificUserContainer = document.getElementById('specificUserContainer');
        const userIdSelect = document.getElementById('user_id');

        recipientsSelect.addEventListener('change', function() {
            if (this.value === 'specific') {
                specificUserContainer.classList.remove('hidden');
                userIdSelect.setAttribute('required', 'required');
            } else {
                specificUserContainer.classList.add('hidden');
                userIdSelect.removeAttribute('required');
            }
        });
    });
</script>
@endsection
@endsection
