@extends('layouts.admin')

@section('title', 'تقرير العملاء')

@section('content')
<div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <div>
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white">👥 تقرير العملاء</h2>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">عرض تفاصيل وإحصائيات العملاء والمبيعات</p>
    </div>
    <div class="mt-4 md:mt-0 flex space-x-2 space-x-reverse">
        <!-- أزرار التصدير والطباعة -->
        <button onclick="window.open('{{ route('admin.reports.export.customers') }}', '_blank')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center transition-all">
            <i class="fas fa-download mr-2"></i>
            تصدير
        </button>
        <button onclick="window.print()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center transition-all">
            <i class="fas fa-print mr-2"></i>
            طباعة
        </button>
        <a href="{{ route('admin.reports') }}" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-arrow-right ml-2"></i>
            <span>العودة للتقارير</span>
        </a>
    </div>
</div>

<!-- فلاتر التقرير -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">🔍 فلاتر التقرير</h3>
    <form method="GET" action="{{ route('admin.reports.customers') }}" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- فترة زمنية -->
        <div>
            <label for="period" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">📅 الفترة الزمنية</label>
            <select id="period" name="period" onchange="toggleCustomDates(this.value)" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                <option value="all" {{ request('period') == 'all' ? 'selected' : '' }}>🌐 جميع الفترات</option>
                <option value="today" {{ request('period') == 'today' ? 'selected' : '' }}>📅 اليوم</option>
                <option value="week" {{ request('period') == 'week' ? 'selected' : '' }}>📊 هذا الأسبوع</option>
                <option value="month" {{ request('period') == 'month' ? 'selected' : '' }}>📈 هذا الشهر</option>
                <option value="quarter" {{ request('period') == 'quarter' ? 'selected' : '' }}>📋 هذا الربع</option>
                <option value="year" {{ request('period') == 'year' ? 'selected' : '' }}>📆 هذا العام</option>
                <option value="custom" {{ request('period') == 'custom' ? 'selected' : '' }}>🎯 فترة مخصصة</option>
            </select>
        </div>

        <!-- حالة العميل -->
        <div>
            <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">👤 حالة العميل</label>
            <select id="status" name="status" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                <option value="all" {{ request('status') == 'all' ? 'selected' : '' }}>🌐 جميع العملاء</option>
                <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>✅ عملاء نشطين</option>
                <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>⚠️ عملاء غير نشطين</option>
                <option value="new" {{ request('status') == 'new' ? 'selected' : '' }}>🆕 عملاء جدد</option>
            </select>
        </div>

        <!-- البحث -->
        <div>
            <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">🔍 البحث</label>
            <input type="text" id="search" name="search" value="{{ request('search') }}" placeholder="اسم العميل أو البريد أو الهاتف..." class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
        </div>

        <!-- زر التطبيق -->
        <div class="flex items-end">
            <button type="submit" class="w-full bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-md transition-all">
                <i class="fas fa-search ml-1"></i>
                <span>تطبيق الفلتر</span>
            </button>
        </div>

        <!-- التواريخ المخصصة -->
        <div id="custom_dates" class="grid grid-cols-1 md:grid-cols-2 gap-4 md:col-span-4" style="{{ request('period') == 'custom' ? '' : 'display: none;' }}">
            <div>
                <label for="custom_start" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">📅 من تاريخ</label>
                <input type="date" id="custom_start" name="start_date" value="{{ request('start_date') }}" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
            </div>
            <div>
                <label for="custom_end" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">📅 إلى تاريخ</label>
                <input type="date" id="custom_end" name="end_date" value="{{ request('end_date') }}" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
            </div>
        </div>
    </form>
</div>

<!-- ملخص العملاء المحسن -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border-l-4 border-blue-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي العملاء</p>
                <p class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ number_format($totalCustomers) }}</p>
                <p class="text-xs text-gray-500 mt-1">عميل مسجل</p>
            </div>
            <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3">
                <i class="fas fa-users text-blue-500 text-2xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border-l-4 border-green-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">عملاء نشطين</p>
                <p class="text-3xl font-bold text-green-600 dark:text-green-400">{{ number_format($activeCustomers) }}</p>
                <p class="text-xs text-gray-500 mt-1">لديهم طلبات</p>
            </div>
            <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3">
                <i class="fas fa-user-check text-green-500 text-2xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border-l-4 border-purple-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">عملاء جدد</p>
                <p class="text-3xl font-bold text-purple-600 dark:text-purple-400">{{ number_format($newCustomers) }}</p>
                <p class="text-xs text-gray-500 mt-1">في الفترة المحددة</p>
            </div>
            <div class="rounded-full bg-purple-100 dark:bg-purple-900/30 p-3">
                <i class="fas fa-user-plus text-purple-500 text-2xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border-l-4 border-yellow-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">متوسط قيمة الطلب</p>
                <p class="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{{ number_format($averageOrderValuePerCustomer ?? 0, 2) }}</p>
                <p class="text-xs text-gray-500 mt-1">دينار ليبي</p>
            </div>
            <div class="rounded-full bg-yellow-100 dark:bg-yellow-900/30 p-3">
                <i class="fas fa-money-bill-wave text-yellow-500 text-2xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات إضافية -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border-l-4 border-green-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي الإيرادات</p>
                <p class="text-3xl font-bold text-green-600 dark:text-green-400">{{ number_format($totalRevenue ?? 0, 2) }}</p>
                <p class="text-xs text-gray-500 mt-1">دينار ليبي</p>
            </div>
            <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3">
                <i class="fas fa-chart-line text-green-500 text-2xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border-l-4 border-blue-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي الطلبات</p>
                <p class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ number_format($totalOrders ?? 0) }}</p>
                <p class="text-xs text-gray-500 mt-1">طلب مكتمل</p>
            </div>
            <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3">
                <i class="fas fa-shopping-cart text-blue-500 text-2xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- تخطيط شبكي: جدول العملاء + الشريط الجانبي -->
<div class="grid grid-cols-1 xl:grid-cols-4 gap-6 mb-6">
    <!-- جدول العملاء -->
    <div class="xl:col-span-3">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">📋 قائمة العملاء</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">عرض تفاصيل العملاء مع إحصائيات الطلبات</p>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">العميل</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">معلومات الاتصال</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">عدد الطلبات</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">إجمالي المشتريات</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">تاريخ التسجيل</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($customers as $customer)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="rounded-full bg-primary/10 p-2 ml-3">
                                            <i class="fas fa-user text-primary text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                {{ $customer->first_name }} {{ $customer->last_name }}
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 dark:text-white">{{ $customer->email }}</div>
                                    <div class="text-sm text-gray-500">{{ $customer->phone ?? 'غير محدد' }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                        {{ number_format($customer->orders_count) }} طلب
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                                        {{ number_format($customer->total_spent, 2) }} د.ل
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ \Carbon\Carbon::parse($customer->created_at)->format('Y-m-d') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($customer->orders_count > 0)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                            <i class="fas fa-check-circle mr-1"></i>
                                            نشط
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                                            <i class="fas fa-clock mr-1"></i>
                                            غير نشط
                                        </span>
                                    @endif
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="px-6 py-12 text-center">
                                    <div class="text-gray-500 dark:text-gray-400">
                                        <i class="fas fa-users text-4xl mb-4"></i>
                                        <p class="text-lg font-medium">لا توجد عملاء</p>
                                        <p class="text-sm">لم يتم العثور على عملاء مطابقين للفلاتر المحددة</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($customers->hasPages())
                <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                    {{ $customers->appends(request()->query())->links() }}
                </div>
            @endif
        </div>
    </div>

    <!-- الشريط الجانبي -->
    <div class="xl:col-span-1 space-y-6">
        <!-- أفضل 5 عملاء -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h4 class="text-lg font-bold text-gray-800 dark:text-white mb-4">
                <i class="fas fa-crown text-yellow-500 mr-2"></i>
                أفضل العملاء
            </h4>
            <div class="space-y-3">
                @foreach($topCustomers->take(5) as $index => $customer)
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 ml-3">
                                @if($index == 0)
                                    <span class="inline-flex items-center justify-center w-6 h-6 bg-yellow-500 text-white text-xs font-bold rounded-full">1</span>
                                @elseif($index == 1)
                                    <span class="inline-flex items-center justify-center w-6 h-6 bg-gray-400 text-white text-xs font-bold rounded-full">2</span>
                                @elseif($index == 2)
                                    <span class="inline-flex items-center justify-center w-6 h-6 bg-orange-500 text-white text-xs font-bold rounded-full">3</span>
                                @else
                                    <span class="inline-flex items-center justify-center w-6 h-6 bg-blue-500 text-white text-xs font-bold rounded-full">{{ $index + 1 }}</span>
                                @endif
                            </div>
                            <div class="min-w-0 flex-1">
                                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                    {{ $customer->first_name }} {{ $customer->last_name }}
                                </p>
                                <p class="text-xs text-gray-500">{{ $customer->orders_count }} طلب</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-bold text-green-600 dark:text-green-400">
                                {{ number_format($customer->total_spent, 0) }}
                            </p>
                            <p class="text-xs text-gray-500">د.ل</p>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h4 class="text-lg font-bold text-gray-800 dark:text-white mb-4">
                <i class="fas fa-chart-bar text-blue-500 mr-2"></i>
                إحصائيات سريعة
            </h4>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">معدل النشاط</span>
                    <span class="text-sm font-bold text-blue-600 dark:text-blue-400">
                        {{ $totalCustomers > 0 ? number_format(($activeCustomers / $totalCustomers) * 100, 1) : 0 }}%
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">متوسط الطلبات/عميل</span>
                    <span class="text-sm font-bold text-green-600 dark:text-green-400">
                        {{ $activeCustomers > 0 ? number_format($totalOrders / $activeCustomers, 1) : 0 }}
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">متوسط الإنفاق/عميل</span>
                    <span class="text-sm font-bold text-purple-600 dark:text-purple-400">
                        {{ $activeCustomers > 0 ? number_format($totalRevenue / $activeCustomers, 0) : 0 }} د.ل
                    </span>
                </div>
            </div>
        </div>

        <!-- إجراءات سريعة -->
        <!-- <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h4 class="text-lg font-bold text-gray-800 dark:text-white mb-4">
                <i class="fas fa-tools text-gray-500 mr-2"></i>
                إجراءات سريعة
            </h4>
            <div class="space-y-3">
                <button onclick="window.open('{{ route('admin.reports.export.customers') }}', '_blank')" class="w-full bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-md transition-colors text-sm font-medium">
                    <i class="fas fa-file-excel mr-2"></i>
                    تصدير Excel
                </button>
                <button onclick="window.print()" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md transition-colors text-sm font-medium">
                    <i class="fas fa-print mr-2"></i>
                    طباعة التقرير
                </button>
                <a href="{{ route('admin.users') }}" class="block w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md transition-colors text-sm font-medium text-center">
                    <i class="fas fa-users mr-2"></i>
                    إدارة المستخدمين
                </a>
            </div>
        </div> -->
    </div>
</div>



<!-- رسم بياني لتوزيع العملاء -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">نسبة العملاء النشطين</h3>
        <div class="h-64">
            <canvas id="activeCustomersChart"></canvas>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">نسبة العملاء الجدد</h3>
        <div class="h-64">
            <canvas id="newCustomersChart"></canvas>
        </div>
    </div>
</div>

@endsection
@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // بيانات المخططات (استخدام قيم Blade مباشرة مع Number لتجنب أخطاء التحويل)
        const activeCustomers = Number({{ $activeCustomers ?? 0 }});
        const totalCustomers  = Number({{ $totalCustomers ?? 0 }});
        const newCustomers    = Number({{ $newCustomers ?? 0 }});
        const inactiveCustomers = Math.max(0, totalCustomers - activeCustomers);
        const oldCustomers     = Math.max(0, totalCustomers - newCustomers);

        function renderPie(canvasEl, labels, data, colors) {
            if (!canvasEl || typeof Chart === 'undefined') return;

            // إذا كانت جميع القيم صفر، نعرض شكل بديل رمادي ليوضح عدم وجود بيانات
            const sum = data.reduce((a, b) => a + b, 0);
            const dataset = sum > 0 ? data : [1];
            const dsColors = sum > 0 ? colors : ['rgba(203,213,225,0.6)'];
            const lbls = sum > 0 ? labels : ['لا توجد بيانات'];

            new Chart(canvasEl.getContext('2d'), {
                type: 'pie',
                data: {
                    labels: lbls,
                    datasets: [{
                        data: dataset,
                        backgroundColor: dsColors,
                        borderColor: dsColors.map(c => c.replace('0.6', '1')),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                font: { family: 'Tajawal' }
                            }
                        }
                    }
                }
            });
        }

        // رسم بياني للعملاء النشطين
        renderPie(
            document.getElementById('activeCustomersChart'),
            ['نشط', 'غير نشط'],
            [activeCustomers, inactiveCustomers],
            ['rgba(72, 187, 120, 0.6)', 'rgba(160, 174, 192, 0.6)']
        );

        // رسم بياني للعملاء الجدد
        renderPie(
            document.getElementById('newCustomersChart'),
            ['جديد', 'قديم'],
            [newCustomers, oldCustomers],
            ['rgba(66, 153, 225, 0.6)', 'rgba(160, 174, 192, 0.6)']
        );

        // تبديل عرض التواريخ المخصصة
        window.toggleCustomDates = function(period) {
            const customDates = document.getElementById('custom_dates');
            if (period === 'custom') {
                customDates.style.display = 'grid';
            } else {
                customDates.style.display = 'none';
            }
        }

        // تحديث الصفحة عند تغيير الفترة الزمنية
        const periodSelect = document.getElementById('period');
        if (periodSelect) {
            periodSelect.addEventListener('change', function() {
                if (this.value !== 'custom') {
                    // إرسال النموذج تلقائياً عند اختيار فترة محددة مسبقاً
                    this.form.submit();
                }
            });
        }
    });
</script>
@endsection
