@extends('layouts.admin')

@section('title', 'إدارة قائمة الطعام - لوحة تحكم Eat Hub')

@section('page-title', 'إدارة قائمة الطعام')

@section('content')
<div class="space-y-6 h-full">
    <!-- العنوان الرئيسي -->
    <div class="flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">إدارة قائمة الطعام</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">إدارة عناصر القائمة والأصناف</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <form action="{{ route('admin.menu') }}" method="GET" class="flex space-x-2 space-x-reverse">
                <div class="relative">
                    <input type="text" name="search" value="{{ request('search') }}" placeholder="بحث..." class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary text-base">
                    <button type="submit" class="absolute left-3 top-2.5 text-gray-500 dark:text-gray-400">
                        <i class="fas fa-search"></i>
                    </button>
                    @if(request('category'))
                        <input type="hidden" name="category" value="{{ request('category') }}">
                    @endif
                </div>
            </form>
            <a href="{{ route('admin.menu.recipes') }}" class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded-lg flex items-center transition-all">
                <i class="fas fa-calculator ml-2"></i>
                <span>إدارة التكاليف</span>
            </a>
            <a href="{{ route('admin.menu.create') }}" class="bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-lg flex items-center transition-all">
                <i class="fas fa-plus ml-2"></i>
                <span>إضافة منتج</span>
            </a>
        </div>
    </div>

<div class="mb-6 flex flex-wrap gap-2">
    <a href="{{ route('admin.menu') }}" class="px-4 py-2 {{ request('category') ? 'bg-white dark:bg-gray-700 text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-600' : 'bg-primary text-white' }} rounded-md">الكل</a>
    @foreach($categories as $key => $label)
        <a href="{{ route('admin.menu', ['category' => $key, 'search' => request('search')]) }}" class="px-4 py-2 {{ request('category') == $key ? 'bg-primary text-white' : 'bg-white dark:bg-gray-700 text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-600' }} rounded-md">{{ $label }}</a>
    @endforeach
</div>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    @if($menuItems->count() > 0)
        @foreach($menuItems as $item)
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            <div class="relative h-48">
                @if($item->image_url)
                <img src="{{ $item->image_url }}" alt="{{ $item->name }}" class="w-full h-full object-cover">
                @else
                <div class="w-full h-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                    <i class="fas fa-image text-gray-400 text-5xl"></i>
                </div>
                @endif
                <div class="absolute top-2 right-2">
                    @if($item->is_available)
                    <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs">متوفر</span>
                    @else
                    <span class="bg-red-500 text-white px-2 py-1 rounded-full text-xs">غير متوفر</span>
                    @endif
                </div>
            </div>
            <div class="p-4">
                <div class="flex justify-between items-start mb-2">
                    <h3 class="font-bold text-lg text-gray-800 dark:text-white">{{ $item->name }}</h3>
                    <div class="text-left">
                        <span class="font-bold text-primary block">{{ number_format($item->price, 2) }} د.ل</span>
                        @php
                            $costAnalysis = $item->getCostAnalysis();
                        @endphp
                        @if($costAnalysis['has_recipe'])
                            <span class="text-xs text-gray-500 dark:text-gray-400 block">التكلفة: {{ number_format($costAnalysis['ingredients_cost'], 2) }} د.ل</span>
                            <span class="text-xs {{ $costAnalysis['profit'] > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }} block">
                                الربح: {{ number_format($costAnalysis['profit'], 2) }} د.ل ({{ $costAnalysis['profit_margin'] }}%)
                            </span>
                        @else
                            <span class="text-xs text-orange-500 dark:text-orange-400 block">
                                <i class="fas fa-exclamation-triangle"></i> لا توجد وصفة
                            </span>
                        @endif
                    </div>
                </div>
                <p class="text-gray-600 dark:text-gray-300 text-sm mb-3">{{ $item->description ?? 'لا يوجد وصف' }}</p>

                <!-- التقييمات -->
                <div class="flex items-center text-sm mb-4">
                    @php
                        $averageRating = $item->reviews()->avg('rating') ?? 0;
                        $reviewsCount = $item->reviews()->count();
                    @endphp
                    <div class="text-yellow-400 flex">
                        @for($i = 1; $i <= 5; $i++)
                            @if($i <= floor($averageRating))
                                <i class="fas fa-star text-xs"></i>
                            @elseif($i - 0.5 <= $averageRating)
                                <i class="fas fa-star-half-alt text-xs"></i>
                            @else
                                <i class="far fa-star text-xs"></i>
                            @endif
                        @endfor
                    </div>
                    <span class="text-gray-600 dark:text-gray-400 mr-2 text-xs">
                        @if($averageRating > 0)
                            {{ number_format($averageRating, 1) }}
                            @if($reviewsCount > 0)
                                ({{ $reviewsCount }})
                            @endif
                        @else
                            <span class="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-2 py-1 rounded-full">لا توجد تقييمات</span>
                        @endif
                    </span>
                </div>

                <div class="flex justify-between items-center">
                    @php
                        $categoryLabels = [
                            'main' => 'الأطباق الرئيسية',
                            'appetizer' => 'المقبلات',
                            'dessert' => 'الحلويات',
                            'beverage' => 'المشروبات'
                        ];
                        $categoryColors = [
                            'main' => 'blue',
                            'appetizer' => 'green',
                            'dessert' => 'purple',
                            'beverage' => 'yellow'
                        ];
                        $color = $categoryColors[$item->category] ?? 'gray';
                    @endphp
                    <span class="text-xs bg-{{ $color }}-100 dark:bg-{{ $color }}-900/30 text-{{ $color }}-800 dark:text-{{ $color }}-300 px-2 py-1 rounded-full">
                        {{ $categoryLabels[$item->category] ?? $item->category }}
                    </span>
                    <div class="flex space-x-2 space-x-reverse">
                        <a href="{{ route('admin.menu.show', $item->item_id) }}" class="text-gray-500 hover:text-gray-700 transition-all">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{{ route('admin.menu.edit', $item->item_id) }}" class="text-blue-500 hover:text-blue-700 transition-all">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button onclick="deleteMenuItem({{ $item->item_id }})" class="text-red-500 hover:text-red-700 transition-all">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
    @else
        <div class="col-span-3 p-8 text-center">
            <div class="text-gray-500 dark:text-gray-400 mb-4">
                <i class="fas fa-utensils text-5xl mb-4"></i>
                @if(request('search') || request('category'))
                    <h3 class="text-xl font-bold">لا توجد نتائج مطابقة للبحث</h3>
                    <p class="mt-2">جرب تغيير معايير البحث أو التصفية</p>
                    <a href="{{ route('admin.menu') }}" class="inline-block mt-4 bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-md transition-all">
                        <i class="fas fa-undo ml-2"></i>
                        <span>إعادة ضبط البحث</span>
                    </a>
                @else
                    <h3 class="text-xl font-bold">لا توجد منتجات في القائمة</h3>
                    <p class="mt-2">قم بإضافة منتجات جديدة للقائمة من خلال زر "إضافة منتج"</p>
                    <a href="{{ route('admin.menu.create') }}" class="inline-block mt-4 bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md transition-all">
                        <i class="fas fa-plus ml-2"></i>
                        <span>إضافة منتج</span>
                    </a>
                @endif
            </div>
        </div>
    @endif
</div>

<!-- ترقيم الصفحات -->
@if($menuItems->hasPages())
<div class="mt-6 flex justify-center">
    <div class="pagination-wrapper">
        {{ $menuItems->appends(request()->query())->links('pagination.tailwind') }}
    </div>
</div>
@endif

<!-- نموذج تأكيد الحذف -->
<div id="deleteModal" class="fixed inset-0 bg-black/50 z-50 hidden flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-auto">
        <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-4">تأكيد الحذف</h3>
        <p class="text-gray-600 dark:text-gray-300 mb-6">هل أنت متأكد من رغبتك في حذف هذا العنصر من القائمة؟ هذا الإجراء لا يمكن التراجع عنه.</p>
        <div class="flex justify-end space-x-2 space-x-reverse">
            <button id="cancelDelete" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600">إلغاء</button>
            <form id="deleteForm" method="POST" action="">
                @csrf
                @method('DELETE')
                <button type="submit" class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">حذف</button>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    function deleteMenuItem(id) {
        const deleteModal = document.getElementById('deleteModal');
        const deleteForm = document.getElementById('deleteForm');
        const cancelDelete = document.getElementById('cancelDelete');

        deleteForm.action = "{{ route('admin.menu.delete', '') }}/" + id;
        deleteModal.classList.remove('hidden');

        cancelDelete.addEventListener('click', function() {
            deleteModal.classList.add('hidden');
        });
    }

    // تحسين تجربة البحث
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.querySelector('input[name="search"]');
        const searchForm = searchInput.closest('form');

        // إرسال النموذج عند الضغط على Enter
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchForm.submit();
            }
        });

        // مسح حقل البحث عند النقر على زر X
        const clearSearch = document.createElement('button');
        clearSearch.type = 'button';
        clearSearch.className = 'absolute right-3 top-2.5 text-gray-500 dark:text-gray-400 hidden';
        clearSearch.innerHTML = '<i class="fas fa-times"></i>';

        searchInput.parentNode.appendChild(clearSearch);

        // إظهار زر المسح عندما يكون هناك نص في حقل البحث
        function toggleClearButton() {
            if (searchInput.value.length > 0) {
                clearSearch.classList.remove('hidden');
            } else {
                clearSearch.classList.add('hidden');
            }
        }

        // تنفيذ الدالة عند تحميل الصفحة
        toggleClearButton();

        // تنفيذ الدالة عند كتابة نص
        searchInput.addEventListener('input', toggleClearButton);

        // مسح النص وإعادة التركيز على حقل البحث
        clearSearch.addEventListener('click', function() {
            searchInput.value = '';
            searchInput.focus();
            toggleClearButton();
        });
    });
</script>
@endsection
