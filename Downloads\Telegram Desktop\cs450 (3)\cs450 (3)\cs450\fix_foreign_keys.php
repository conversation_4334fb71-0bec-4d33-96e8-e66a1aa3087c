<?php
/**
 * أداة إصلاح المفاتيح الخارجية في ملفات الهجرة
 * تصحح جميع المراجع للمفاتيح الأساسية المخصصة
 */

echo "========================================\n";
echo "   إصلاح المفاتيح الخارجية\n";
echo "   Fix Foreign Keys Tool\n";
echo "========================================\n\n";

// قائمة الجداول ومفاتيحها الأساسية
$primaryKeys = [
    'users' => 'user_id',
    'ingredients' => 'ingredient_id',
    'inventory' => 'inventory_id',
    'menu_items' => 'item_id',
    'tables' => 'table_id',
    'reservations' => 'reservation_id',
    'orders' => 'order_id',
    'order_items' => 'order_item_id',
    'payments' => 'payment_id',
    'cart' => 'cart_id',
    'reviews' => 'review_id',
    'notifications' => 'notification_id',
    'expenses' => 'expense_id',
    'invoices' => 'invoice_id',
    'financial_reports' => 'report_id',
    'report_sources' => 'source_id',
    'offers' => 'offer_id',
    'categories' => 'category_id',
    'components' => 'component_id',
    'cart_items' => 'cart_item_id',
];

$migrationsPath = 'database/migrations/';
$files = glob($migrationsPath . '*.php');
$fixed = 0;
$errors = 0;

echo "فحص وإصلاح ملفات الهجرة...\n\n";

foreach ($files as $file) {
    $filename = basename($file);
    $content = file_get_contents($file);
    $originalContent = $content;
    $changed = false;
    
    echo "فحص: $filename\n";
    
    // البحث عن جميع استخدامات constrained
    if (preg_match_all('/->constrained\([\'"]([^\'\"]+)[\'\"]\)/', $content, $matches)) {
        foreach ($matches[1] as $index => $tableName) {
            if (isset($primaryKeys[$tableName])) {
                $oldPattern = $matches[0][$index];
                $newPattern = "->constrained('{$tableName}', '{$primaryKeys[$tableName]}')";
                
                if ($oldPattern !== $newPattern) {
                    $content = str_replace($oldPattern, $newPattern, $content);
                    $changed = true;
                    echo "  ✓ تم إصلاح: {$tableName} -> {$primaryKeys[$tableName]}\n";
                }
            }
        }
    }
    
    // حفظ الملف إذا تم تغييره
    if ($changed) {
        if (file_put_contents($file, $content)) {
            $fixed++;
            echo "  ✅ تم حفظ التغييرات\n";
        } else {
            $errors++;
            echo "  ❌ خطأ في حفظ الملف\n";
        }
    } else {
        echo "  ⚪ لا توجد تغييرات مطلوبة\n";
    }
    
    echo "\n";
}

echo "========================================\n";
echo "تقرير الإصلاح:\n";
echo "========================================\n";
echo "الملفات المُصلحة: $fixed\n";
echo "الأخطاء: $errors\n\n";

if ($errors == 0) {
    echo "🎉 تم إصلاح جميع المفاتيح الخارجية بنجاح!\n";
    echo "يمكنك الآن تشغيل: php artisan migrate:fresh\n";
} else {
    echo "⚠ هناك أخطاء تحتاج حل يدوي\n";
}

echo "\nتم الانتهاء من إصلاح المفاتيح الخارجية!\n";
?>
