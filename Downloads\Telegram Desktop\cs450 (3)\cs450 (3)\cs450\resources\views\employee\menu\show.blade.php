@extends('employee.layouts.app')

@section('content')
<div class="max-w-6xl mx-auto">
    <!-- العودة للقائمة -->
    <div class="mb-6">
        <a href="{{ route('employee.menu') }}" class="inline-flex items-center px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-300">
            <i class="fas fa-arrow-right ml-2"></i>
            العودة لقائمة الطعام
        </a>
    </div>

    <!-- تفاصيل الصنف -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
        <div class="md:flex">
            <!-- صورة الصنف -->
            <div class="md:w-1/2">
                @if($item->image_url)
                <img src="{{ $item->image_url }}" alt="{{ $item->name }}" class="w-full h-96 object-cover">
                @else
                <div class="w-full h-96 bg-gradient-to-br from-orange-400 to-red-500 flex items-center justify-center">
                    <i class="fas fa-utensils text-white text-6xl"></i>
                </div>
                @endif
            </div>

            <!-- معلومات الصنف -->
            <div class="md:w-1/2 p-8">
                <!-- العنوان والفئة -->
                <div class="flex justify-between items-start mb-4">
                    <h1 class="text-3xl font-bold text-gray-800 dark:text-white">{{ $item->name }}</h1>
                    <span class="px-3 py-1 text-sm rounded-full font-medium {{ $item->category == 'main' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : ($item->category == 'appetizer' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : ($item->category == 'dessert' ? 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200' : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200')) }}">
                        {{ $item->category == 'main' ? 'الأطباق الرئيسية' : ($item->category == 'appetizer' ? 'المقبلات' : ($item->category == 'dessert' ? 'الحلويات' : 'المشروبات')) }}
                    </span>
                </div>

                <!-- الوصف -->
                <p class="text-gray-600 dark:text-gray-400 text-lg leading-relaxed mb-6">{{ $item->description }}</p>

                <!-- السعر والحالة -->
                <div class="flex justify-between items-center mb-6">
                    <span class="text-4xl font-bold text-primary">{{ number_format($item->price, 2) }} ر.س</span>
                    <span class="px-4 py-2 rounded-full font-medium {{ $item->is_available ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' }}">
                        {{ $item->is_available ? 'متوفر' : 'غير متوفر' }}
                    </span>
                </div>

                <!-- معلومات إضافية -->
                <div class="grid grid-cols-2 gap-4 mb-6 text-sm">
                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                        <span class="text-gray-500 dark:text-gray-400">تاريخ الإضافة:</span>
                        <span class="block font-medium text-gray-800 dark:text-white">{{ $item->created_at->format('Y-m-d') }}</span>
                    </div>
                    @if($item->is_featured)
                    <div class="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg">
                        <span class="text-yellow-600 dark:text-yellow-400">
                            <i class="fas fa-star ml-1"></i>
                            صنف مميز
                        </span>
                    </div>
                    @endif
                </div>

                <!-- أزرار الإجراءات -->
                <div class="flex gap-3">
                    <button class="flex-1 add-to-order-btn bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-300 flex items-center justify-center {{ !$item->is_available ? 'opacity-50 cursor-not-allowed' : '' }}" 
                            data-id="{{ $item->item_id }}" 
                            data-name="{{ $item->name }}"
                            data-price="{{ $item->price }}"
                            {{ !$item->is_available ? 'disabled' : '' }}>
                        <i class="fas fa-plus ml-2"></i>
                        إضافة للطلب
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- المكونات (إذا كانت متوفرة) -->
    @if($item->recipe && $item->recipe->count() > 0)
    <div class="mt-8 bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4 flex items-center">
            <i class="fas fa-list-ul text-primary ml-2"></i>
            المكونات
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            @foreach($item->recipe as $recipe)
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h3 class="font-medium text-gray-800 dark:text-white">{{ $recipe->ingredient->name }}</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">الكمية: {{ $recipe->quantity }} {{ $recipe->ingredient->unit }}</p>
            </div>
            @endforeach
        </div>
    </div>
    @endif

    <!-- أصناف مشابهة -->
    @if($relatedItems->count() > 0)
    <div class="mt-8">
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-6 flex items-center">
            <i class="fas fa-utensils text-primary ml-2"></i>
            أصناف مشابهة
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            @foreach($relatedItems as $relatedItem)
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                @if($relatedItem->image_path)
                <img src="{{ $relatedItem->image_path }}" alt="{{ $relatedItem->name }}" class="w-full h-48 object-cover">
                @elseif($relatedItem->image)
                <img src="{{ asset('storage/' . $relatedItem->image) }}" alt="{{ $relatedItem->name }}" class="w-full h-48 object-cover">
                @else
                <div class="w-full h-48 bg-gradient-to-br from-orange-400 to-red-500 flex items-center justify-center">
                    <i class="fas fa-utensils text-white text-2xl"></i>
                </div>
                @endif
                <div class="p-4">
                    <h3 class="font-bold text-gray-800 dark:text-white mb-2">{{ $relatedItem->name }}</h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">{{ Str::limit($relatedItem->description, 60) }}</p>
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-bold text-primary">{{ number_format($relatedItem->price, 2) }} ر.س</span>
                        <a href="{{ route('employee.menu.show', $relatedItem->item_id) }}" class="text-blue-600 hover:text-blue-800 text-sm">
                            عرض التفاصيل
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة مستمع الحدث لزر إضافة للطلب
        const addButton = document.querySelector('.add-to-order-btn');
        if (addButton) {
            addButton.addEventListener('click', function() {
                const itemId = this.getAttribute('data-id');
                const itemName = this.getAttribute('data-name');
                const itemPrice = this.getAttribute('data-price');

                // إضافة العنصر للطلب
                addItemToOrder(itemId, itemName, itemPrice);
            });
        }
    });

    // دالة إضافة عنصر للطلب
    function addItemToOrder(itemId, itemName, itemPrice) {
        // إضافة العنصر مباشرة إلى الطلب بكمية 1 بدون ملاحظات
        addToCart(itemId, itemName, itemPrice, 1, '');

        // إظهار رسالة تأكيد
        showSuccessMessage('تمت إضافة ' + itemName + ' إلى الطلب');
    }

    // دالة إضافة العنصر إلى سلة التسوق
    function addToCart(itemId, itemName, itemPrice, quantity, notes) {
        console.log('إضافة العنصر إلى السلة:', itemId, itemName, itemPrice, quantity, notes);

        // استرجاع عناصر السلة الحالية من التخزين المحلي
        let cartItems = JSON.parse(localStorage.getItem('cartItems')) || [];

        // التحقق مما إذا كان العنصر موجودًا بالفعل في السلة
        const existingItemIndex = cartItems.findIndex(item => item.id === itemId);

        if (existingItemIndex !== -1) {
            // تحديث الكمية إذا كان العنصر موجودًا بالفعل
            cartItems[existingItemIndex].quantity += quantity;
            // تحديث الملاحظات إذا تم إدخالها
            if (notes) {
                cartItems[existingItemIndex].notes = notes;
            }
        } else {
            // إضافة عنصر جديد إلى السلة
            cartItems.push({
                id: itemId,
                name: itemName,
                price: parseFloat(itemPrice),
                quantity: quantity,
                notes: notes
            });
        }

        // حفظ السلة المحدثة في التخزين المحلي
        localStorage.setItem('cartItems', JSON.stringify(cartItems));

        // تحديث عداد السلة في الواجهة
        updateCartCount();
    }

    // دالة تحديث عداد السلة
    function updateCartCount() {
        const cartItems = JSON.parse(localStorage.getItem('cartItems')) || [];
        const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);
        
        const cartCountElement = document.getElementById('cartCount');
        if (cartCountElement) {
            cartCountElement.textContent = totalItems;
            cartCountElement.style.display = totalItems > 0 ? 'flex' : 'none';
        }
    }

    // دالة إظهار رسالة النجاح
    function showSuccessMessage(message) {
        // إنشاء عنصر الرسالة
        const messageElement = document.createElement('div');
        messageElement.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center';
        messageElement.innerHTML = '<i class="fas fa-check-circle ml-2 text-green-200 text-xl"></i><span class="font-bold">' + message + '</span>';

        // إضافة العنصر إلى الصفحة
        document.body.appendChild(messageElement);

        // إضافة تأثير ظهور
        setTimeout(function() {
            messageElement.classList.add('animate-bounce');
        }, 100);

        // إخفاء الرسالة بعد 3 ثوان
        setTimeout(function() {
            messageElement.classList.add('opacity-0', 'transition-opacity', 'duration-500');
            setTimeout(function() {
                document.body.removeChild(messageElement);
            }, 500);
        }, 3000);
    }
</script>
@endsection
