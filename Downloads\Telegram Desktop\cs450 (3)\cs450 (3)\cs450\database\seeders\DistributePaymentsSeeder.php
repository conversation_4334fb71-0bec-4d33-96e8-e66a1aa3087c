<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Payment;
use App\Models\User;

class DistributePaymentsSeeder extends Seeder
{
    public function run()
    {
        // الحصول على جميع الموظفين والإداريين
        $employees = User::whereIn('user_type', ['admin', 'employee'])->get();
        
        if ($employees->isEmpty()) {
            $this->command->error('لا يوجد موظفين في النظام');
            return;
        }
        
        // الحصول على جميع المدفوعات
        $payments = Payment::all();
        
        $this->command->info("عدد المدفوعات: {$payments->count()}");
        $this->command->info("عدد الموظفين: {$employees->count()}");
        
        // توزيع المدفوعات على الموظفين بشكل عشوائي
        foreach ($payments as $payment) {
            $randomEmployee = $employees->random();
            $payment->update(['employee_id' => $randomEmployee->user_id]);
        }
        
        $this->command->info("تم توزيع المدفوعات على الموظفين بنجاح");
        
        // عرض إحصائيات
        foreach ($employees as $employee) {
            $count = Payment::where('employee_id', $employee->user_id)->count();
            $this->command->info("{$employee->first_name} {$employee->last_name}: {$count} دفعة");
        }
    }
}
