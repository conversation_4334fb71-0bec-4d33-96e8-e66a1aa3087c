@extends('layouts.admin')

@section('title', 'تقرير الطلبات - لوحة تحكم Eat Hub')

@section('page-title', 'تقرير الطلبات')

@push('styles')
<!-- Chart.js CSS -->
<style>
    .chart-container { position: relative; height: 256px; }
    .table-container {
        max-height: 500px;
        overflow-y: auto;
        overflow-x: auto;
    }
    .table-container::-webkit-scrollbar { width: 6px; height: 6px; }
    .table-container::-webkit-scrollbar-track { background: #f1f1f1; }
    .table-container::-webkit-scrollbar-thumb { background: #c1c1c1; border-radius: 3px; }
    .table-container::-webkit-scrollbar-thumb:hover { background: #a8a8a8; }

    /* تحسين تنسيق الجدول */
    .orders-table {
        min-width: 100%;
        table-layout: auto;
    }
    .orders-table th,
    .orders-table td {
        text-align: right;
        vertical-align: middle;
    }
    .orders-table th:first-child,
    .orders-table td:first-child {
        width: 10%;
        min-width: 100px;
    }
    .orders-table th:nth-child(2),
    .orders-table td:nth-child(2) {
        width: 20%;
        min-width: 180px;
    }
    .orders-table th:nth-child(3),
    .orders-table td:nth-child(3) {
        width: 15%;
        min-width: 140px;
    }
    .orders-table th:nth-child(4),
    .orders-table td:nth-child(4) {
        width: 12%;
        min-width: 120px;
        text-align: center;
    }
    .orders-table th:nth-child(5),
    .orders-table td:nth-child(5) {
        width: 15%;
        min-width: 120px;
    }
    .orders-table th:nth-child(6),
    .orders-table td:nth-child(6) {
        width: 18%;
        min-width: 140px;
    }
    .orders-table th:nth-child(7),
    .orders-table td:nth-child(7) {
        width: 10%;
        min-width: 80px;
        text-align: center;
    }

    @media print {
        .print\\:hidden { display: none !important; }
        .chart-container { height: 200px; }
        .table-container { max-height: none; overflow: visible; }
    }

    @media (max-width: 768px) {
        .chart-container { height: 200px; }
        .table-container { max-height: 350px; }
        .orders-table th,
        .orders-table td {
            padding: 8px 4px;
            font-size: 12px;
        }
        .orders-table th:nth-child(2),
        .orders-table td:nth-child(2) {
            min-width: 150px;
        }
    }
</style>
@endpush

@section('content')
<div class="mb-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-1">🛒 تقرير الطلبات</h2>
            <p class="text-gray-600 dark:text-gray-400 text-sm">تفاصيل الطلبات والإحصائيات التفصيلية</p>
        </div>
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 sm:space-x-reverse">
            <a href="{{ route('admin.reports') }}" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 py-2 px-4 rounded-md transition-all">
                <i class="fas fa-arrow-right ml-1"></i>
                <span>العودة للتقارير</span>
            </a>
            <button onclick="exportOrdersToExcel()" class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-md transition-all">
                <i class="fas fa-download ml-1"></i>
                <span>تصدير Excel</span>
            </button>
            <button onclick="window.print()" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md transition-all print:hidden">
                <i class="fas fa-print ml-1"></i>
                <span>طباعة</span>
            </button>
        </div>
    </div>

    <!-- فلاتر التقرير -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
        <form action="{{ route('admin.reports.orders') }}" method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label for="period" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">📅 الفترة الزمنية</label>
                <select id="period" name="period" onchange="toggleCustomDates(this.value)" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    <option value="week" {{ request('period') == 'week' ? 'selected' : '' }}>📅 آخر أسبوع</option>
                    <option value="month" {{ request('period') == 'month' ? 'selected' : '' }}>📅 آخر شهر</option>
                    <option value="quarter" {{ request('period') == 'quarter' ? 'selected' : '' }}>📅 آخر 3 أشهر</option>
                    <option value="year" {{ request('period') == 'year' ? 'selected' : '' }}>📅 آخر سنة</option>
                    <option value="custom" {{ request('period') == 'custom' ? 'selected' : '' }}>🎯 فترة مخصصة</option>
                </select>
            </div>
            <div id="custom_dates" class="grid grid-cols-1 md:grid-cols-2 gap-4 md:col-span-2" style="{{ request('period') == 'custom' ? '' : 'display: none;' }}">
                <div>
                    <label for="custom_start" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">📅 من تاريخ</label>
                    <input type="date" id="custom_start" name="start_date" value="{{ $startDate }}" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                </div>
                <div>
                    <label for="custom_end" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">📅 إلى تاريخ</label>
                    <input type="date" id="custom_end" name="end_date" value="{{ $endDate }}" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                </div>
            </div>
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">📊 حالة الطلب</label>
                <select id="status" name="status" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    <option value="">جميع الحالات</option>
                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>⏳ قيد الانتظار</option>
                    <option value="preparing" {{ request('status') == 'preparing' ? 'selected' : '' }}>👨‍🍳 قيد التحضير</option>
                    <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>✅ مكتمل</option>
                    <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>❌ ملغي</option>
                </select>
            </div>
            <div class="flex items-end">
                <button type="submit" class="w-full bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-md transition-all">
                    <i class="fas fa-search ml-1"></i>
                    <span>تطبيق الفلتر</span>
                </button>
            </div>
        </form>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
        <!-- إجمالي الطلبات -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">🛒 إجمالي الطلبات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $totalOrders }}</p>
                </div>
                <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3">
                    <i class="fas fa-shopping-cart text-blue-500 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- الطلبات المكتملة -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">✅ الطلبات المكتملة</p>
                    <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $completedOrders }}</p>
                </div>
                <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3">
                    <i class="fas fa-check text-green-500 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- الطلبات قيد الانتظار -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">⏳ قيد الانتظار</p>
                    <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ $pendingOrders }}</p>
                </div>
                <div class="rounded-full bg-yellow-100 dark:bg-yellow-900/30 p-3">
                    <i class="fas fa-clock text-yellow-500 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- الطلبات الملغاة -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">❌ الطلبات الملغاة</p>
                    <p class="text-2xl font-bold text-red-600 dark:text-red-400">{{ $cancelledOrders }}</p>
                </div>
                <div class="rounded-full bg-red-100 dark:bg-red-900/30 p-3">
                    <i class="fas fa-times text-red-500 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- متوسط قيمة الطلب -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">💰 متوسط قيمة الطلب</p>
                    <p class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ number_format($averageOrderValue, 2) }} د.ل</p>
                </div>
                <div class="rounded-full bg-purple-100 dark:bg-purple-900/30 p-3">
                    <i class="fas fa-money-bill-wave text-purple-500 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- مخططات التقرير -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- مخطط الطلبات اليومية -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">📈 الطلبات اليومية</h3>
            </div>
            <div id="dailyOrdersChart" class="w-full h-80">
                <canvas id="dailyOrdersCanvas" height="320"></canvas>
            </div>
        </div>

        <!-- مخطط توزيع الطلبات -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">🥧 توزيع الطلبات حسب الحالة</h3>
            </div>
            <div id="statusOrdersChart" class="w-full h-80">
                <canvas id="statusOrdersCanvas" height="320"></canvas>
            </div>
        </div>
    </div>



    <!-- قسم الجدول مع الشريط الجانبي -->
    <div class="grid grid-cols-1 xl:grid-cols-4 gap-6 mb-6">
        <!-- قائمة الطلبات التفصيلية -->
        <div class="xl:col-span-3 bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">📋 قائمة الطلبات التفصيلية</h3>
                    <span class="text-sm text-gray-600 dark:text-gray-400">إجمالي: {{ count($orders) }} طلب</span>
                </div>
            </div>

        <div class="table-container">
            <table class="orders-table w-full">
                <thead class="bg-gray-50 dark:bg-gray-700 sticky top-0">
                    <tr>
                        <th class="px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">🆔 رقم الطلب</th>
                        <th class="px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">👤 العميل</th>
                        <th class="px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">📅 التاريخ والوقت</th>
                        <th class="px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">🍽️ عدد الأصناف</th>
                        <th class="px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">💰 المبلغ</th>
                        <th class="px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">📊 الحالة</th>
                        <th class="px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">🔧 الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($orders as $order)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900 dark:text-white">#{{ $order->order_id }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-gradient-to-r from-blue-400 to-blue-600 flex items-center justify-center">
                                            <i class="fas fa-user text-white text-sm"></i>
                                        </div>
                                    </div>
                                    <div class="mr-4">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                                            {{ $order->user ? $order->user->first_name . ' ' . $order->user->last_name : 'غير معروف' }}
                                        </div>
                                        @if($order->user && $order->user->phone)
                                            <div class="text-xs text-gray-500 dark:text-gray-400">📱 {{ $order->user->phone }}</div>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $order->created_at->format('Y-m-d') }}</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">🕐 {{ $order->created_at->format('H:i') }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                                    🍽️ {{ $order->items->count() }} صنف
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-bold text-green-600 dark:text-green-400">{{ number_format($order->total_amount, 2) }} د.ل</div>
                            </td>
                            <td class="px-6 py-4">
                                @if($order->status == 'completed')
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                        ✅ مكتمل
                                    </span>
                                @elseif($order->status == 'preparing')
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                                        👨‍🍳 قيد التحضير
                                    </span>
                                @elseif($order->status == 'pending')
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
                                        ⏳ قيد الانتظار
                                    </span>
                                @elseif($order->status == 'cancelled')
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
                                        ❌ ملغي
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                                        {{ $order->status }}
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex justify-center">
                                    <a href="{{ route('admin.orders.show', $order->order_id) }}"
                                       class="inline-flex items-center px-3 py-1 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 rounded-md transition-colors text-xs font-medium"
                                       title="عرض التفاصيل">
                                        <i class="fas fa-eye ml-1"></i>
                                        عرض
                                    </a>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="px-6 py-16 text-center">
                                <div class="text-gray-500 dark:text-gray-400">
                                    <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                                        <i class="fas fa-shopping-cart text-2xl text-gray-400"></i>
                                    </div>
                                    <p class="text-lg font-medium mb-2">لا توجد طلبات متاحة</p>
                                    <p class="text-sm">لم يتم العثور على طلبات في الفترة المحددة</p>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

            @if(method_exists($orders, 'links'))
            <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                {{ $orders->links('pagination.tailwind') }}
            </div>
            @endif
        </div>

        <!-- الشريط الجانبي -->
        <div class="xl:col-span-1 space-y-6">
            <!-- ملخص سريع -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h4 class="text-lg font-bold text-gray-800 dark:text-white mb-4">📊 ملخص سريع</h4>
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600 dark:text-gray-400">إجمالي المبيعات</span>
                        <span class="text-sm font-bold text-green-600 dark:text-green-400">{{ number_format($completedSalesTotal, 2) }} د.ل</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600 dark:text-gray-400">متوسط الطلب</span>
                        <span class="text-sm font-bold text-blue-600 dark:text-blue-400">{{ number_format($averageOrderValue, 2) }} د.ل</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600 dark:text-gray-400">متوسط الأصناف</span>
                        <span class="text-sm font-bold text-purple-600 dark:text-purple-400">{{ $avgItemsPerOrder }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600 dark:text-gray-400">معدل الإكمال</span>
                        <span class="text-sm font-bold text-green-600 dark:text-green-400">
                            {{ $totalOrders > 0 ? round(($completedOrders / $totalOrders) * 100, 1) : 0 }}%
                        </span>
                    </div>
                </div>
            </div>

            <!-- أعلى الأصناف -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h4 class="text-lg font-bold text-gray-800 dark:text-white mb-4">🏆 أعلى الأصناف</h4>
                <div class="space-y-3">
                    @forelse($topProducts->take(3) as $index => $prod)
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="inline-flex items-center justify-center w-6 h-6 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded-full text-xs font-bold mr-3">
                                    {{ $index + 1 }}
                                </span>
                                <div>
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ Str::limit($prod->name, 20) }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">{{ $prod->qty }} قطعة</div>
                                </div>
                            </div>
                            <div class="text-sm font-bold text-green-600 dark:text-green-400">{{ number_format($prod->total, 0) }} د.ل</div>
                        </div>
                    @empty
                        <p class="text-sm text-gray-500 dark:text-gray-400 text-center">لا توجد بيانات</p>
                    @endforelse
                </div>
            </div>

            <!-- أفضل العملاء -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h4 class="text-lg font-bold text-gray-800 dark:text-white mb-4">👑 أفضل العملاء</h4>
                <div class="space-y-3">
                    @forelse($topCustomers->take(3) as $index => $cust)
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="inline-flex items-center justify-center w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-xs font-bold mr-3">
                                    {{ $index + 1 }}
                                </span>
                                <div>
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ Str::limit($cust->name, 15) }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">{{ $cust->orders_count }} طلب</div>
                                </div>
                            </div>
                            <div class="text-sm font-bold text-green-600 dark:text-green-400">{{ number_format($cust->total_spent, 0) }} د.ل</div>
                        </div>
                    @empty
                        <p class="text-sm text-gray-500 dark:text-gray-400 text-center">لا توجد بيانات</p>
                    @endforelse
                </div>
            </div>

            <!-- أزرار سريعة -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h4 class="text-lg font-bold text-gray-800 dark:text-white mb-4">⚡ إجراءات سريعة</h4>
                <div class="space-y-3">
                    <button onclick="exportOrdersToExcel()" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md transition-colors text-sm font-medium">
                        <i class="fas fa-file-excel mr-2"></i>
                        تصدير Excel
                    </button>
                    <button onclick="window.print()" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors text-sm font-medium">
                        <i class="fas fa-print mr-2"></i>
                        طباعة التقرير
                    </button>
                    <a href="{{ route('admin.orders') }}" class="block w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md transition-colors text-sm font-medium text-center">
                        <i class="fas fa-list mr-2"></i>
                        إدارة الطلبات
                    </a>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- SheetJS for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="{{ asset('js/orders-charts.js') }}"></script>
<script>
    function toggleCustomDates(value) {
        const customDatesDiv = document.getElementById('custom_dates');
        if (value === 'custom') {
            customDatesDiv.style.display = 'grid';
        } else {
            customDatesDiv.style.display = 'none';
        }
    }

    // تمرير البيانات للمخططات
    window.ordersData = {
        daily: {!! json_encode($ordersByDay ?? []) !!},
        status: {!! json_encode($ordersByStatus ?? []) !!}
    };

    // تصدير البيانات إلى Excel
    function exportOrdersToExcel() {
        const ordersData = [];
        ordersData.push(['رقم الطلب','العميل','التاريخ','الوقت','عدد الأصناف','المبلغ (د.ل)','الحالة']);
        const exportRows = {!! json_encode($ordersExportData ?? []) !!};
        exportRows.forEach(function (order) {
            ordersData.push([
                order.order_id,
                order.customer,
                order.date,
                order.time,
                order.items_count,
                order.amount,
                order.status,
            ]);
        });
        const ws = XLSX.utils.aoa_to_sheet(ordersData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'تقرير الطلبات');
        const fileName = `تقرير_الطلبات_${new Date().toISOString().split('T')[0]}.xlsx`;
        XLSX.writeFile(wb, fileName);
    }

    // تهيئة المخططات عند التحميل (إذا الملف متوفر)
    window.addEventListener('load', function() {
        setTimeout(function() {
            if (typeof initOrdersChartsChartJS === 'function') {
                initOrdersChartsChartJS();
            }
        }, 300);
    });
</script>
@endsection
