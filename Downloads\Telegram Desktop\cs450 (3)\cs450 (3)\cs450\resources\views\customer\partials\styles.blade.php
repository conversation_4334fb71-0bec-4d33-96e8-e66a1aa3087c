<style>
    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');

    body {
        font-family: 'Cairo', sans-serif;
    }

    /* تنسيق السكرولبار */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    .dark ::-webkit-scrollbar-track {
        background: #2d3748;
    }

    ::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #FF6B35;
    }

    /* تأثيرات الانتقال */
    .nav-transition {
        transition: all 0.3s ease-in-out;
    }

    /* تأثيرات الحركة */
    .fade-in {
        animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes slideLeft {
        from { opacity: 0; transform: translateX(30px); }
        to { opacity: 1; transform: translateX(0); }
    }

    .hero-image-mask {
        mask-image: linear-gradient(to left, transparent 5%, black 25%);
        -webkit-mask-image: linear-gradient(to left, transparent 5%, black 25%);
    }

    .splash-gradient {
        background: radial-gradient(circle at center, rgba(255, 107, 53, 0.15) 0%, transparent 70%);
    }

    /* تنسيق البطاقات */
    .card-hover {
        transition: all 0.3s ease;
    }

    .card-hover:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .dark .card-hover:hover {
        box-shadow: 0 10px 25px -5px rgba(255, 107, 53, 0.2), 0 10px 10px -5px rgba(255, 107, 53, 0.1);
    }

    /* تنسيق العناصر في القائمة */
    .menu-item-card {
        transition: all 0.3s ease;
    }

    .menu-item-card:hover {
        transform: translateY(-5px);
    }

    .menu-item-image {
        transition: transform 0.5s ease;
    }

    .menu-item-card:hover .menu-item-image {
        transform: scale(1.05);
    }

    /* تنسيق النجوم للتقييم */
    .star-rating {
        transition: all 0.2s ease;
    }

    .star-rating:hover {
        transform: scale(1.1);
    }

    /* آثر إضافي للأزرار */
    .btn-hover-effect {
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .btn-hover-effect:hover {
        transform: translateY(-2px);
    }

    .btn-hover-effect:after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.6s, height 0.6s;
    }

    .btn-hover-effect:active:after {
        width: 300px;
        height: 300px;
        opacity: 0;
    }

    /* تنميط خاص للقائمة العلوية عند التمرير */
    .navbar-fixed {
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .dark .navbar-fixed {
        background-color: rgba(31, 41, 55, 0.8);
    }

    /* تنميط النص المتوهج */
    .text-glow {
        text-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
    }

    .dark .text-glow {
        text-shadow: 0 0 15px rgba(255, 107, 53, 0.7);
    }

    /* تنميط الصور المستديرة */
    .rounded-image-border {
        border: 3px solid #FF6B35;
        box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.3);
    }

    /* تنميط أزرار التبديل بين الصفحات */
    .tab-button {
        position: relative;
    }

    .tab-button::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 3px;
        background-color: #FF6B35;
        transition: width 0.3s;
    }

    .tab-button:hover::after,
    .tab-button.active::after {
        width: 100%;
    }

    /* تحسينات الوضع المظلم */
    .dark {
        color-scheme: dark;
    }

    .dark body {
        background-color: #111827 !important;
        color: #f9fafb !important;
    }

    /* تأثيرات زر الوضع المظلم */
    #darkModeToggle {
        transition: all 0.3s ease;
    }

    #darkModeToggle:hover {
        transform: scale(1.1);
    }

    #darkModeToggle:active {
        transform: scale(0.95);
    }

    /* تأكد من ظهور الأيقونات بشكل صحيح */
    #darkModeIcon {
        transition: all 0.3s ease;
    }

    .dark #darkModeIcon {
        color: #fbbf24 !important;
    }
</style>
