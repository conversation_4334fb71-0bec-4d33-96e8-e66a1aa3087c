<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Exception;

class DatabaseSeeder extends Seeder
{
    /**
     * تشغيل بذور قاعدة البيانات بالترتيب الصحيح
     */
    public function run()
    {
        $this->command->info('🌱 بدء تشغيل بذور قاعدة البيانات...');

        try {
            // تعطيل فحص المفاتيح الخارجية مؤقتاً
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');

            // 1. إنشاء الأدوار والصلاحيات الأساسية
            $this->seedRolesAndPermissions();

            // 2. إنشاء المستخدمين الأساسيين
            $this->seedBasicUsers();

            // 3. إنشاء البيانات الأساسية للنظام
            $this->seedBasicData();

            // 4. إنشاء البيانات التجريبية (اختيارية)
            $this->seedTestData();

            // إعادة تفعيل فحص المفاتيح الخارجية
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');

            $this->command->info('✅ تم تشغيل جميع البذور بنجاح!');

        } catch (Exception $e) {
            // إعادة تفعيل فحص المفاتيح الخارجية في حالة الخطأ
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');

            $this->command->error('❌ خطأ في تشغيل البذور: ' . $e->getMessage());
            Log::error('Database seeding failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إنشاء الأدوار والصلاحيات الأساسية
     */
    private function seedRolesAndPermissions()
    {
        $this->command->info('👥 إنشاء الأدوار والصلاحيات...');

        try {
            // إنشاء الأدوار الأساسية
            Role::firstOrCreate(['name' => 'admin']);
            Role::firstOrCreate(['name' => 'employee']);
            Role::firstOrCreate(['name' => 'customer']);

            $this->command->info('✅ تم إنشاء الأدوار بنجاح');
        } catch (Exception $e) {
            $this->command->warn('⚠️  تحذير في إنشاء الأدوار: ' . $e->getMessage());
        }
    }

    /**
     * إنشاء المستخدمين الأساسيين
     */
    private function seedBasicUsers()
    {
        $this->command->info('👤 إنشاء المستخدمين الأساسيين...');

        try {
            // إنشاء مستخدم مدير
            $admin = User::firstOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'first_name' => 'عبدو',
                    'last_name' => 'الأزرق',
                    'password' => bcrypt('A178a2002'),
                    'phone' => '0919676123',
                    'user_type' => 'admin',
                    'is_active' => true
                ]
            );

            if (class_exists('Spatie\Permission\Models\Role')) {
                $admin->assignRole('admin');
            }

            $this->command->info('✅ تم إنشاء المستخدم المدير: <EMAIL>');
        } catch (Exception $e) {
            $this->command->warn('⚠️  تحذير في إنشاء المستخدمين: ' . $e->getMessage());
        }
    }

    /**
     * إنشاء البيانات الأساسية للنظام
     */
    private function seedBasicData()
    {
        $this->command->info('📊 إنشاء البيانات الأساسية...');

        $basicSeeders = [
            TableSeeder::class => 'الطاولات',
            IngredientSeeder::class => 'المكونات',
            MenuItemSeeder::class => 'قائمة الطعام',
        ];

        foreach ($basicSeeders as $seederClass => $description) {
            try {
                if (class_exists($seederClass)) {
                    $this->call($seederClass);
                    $this->command->info("✅ تم إنشاء $description");
                } else {
                    $this->command->warn("⚠️  $seederClass غير موجود");
                }
            } catch (Exception $e) {
                $this->command->warn("⚠️  تحذير في إنشاء $description: " . $e->getMessage());
            }
        }
    }

    /**
     * إنشاء البيانات التجريبية (اختيارية)
     */
    private function seedTestData()
    {
        $this->command->info('🧪 إنشاء البيانات التجريبية...');

        $testSeeders = [
            ExpenseSeeder::class => 'المصروفات التجريبية',
            PaymentSeeder::class => 'المدفوعات التجريبية',
            NotificationSeeder::class => 'الإشعارات التجريبية',
            OfferSeeder::class => 'العروض التجريبية',
            TestUserSeeder::class => 'المستخدمين التجريبيين',
            ReviewSeeder::class => 'التقييمات التجريبية',
        ];

        foreach ($testSeeders as $seederClass => $description) {
            try {
                if (class_exists($seederClass)) {
                    $this->call($seederClass);
                    $this->command->info("✅ تم إنشاء $description");
                } else {
                    $this->command->warn("⚠️  $seederClass غير موجود - تم التخطي");
                }
            } catch (Exception $e) {
                $this->command->warn("⚠️  تحذير في إنشاء $description: " . $e->getMessage());
                // لا نوقف العملية للبيانات التجريبية
                continue;
            }
        }
    }
}