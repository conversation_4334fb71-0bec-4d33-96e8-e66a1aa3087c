@extends('layouts.admin')

@section('title', 'تعديل المخزون')

@section('content')
<div class="mb-6">
    <h2 class="text-2xl font-bold text-gray-800 dark:text-white">تعديل المخزون</h2>
    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">تعديل معلومات المخزون</p>
</div>

<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
    <div class="p-6">
        <form action="{{ route('admin.inventory.update', $inventory->inventory_id) }}" method="POST">
            @csrf
            @method('PUT')
            
            <div class="mb-6">
                <label for="ingredient_search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المكون</label>
                <div class="relative">
                    <!-- حقل البحث -->
                    <input type="text"
                           id="ingredient_search"
                           placeholder="ابحث عن المكون..."
                           class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary transition-all"
                           autocomplete="off">

                    <!-- الحقل المخفي للقيمة -->
                    <input type="hidden" id="ingredient_id" name="ingredient_id" value="{{ old('ingredient_id', $inventory->ingredient_id) }}">

                    <!-- قائمة النتائج -->
                    <div id="ingredient_dropdown" class="absolute z-50 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-y-auto hidden">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>

                    <!-- عرض المكون المختار -->
                    <div id="selected_ingredient" class="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md {{ old('ingredient_id', $inventory->ingredient_id) ? '' : 'hidden' }}">
                        <div class="flex items-center justify-between">
                            <div>
                                <span class="text-sm font-medium text-blue-800 dark:text-blue-200" id="selected_ingredient_name">
                                    @if(old('ingredient_id', $inventory->ingredient_id))
                                        @php
                                            $selected = $ingredients->firstWhere('ingredient_id', old('ingredient_id', $inventory->ingredient_id));
                                        @endphp
                                        {{ $selected ? $selected->name . ' (' . $selected->unit . ')' : '' }}
                                    @endif
                                </span>
                            </div>
                            <button type="button" onclick="clearSelectedIngredient()" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
                @error('ingredient_id')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                @enderror
            </div>
            
            <div class="mb-6">
                <label for="quantity" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الكمية</label>
                <div class="flex">
                    <input type="number" id="quantity" name="quantity" value="{{ old('quantity', $inventory->quantity) }}" step="0.01" min="0" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                    <span id="unit-display" class="inline-flex items-center px-3 py-2 text-gray-500 bg-gray-100 dark:bg-gray-600 dark:text-gray-300 border border-r-0 border-gray-300 dark:border-gray-600 rounded-l-md">
                        {{ $inventory->ingredient->unit }}
                    </span>
                </div>
                @error('quantity')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                @enderror
            </div>
            
            <div class="mb-6">
                <label for="cost_per_unit" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التكلفة للوحدة</label>
                <div class="flex">
                    <input type="number" id="cost_per_unit" name="cost_per_unit" value="{{ old('cost_per_unit', $inventory->cost_per_unit) }}" step="0.01" min="0.01" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                    <span class="inline-flex items-center px-3 py-2 text-gray-500 bg-gray-100 dark:bg-gray-600 dark:text-gray-300 border border-r-0 border-gray-300 dark:border-gray-600 rounded-l-md">
                        دينار
                    </span>
                </div>
                @error('cost_per_unit')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                @enderror
            </div>
            
            <div class="mb-6">
                <label for="expiry_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ الانتهاء (اختياري)</label>
                <input type="date" id="expiry_date" name="expiry_date" value="{{ old('expiry_date', $inventory->expiry_date ? $inventory->expiry_date->format('Y-m-d') : '') }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                @error('expiry_date')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                @enderror
            </div>
            
            <div class="flex justify-end space-x-2 space-x-reverse">
                <a href="{{ route('admin.inventory') }}" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">إلغاء</a>
                <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-all">تحديث المخزون</button>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // بيانات المكونات
        const ingredients = @json($ingredients->map(function($ingredient) {
            return [
                'id' => $ingredient->ingredient_id,
                'name' => $ingredient->name,
                'unit' => $ingredient->unit
            ];
        }));

        // العناصر
        const searchInput = document.getElementById('ingredient_search');
        const hiddenInput = document.getElementById('ingredient_id');
        const dropdown = document.getElementById('ingredient_dropdown');
        const selectedDiv = document.getElementById('selected_ingredient');
        const selectedNameSpan = document.getElementById('selected_ingredient_name');
        const unitDisplay = document.getElementById('unit-display');

        let selectedIndex = -1;

        // البحث في المكونات
        function searchIngredients(query) {
            if (!query.trim()) {
                return ingredients;
            }

            return ingredients.filter(ingredient =>
                ingredient.name.toLowerCase().includes(query.toLowerCase())
            );
        }

        // عرض النتائج
        function displayResults(results) {
            dropdown.innerHTML = '';

            if (results.length === 0) {
                dropdown.innerHTML = '<div class="p-3 text-gray-500 dark:text-gray-400 text-center">لا توجد نتائج</div>';
                dropdown.classList.remove('hidden');
                return;
            }

            results.forEach((ingredient, index) => {
                const div = document.createElement('div');
                div.className = 'p-3 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer border-b border-gray-200 dark:border-gray-600 last:border-b-0';
                div.innerHTML = `
                    <div class="font-medium text-gray-800 dark:text-white">${ingredient.name}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">الوحدة: ${ingredient.unit}</div>
                `;

                div.addEventListener('click', () => selectIngredient(ingredient));
                dropdown.appendChild(div);
            });

            dropdown.classList.remove('hidden');
            selectedIndex = -1;
        }

        // اختيار مكون
        function selectIngredient(ingredient) {
            hiddenInput.value = ingredient.id;
            searchInput.value = '';
            selectedNameSpan.textContent = `${ingredient.name} (${ingredient.unit})`;
            selectedDiv.classList.remove('hidden');
            dropdown.classList.add('hidden');

            // تحديث وحدة القياس
            updateUnit(ingredient.unit);

            console.log('تم اختيار المكون:', ingredient.name);
        }

        // مسح الاختيار
        window.clearSelectedIngredient = function() {
            hiddenInput.value = '';
            searchInput.value = '';
            selectedDiv.classList.add('hidden');
            dropdown.classList.add('hidden');
            if (unitDisplay) unitDisplay.textContent = 'وحدة';
            searchInput.focus();
        }

        // تحديث وحدة القياس
        function updateUnit(unit = null) {
            if (!unitDisplay) return;

            if (unit) {
                unitDisplay.textContent = unit;
            } else {
                const selectedId = hiddenInput.value;
                if (selectedId) {
                    const ingredient = ingredients.find(ing => ing.id == selectedId);
                    if (ingredient) {
                        unitDisplay.textContent = ingredient.unit;
                    }
                } else {
                    unitDisplay.textContent = 'وحدة';
                }
            }
        }

        // أحداث البحث
        searchInput.addEventListener('input', function() {
            const query = this.value;
            const results = searchIngredients(query);
            displayResults(results);
        });

        // إخفاء القائمة عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
                dropdown.classList.add('hidden');
            }
        });

        // التنقل بالكيبورد
        searchInput.addEventListener('keydown', function(e) {
            const items = dropdown.querySelectorAll('div[class*="cursor-pointer"]');

            if (e.key === 'ArrowDown') {
                e.preventDefault();
                selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
                updateSelection(items);
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                selectedIndex = Math.max(selectedIndex - 1, -1);
                updateSelection(items);
            } else if (e.key === 'Enter') {
                e.preventDefault();
                if (selectedIndex >= 0 && items[selectedIndex]) {
                    items[selectedIndex].click();
                }
            } else if (e.key === 'Escape') {
                dropdown.classList.add('hidden');
                selectedIndex = -1;
            }
        });

        function updateSelection(items) {
            items.forEach((item, index) => {
                if (index === selectedIndex) {
                    item.classList.add('bg-blue-100', 'dark:bg-blue-900');
                } else {
                    item.classList.remove('bg-blue-100', 'dark:bg-blue-900');
                }
            });
        }

        // إظهار القائمة عند التركيز
        searchInput.addEventListener('focus', function() {
            if (this.value.trim() || ingredients.length > 0) {
                const results = searchIngredients(this.value);
                displayResults(results);
            }
        });

        // تحديث الوحدة عند تحميل الصفحة
        updateUnit();
    });
</script>
@endsection
