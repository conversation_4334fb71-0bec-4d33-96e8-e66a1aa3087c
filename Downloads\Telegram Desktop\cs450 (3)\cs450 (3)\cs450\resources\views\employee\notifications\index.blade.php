@extends('employee.layouts.app')

@section('title', 'الإشعارات')

@section('content')
<div id="notifications-page" class="page fade-in">
    <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">الإشعارات</h2>
            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <a href="{{ route('employee.dashboard') }}" class="hover:text-primary">لوحة التحكم</a>
                <i class="fas fa-chevron-left mx-2 text-xs"></i>
                <span>الإشعارات</span>
            </div>
        </div>
        <div class="mt-4 md:mt-0">
            <form action="{{ route('employee.notifications.mark-all-read') }}" method="POST">
                @csrf
                <button type="submit" class="inline-flex items-center px-4 py-2 bg-primary hover:bg-primary/90 text-white rounded-md transition-colors">
                    <i class="fas fa-check-double ml-2"></i>
                    <span>تحديد الكل كمقروء</span>
                </button>
            </form>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="flex border-b border-gray-200 dark:border-gray-700">
            <a href="{{ route('employee.notifications') }}" class="px-6 py-3 {{ request()->routeIs('employee.notifications') && !request()->query('filter') ? 'border-b-2 border-primary text-primary font-medium' : 'text-gray-600 dark:text-gray-300 font-medium hover:text-primary' }}">
                جميع الإشعارات
            </a>
            <a href="{{ route('employee.notifications', ['filter' => 'unread']) }}" class="px-6 py-3 {{ request()->query('filter') == 'unread' ? 'border-b-2 border-primary text-primary font-medium' : 'text-gray-600 dark:text-gray-300 font-medium hover:text-primary' }}">
                غير مقروءة
            </a>
            <a href="{{ route('employee.notifications', ['filter' => 'read']) }}" class="px-6 py-3 {{ request()->query('filter') == 'read' ? 'border-b-2 border-primary text-primary font-medium' : 'text-gray-600 dark:text-gray-300 font-medium hover:text-primary' }}">
                مقروءة
            </a>
        </div>

        <div class="divide-y divide-gray-200 dark:divide-gray-700">
            @forelse($notifications as $notification)
                <div class="p-4 hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors {{ $notification->is_read ? '' : 'bg-blue-50 dark:bg-blue-900/10' }}">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 ml-3">
                            @if($notification->type == 'order')
                                <div class="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-500 dark:text-blue-300">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                            @elseif($notification->type == 'reservation')
                                <div class="h-10 w-10 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center text-green-500 dark:text-green-300">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                            @elseif($notification->type == 'inventory')
                                <div class="h-10 w-10 rounded-full bg-yellow-100 dark:bg-yellow-900/30 flex items-center justify-center text-yellow-500 dark:text-yellow-300">
                                    <i class="fas fa-box"></i>
                                </div>
                            @elseif($notification->type == 'system')
                                <div class="h-10 w-10 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center text-purple-500 dark:text-purple-300">
                                    <i class="fas fa-cog"></i>
                                </div>
                            @elseif($notification->type == 'contact')
                                <div class="h-10 w-10 rounded-full bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center text-orange-500 dark:text-orange-300">
                                    <i class="fas fa-envelope"></i>
                                </div>
                            @else
                                <div class="h-10 w-10 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-300">
                                    <i class="fas fa-bell"></i>
                                </div>
                            @endif
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <h3 class="text-sm font-medium text-gray-800 dark:text-white">
                                    {{ $notification->title }}
                                </h3>
                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                    {{ $notification->created_at->diffForHumans() }}
                                </span>
                            </div>
                            <p class="mt-1 text-sm text-gray-600 dark:text-gray-300">
                                {{ $notification->message }}
                            </p>
                            <div class="mt-2 flex items-center justify-between">
                                @if($notification->action_url)
                                    <a href="{{ $notification->action_url }}" class="text-xs text-primary hover:underline">
                                        {{ $notification->action_text ?: 'عرض التفاصيل' }}
                                    </a>
                                @else
                                    <span></span>
                                @endif

                                <div class="flex items-center space-x-2 space-x-reverse">
                                    @if(!$notification->is_read)
                                        <form action="{{ route('employee.notifications.mark-read', $notification->notification_id) }}" method="POST" class="inline">
                                            @csrf
                                            <button type="submit" class="text-xs text-gray-500 dark:text-gray-400 hover:text-primary">
                                                تحديد كمقروء
                                            </button>
                                        </form>
                                    @endif
                                    <form action="{{ route('employee.notifications.delete', $notification->notification_id) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-xs text-red-500 hover:text-red-700">
                                            حذف
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="p-8 text-center">
                    <div class="inline-flex items-center justify-center h-16 w-16 rounded-full bg-gray-100 dark:bg-gray-700 mb-4">
                        <i class="fas fa-bell-slash text-2xl text-gray-500 dark:text-gray-400"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-2">لا توجد إشعارات</h3>
                    <p class="text-gray-500 dark:text-gray-400">
                        ستظهر هنا جميع الإشعارات الخاصة بك عندما تصل.
                    </p>
                </div>
            @endforelse
        </div>

        @if($notifications->count() > 0)
            <div class="px-4 py-3 border-t border-gray-200 dark:border-gray-700">
                {{ $notifications->links() }}
            </div>
        @endif
    </div>
</div>
@endsection
