# نظام الإعدادات الشامل - Eat Hub

## 📋 نظرة عامة

تم تطوير نظام إعدادات شامل ومركزي يؤثر على جميع أجزاء النظام (الإدارة، الموظفين، العملاء). يمكن للمدير الآن تغيير الإعدادات من مكان واحد وستنعكس التغييرات على جميع أجزاء النظام فوراً.

## 🚀 الميزات الجديدة

### ✅ **نظام إعدادات مركزي**
- إعدادات موحدة لجميع أجزاء النظام
- تحديث فوري للتغييرات
- تخزين مؤقت ذكي للأداء

### ✅ **إعدادات الضرائب والرسوم**
- نسبة ضريبة القيمة المضافة قابلة للتخصيص
- رسوم الخدمة المرنة
- خيار تضمين الضريبة في السعر

### ✅ **إعدادات المطعم**
- اسم المطعم
- معلومات الاتصال (هاتف، إيميل، عنوان)
- المنطقة الزمنية وتنسيق التاريخ

### ✅ **إعدادات النظام**
- العملة المستخدمة
- اللغة الافتراضية
- إعدادات الإشعارات

## 🛠️ المكونات التقنية

### 1. **SettingsHelper Class**
```php
// الحصول على نسبة الضريبة
$taxRate = SettingsHelper::getTaxRate();

// حساب الضريبة لمبلغ معين
$tax = SettingsHelper::calculateTax($amount);

// تنسيق المبلغ مع العملة
$formatted = SettingsHelper::formatCurrency($amount);
```

### 2. **ShareSettings Middleware**
- يمرر الإعدادات العامة لجميع الـ views
- متاح في جميع الصفحات تلقائياً
- يوفر helper functions للاستخدام في الواجهات

### 3. **Settings Model**
- إدارة الإعدادات في قاعدة البيانات
- تجميع الإعدادات حسب المجموعات
- دوال مساعدة للقراءة والكتابة

## 📊 الإعدادات المتاحة

### **إعدادات عامة (general)**
- `restaurant_name`: اسم المطعم
- `restaurant_phone`: هاتف المطعم
- `restaurant_email`: إيميل المطعم
- `restaurant_address`: عنوان المطعم

### **إعدادات الضرائب (tax)**
- `tax_rate`: نسبة ضريبة القيمة المضافة (%)
- `service_fee`: رسوم الخدمة (%)
- `include_tax_in_price`: تضمين الضريبة في السعر (نعم/لا)

### **إعدادات النظام (system)**
- `currency`: العملة المستخدمة
- `timezone`: المنطقة الزمنية
- `date_format`: تنسيق التاريخ

### **إعدادات التوصيل (delivery)**
- `delivery_fee`: رسوم التوصيل
- `free_delivery_minimum`: الحد الأدنى للتوصيل المجاني
- `max_delivery_distance`: أقصى مسافة توصيل

### **إعدادات الطلبات (orders)**
- `min_order_amount`: الحد الأدنى لقيمة الطلب
- `max_order_items`: الحد الأقصى لعدد العناصر
- `order_preparation_time`: وقت تحضير الطلب

## 🔧 كيفية الاستخدام

### **في الـ Controllers:**
```php
use App\Helpers\SettingsHelper;

// الحصول على إعداد معين
$taxRate = SettingsHelper::getTaxRate();

// حساب المجموع مع الضريبة
$total = SettingsHelper::calculateTotal($subtotal);

// تحديث إعداد
SettingsHelper::set('tax_rate', 20, 'tax');
```

### **في الـ Views:**
```blade
<!-- عرض نسبة الضريبة -->
الضريبة ({{ $taxRate }}%): {{ $settingsHelper->formatCurrency($tax) }}

<!-- حساب الضريبة -->
@php
$tax = $settingsHelper->calculateTax($subtotal);
@endphp
```

### **في JavaScript:**
```javascript
// نسبة الضريبة من الإعدادات
const taxRate = {!! json_encode($taxRate ?? 15) !!} / 100;
const tax = subtotal * taxRate;
```

## 📱 التأثير على أجزاء النظام

### **لوحة تحكم الإدارة**
- صفحة إعدادات شاملة
- تحديث فوري للتغييرات
- تنظيف الكاش التلقائي

### **واجهة الموظفين**
- حسابات الضريبة الديناميكية
- عرض معلومات المطعم المحدثة
- فواتير بالإعدادات الجديدة

### **واجهة العملاء**
- أسعار محدثة تلقائياً
- حسابات دقيقة للضريبة
- معلومات المطعم الصحيحة

## 🔄 التحديثات المطبقة

### **Controllers المحدثة:**
- ✅ `AdminController` - إدارة الإعدادات
- ✅ `OrderController` - حسابات الضريبة
- ✅ `PaymentController` - الفواتير والمدفوعات

### **Views المحدثة:**
- ✅ `admin/settings.blade.php` - صفحة الإعدادات
- ✅ `customer/orders/show.blade.php` - عرض الطلب
- ✅ `customer/orders/invoice.blade.php` - الفاتورة
- ✅ `customer/cart/index.blade.php` - السلة
- ✅ `customer/cart/checkout.blade.php` - الدفع
- ✅ `employee/orders/create.blade.php` - إنشاء طلب

### **Commands المحدثة:**
- ✅ `CreateMissingInvoices` - إنشاء الفواتير

## 🎯 الفوائد

### **للمدير:**
- تحكم كامل في إعدادات النظام
- تحديث فوري للتغييرات
- واجهة سهلة الاستخدام

### **للموظفين:**
- حسابات دقيقة تلقائياً
- معلومات محدثة دائماً
- عمل أكثر كفاءة

### **للعملاء:**
- أسعار شفافة ودقيقة
- معلومات صحيحة ومحدثة
- تجربة أفضل

## 🔒 الأمان والأداء

### **التخزين المؤقت:**
- كاش ذكي للإعدادات (3600 ثانية)
- تنظيف تلقائي عند التحديث
- أداء محسن للاستعلامات

### **الأمان:**
- التحقق من صحة البيانات
- صلاحيات محددة للتعديل
- حماية من القيم الخاطئة

## 📈 التطوير المستقبلي

### **ميزات مخططة:**
- إعدادات متقدمة للإشعارات
- تخصيص المظهر والألوان
- إعدادات متعددة اللغات
- نسخ احتياطية للإعدادات

### **تحسينات تقنية:**
- API للإعدادات
- إعدادات مشروطة
- تاريخ التغييرات
- استيراد/تصدير الإعدادات

## 🚀 كيفية التشغيل

1. **تشغيل الـ Seeder:**
```bash
php artisan db:seed --class=SettingsSeeder
```

2. **مسح الكاش:**
```bash
php artisan view:clear
php artisan cache:clear
```

3. **إعادة تحميل Composer:**
```bash
composer dump-autoload
```

## 📞 الدعم

للمساعدة أو الاستفسارات حول نظام الإعدادات الجديد، يرجى التواصل مع فريق التطوير.

---

**تم التطوير بواسطة:** فريق Eat Hub  
**التاريخ:** 2025-01-20  
**الإصدار:** 2.0.0
