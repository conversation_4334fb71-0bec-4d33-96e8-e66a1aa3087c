@echo off
chcp 65001 >nul
echo 🚀 الإعداد السريع لنظام إدارة المطعم
echo ===================================
echo.

REM التحقق من وجود PHP
php --version >nul 2>&1
if errorlevel 1 (
    echo ❌ PHP غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت PHP 8.1 أو أحدث
    pause
    exit /b 1
)

REM التحقق من وجود ملف .env
if not exist ".env" (
    echo 📋 نسخ ملف البيئة...
    copy .env.example .env >nul
    echo ✅ تم نسخ ملف .env
    echo.
    echo ⚠️  يرجى تعديل إعدادات قاعدة البيانات في ملف .env
    echo ثم تشغيل هذا الملف مرة أخرى
    pause
    exit /b 0
)

echo 🔧 تشغيل سكريبت الإعداد التلقائي...
php setup.php

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في الإعداد
    echo 💡 جرب تشغيل: php reset-database.php
    pause
    exit /b 1
)

echo.
echo 🔍 فحص صحة النظام...
php check-system.php

echo.
echo 🎉 تم الانتهاء من الإعداد بنجاح!
echo.
echo 📋 معلومات تسجيل الدخول:
echo    البريد الإلكتروني: <EMAIL>
echo    كلمة المرور: A178a2002
echo.
echo 🌐 لتشغيل الخادم:
echo    php artisan serve
echo.
echo 💡 ملفات مفيدة:
echo    - php check-system.php (فحص النظام)
echo    - php reset-database.php (إعادة تعيين قاعدة البيانات)
echo    - SETUP-GUIDE.md (دليل الإعداد الكامل)
echo.
pause
