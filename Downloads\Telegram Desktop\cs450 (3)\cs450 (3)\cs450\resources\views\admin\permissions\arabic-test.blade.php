@extends('layouts.admin')

@section('title', 'اختبار الصلاحيات العربية')

@section('content')
<div class="mb-6">
    <div class="flex justify-between items-center">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white">اختبار الصلاحيات العربية</h2>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">اختبار الصلاحيات المباشرة باللغة العربية</p>
        </div>
    </div>
</div>

<!-- معلومات المستخدم الحالي -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
        <i class="fas fa-user text-blue-500 ml-2"></i>
        معلومات المستخدم الحالي
    </h3>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">الاسم:</p>
            <p class="font-semibold text-gray-800 dark:text-white">{{ auth()->user()->first_name }} {{ auth()->user()->last_name }}</p>
        </div>
        
        <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">نوع المستخدم:</p>
            <p class="font-semibold text-gray-800 dark:text-white">{{ auth()->user()->user_type }}</p>
        </div>
        
        <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">الأدوار:</p>
            <div class="flex flex-wrap gap-2">
                @foreach(auth()->user()->getRoleNames() as $role)
                    <span class="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm">{{ $role }}</span>
                @endforeach
            </div>
        </div>
        
        <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">عدد الصلاحيات:</p>
            <p class="font-semibold text-gray-800 dark:text-white">{{ auth()->user()->getAllPermissions()->count() }}</p>
        </div>
    </div>
</div>

<!-- اختبار الصلاحيات العربية -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
        <i class="fas fa-check-circle text-green-500 ml-2"></i>
        اختبار الصلاحيات العربية
    </h3>
    
    @php
        $arabicPermissions = [
            'عرض_لوحة_التحكم' => 'الوصول للوحة تحكم المدير',
            'إدارة_النظام' => 'إدارة إعدادات النظام العامة',
            'وضع_الصيانة' => 'تفعيل وإلغاء وضع الصيانة',
            'عرض_المستخدمين' => 'عرض قائمة جميع المستخدمين',
            'إضافة_مستخدمين' => 'إنشاء حسابات مستخدمين جديدة',
            'إدارة_الصلاحيات' => 'منح وإزالة صلاحيات المستخدمين',
            'عرض_التقارير_المالية' => 'عرض التقارير المالية',
            'تقارير_الضرائب' => 'إنشاء تقارير الضرائب',
            'إدارة_الطاولات' => 'إدارة طاولات المطعم',
            'إدارة_الحجوزات' => 'إدارة حجوزات العملاء',
            'عرض_الطلبات' => 'عرض جميع طلبات العملاء',
            'إدارة_المخزون' => 'إدارة مخزون المطعم',
            'التحليلات_المتقدمة' => 'عرض التحليلات المتقدمة',
            'الوصول_في_الطوارئ' => 'الوصول للنظام في حالات الطوارئ',
            'عرض_سجل_التدقيق' => 'عرض سجل أنشطة المستخدمين'
        ];
    @endphp
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        @foreach($arabicPermissions as $permission => $description)
            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                    <h4 class="font-medium text-gray-800 dark:text-white text-sm">{{ $permission }}</h4>
                    @if(auth()->user()->can($permission))
                        <i class="fas fa-check-circle text-green-500"></i>
                    @else
                        <i class="fas fa-times-circle text-red-500"></i>
                    @endif
                </div>
                <p class="text-xs text-gray-600 dark:text-gray-400">{{ $description }}</p>
                <div class="mt-2">
                    @if(auth()->user()->can($permission))
                        <span class="px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded text-xs">متاح</span>
                    @else
                        <span class="px-2 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded text-xs">غير متاح</span>
                    @endif
                </div>
            </div>
        @endforeach
    </div>
</div>

<!-- جميع الصلاحيات -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
        <i class="fas fa-list text-purple-500 ml-2"></i>
        جميع صلاحياتي
    </h3>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2">
        @foreach(auth()->user()->getAllPermissions() as $permission)
            <div class="flex items-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                <i class="fas fa-key text-xs text-blue-500 ml-2"></i>
                <span class="text-sm text-gray-700 dark:text-gray-300">{{ $permission->name }}</span>
            </div>
        @endforeach
    </div>
    
    @if(auth()->user()->getAllPermissions()->count() == 0)
        <p class="text-gray-500 dark:text-gray-400 text-center py-8">لا توجد صلاحيات مخصصة</p>
    @endif
</div>

@endsection
