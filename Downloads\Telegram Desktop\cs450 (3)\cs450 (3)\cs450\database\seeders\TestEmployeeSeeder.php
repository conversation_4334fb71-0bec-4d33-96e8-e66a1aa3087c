<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class TestEmployeeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء موظف بصلاحيات محدودة جداً (بدون تقارير)
        $limitedEmployee = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'سارة',
                'last_name' => 'محمد',
                'phone' => '+218912345100',
                'address' => 'طرابلس، ليبيا',
                'password' => Hash::make('limited123'),
                'user_type' => 'employee',
                'is_active' => true
            ]
        );

        // منح صلاحيات محدودة فقط (بدون تقارير)
        $limitedEmployee->givePermissionTo([
            'dashboard.admin', // الوصول للوحة الإدارة
            'orders.view', 'orders.view.details', 'orders.create', // الطلبات فقط
            'tables.view', 'tables.view.details', 'tables.status' // الطاولات فقط
        ]);

        // إنشاء موظف بصلاحيات متوسطة (مع بعض التقارير)
        $mediumEmployee = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'خالد',
                'last_name' => 'أحمد',
                'phone' => '+218912345101',
                'address' => 'بنغازي، ليبيا',
                'password' => Hash::make('medium123'),
                'user_type' => 'employee',
                'is_active' => true
            ]
        );

        // منح صلاحيات متوسطة (مع تقارير المبيعات فقط)
        $mediumEmployee->givePermissionTo([
            'dashboard.admin', // الوصول للوحة الإدارة
            'orders.view', 'orders.view.details', 'orders.view.all', 'orders.create', 'orders.edit', 'orders.status',
            'menu.view', 'menu.view.details',
            'reports.view', 'reports.sales', // تقارير المبيعات فقط
            'tables.view', 'tables.view.details', 'tables.status'
        ]);

        $this->command->info('تم إنشاء موظفين للاختبار:');
        $this->command->line('1. موظف محدود: <EMAIL> / limited123 (بدون تقارير)');
        $this->command->line('2. موظف متوسط: <EMAIL> / medium123 (تقارير المبيعات فقط)');
    }
}
