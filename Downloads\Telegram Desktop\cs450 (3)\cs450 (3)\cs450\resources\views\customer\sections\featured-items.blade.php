<!-- قسم الأطباق المميزة -->
<div class="bg-gray-50 dark:bg-gray-900 py-16">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-800 dark:text-white mb-4">أطباقنا المميزة</h2>
            <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">تحضر بمهارة عالية وباستخدام أفضل المكونات الطازجة لإرضاء ذوقك</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @forelse($featuredItems as $item)
            <div class="menu-item-card bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                <div class="h-48 overflow-hidden relative">
                    <img src="{{ $item->image_url ?? asset('images/default-dish.svg') }}" alt="{{ $item->name }}" class="menu-item-image w-full h-full object-cover">
                    @if(isset($item->is_bestseller) && $item->is_bestseller)
                    <div class="absolute top-4 right-4 bg-white dark:bg-gray-900 text-primary rounded-full py-1 px-3 text-sm font-semibold">
                        الأكثر مبيعاً
                    </div>
                    @endif
                </div>
                <div class="p-5">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="font-bold text-lg text-gray-800 dark:text-white">{{ $item->name }}</h3>
                        <span class="font-bold text-primary">{{ $item->price }} د.ل</span>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">{{ $item->description }}</p>
                    <div class="flex justify-between items-center">
                        <div class="flex items-center text-sm">
                            @php
                                $averageRating = $item->reviews()->avg('rating') ?? 0;
                                $reviewsCount = $item->reviews()->count();
                            @endphp
                            <div class="text-yellow-400 flex">
                                @for($i = 1; $i <= 5; $i++)
                                    @if($i <= floor($averageRating))
                                        <i class="fas fa-star"></i>
                                    @elseif($i - 0.5 <= $averageRating)
                                        <i class="fas fa-star-half-alt"></i>
                                    @else
                                        <i class="far fa-star"></i>
                                    @endif
                                @endfor
                            </div>
                            <span class="text-gray-600 dark:text-gray-400 mr-2 font-medium">
                                @if($averageRating > 0)
                                    {{ number_format($averageRating, 1) }}
                                    @if($reviewsCount > 0)
                                        <span class="text-xs">({{ $reviewsCount }})</span>
                                    @endif
                                @else
                                    <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">جديد</span>
                                @endif
                            </span>
                        </div>
                        @auth
                        <div class="flex gap-2">
                        <button class="add-to-cart btn-hover-effect bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-full text-sm transition"
                                onclick="addToCart({{ $item->item_id ?? $loop->index + 1 }}, '{{ $item->name }}', {{ $item->price }})"
                                data-item-id="{{ $item->item_id ?? $loop->index + 1 }}">
                            <i class="fas fa-plus ml-1"></i>إضافة للسلة
                        </button>
                        <button class="bg-accent hover:bg-accent/90 text-darkText py-2 px-3 rounded-full text-sm transition"
                                    onclick="showCustomizeModal({{ $item->item_id }}, '{{ $item->name }}', {{ $item->price }})"
                                    title="تخصيص الطلب">
                                <i class="fas fa-cog"></i>
                        </button>
                        </div>
                        @else
                        <a href="{{ route('login') }}" class="bg-gray-400 hover:bg-gray-500 text-white py-2 px-4 rounded-full text-sm transition">
                            <i class="fas fa-sign-in-alt ml-1"></i>سجل دخول للطلب
                        </a>
                        @endauth
                    </div>
                </div>
            </div>
            @empty
            <!-- عناصر افتراضية في حالة عدم وجود عناصر مميزة -->
            @include('customer.components.default-menu-item')
            @endforelse
        </div>

        <div class="mt-12 text-center">
            <a href="{{ route('customer.menu') }}" class="btn-hover-effect inline-block bg-white dark:bg-gray-800 border-2 border-primary text-primary hover:bg-primary/5 dark:hover:bg-gray-700 font-bold py-3 px-8 rounded-full text-lg transition">
                عرض القائمة كاملة
            </a>
        </div>
    </div>
</div>
<!-- نافذة تخصيص الطلب -->
<div id="customizeModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <!-- رأس النافذة -->
        <div class="bg-gradient-to-r from-primary to-accent p-6 rounded-t-2xl">
            <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-white" id="modalItemName">تخصيص الطلب</h3>
                <button onclick="closeCustomizeModal()" class="text-white hover:text-gray-200 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- محتوى النافذة -->
        <div class="p-6">
            <!-- الكمية -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الكمية</label>
                <div class="flex items-center justify-center gap-4">
                    <button onclick="changeModalQuantity(-1)" class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 w-10 h-10 rounded-full flex items-center justify-center transition-colors">
                        <i class="fas fa-minus"></i>
                    </button>
                    <span id="modalQuantity" class="text-2xl font-bold text-gray-800 dark:text-white min-w-[3rem] text-center">1</span>
                    <button onclick="changeModalQuantity(1)" class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 w-10 h-10 rounded-full flex items-center justify-center transition-colors">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>

            <!-- المكونات المستبعدة -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    <i class="fas fa-ban text-red-500 ml-2"></i>
                    المكونات المراد استبعادها
                </label>
                <div id="ingredientsList" class="space-y-2 max-h-48 overflow-y-auto">
                    <!-- سيتم ملء المكونات هنا بـ JavaScript -->
                </div>
            </div>

            <!-- ملاحظات خاصة -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <i class="fas fa-comment ml-2"></i>
                    ملاحظات خاصة
                </label>
                <textarea id="modalSpecialInstructions"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"
                          rows="3"
                          placeholder="أي ملاحظات خاصة للطلب..."></textarea>
            </div>

            <!-- السعر -->
            <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="flex justify-between items-center">
                    <span class="text-lg font-medium text-gray-700 dark:text-gray-300">السعر الإجمالي:</span>
                    <span id="modalTotalPrice" class="text-2xl font-bold text-primary">0.00 د.ل</span>
                </div>
            </div>

            <!-- أزرار العمل -->
            <div class="flex gap-3">
                <button onclick="closeCustomizeModal()"
                        class="flex-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 py-3 px-4 rounded-lg font-medium transition-colors">
                    إلغاء
                </button>
                <button onclick="addCustomizedItemToCart()"
                        class="flex-1 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-white py-3 px-4 rounded-lg font-medium transition-all transform hover:scale-105">
                    <i class="fas fa-cart-plus ml-2"></i>
                    إضافة للسلة
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<!-- سكريبت فلترة الفئات -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const categoryFilters = document.querySelectorAll('.category-filter');
    const menuItems = document.querySelectorAll('.menu-item');

    categoryFilters.forEach(filter => {
        filter.addEventListener('click', function() {
            const category = this.dataset.category;

            // تحديث الأزرار النشطة
            categoryFilters.forEach(f => f.classList.remove('active', 'bg-primary', 'text-white'));
            categoryFilters.forEach(f => f.classList.add('bg-white', 'dark:bg-gray-800', 'text-gray-700', 'dark:text-gray-300', 'border', 'border-gray-200', 'dark:border-gray-700'));

            this.classList.add('active', 'bg-primary', 'text-white');
            this.classList.remove('bg-white', 'dark:bg-gray-800', 'text-gray-700', 'dark:text-gray-300', 'border', 'border-gray-200', 'dark:border-gray-700');

            // فلترة العناصر
            menuItems.forEach(item => {
                if (category === 'all' || item.dataset.category === category) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });

});

// متغيرات النافذة المنبثقة
let currentModalItem = {
    id: null,
    name: '',
    price: 0,
    quantity: 1
};

// إظهار نافذة تخصيص الطلب
function showCustomizeModal(itemId, itemName, itemPrice) {
    currentModalItem = {
        id: itemId,
        name: itemName,
        price: parseFloat(itemPrice),
        quantity: 1
    };

    // تحديث عنوان النافذة
    document.getElementById('modalItemName').textContent = `تخصيص: ${itemName}`;

    // إعادة تعيين الكمية
    document.getElementById('modalQuantity').textContent = '1';

    // تحديث السعر
    updateModalPrice();

    // تحميل المكونات الخاصة بهذا الصنف
    loadIngredients(itemId);

    // مسح الملاحظات
    document.getElementById('modalSpecialInstructions').value = '';

    // إظهار النافذة
    document.getElementById('customizeModal').classList.remove('hidden');
}

// إغلاق نافذة التخصيص
function closeCustomizeModal() {
    document.getElementById('customizeModal').classList.add('hidden');
}

// تغيير الكمية في النافذة
function changeModalQuantity(change) {
    const quantityElement = document.getElementById('modalQuantity');
    let quantity = parseInt(quantityElement.textContent) + change;

    if (quantity < 1) quantity = 1;
    if (quantity > 10) quantity = 10;

    quantityElement.textContent = quantity;
    currentModalItem.quantity = quantity;
    updateModalPrice();
}

// تحديث السعر في النافذة
function updateModalPrice() {
    const totalPrice = currentModalItem.price * currentModalItem.quantity;
    document.getElementById('modalTotalPrice').textContent = totalPrice.toFixed(2) + ' د.ل';
}

// تحميل المكونات الخاصة بصنف معين
function loadIngredients(itemId) {
    const ingredientsList = document.getElementById('ingredientsList');
    ingredientsList.innerHTML = '<div class="text-center py-4"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';

    fetch(`/api/menu-item/${itemId}/ingredients`)
        .then(response => response.json())
        .then(data => {
            if (data.length === 0) {
                ingredientsList.innerHTML = '<div class="text-center py-4 text-gray-500">لا توجد مكونات قابلة للاستبعاد لهذا الصنف</div>';
                return;
            }

            let html = '';
            data.forEach(ingredient => {
                html += `
                    <label class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer transition-colors">
                        <input type="checkbox"
                               class="form-checkbox h-5 w-5 text-red-500 rounded focus:ring-red-500 focus:ring-2"
                               value="${ingredient.ingredient_id}"
                               onchange="updateExcludedIngredients()">
                        <span class="mr-3 text-gray-700 dark:text-gray-300">${ingredient.name}</span>
                        <i class="fas fa-ban text-red-400 mr-auto"></i>
                    </label>
                `;
            });
            ingredientsList.innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading ingredients:', error);
            ingredientsList.innerHTML = '<div class="text-center py-4 text-red-500">خطأ في تحميل المكونات</div>';
        });
}

// تحديث المكونات المستبعدة
function updateExcludedIngredients() {
    // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
}

// إضافة العنصر المخصص للسلة
function addCustomizedItemToCart() {
    const excludedIngredients = [];
    const checkboxes = document.querySelectorAll('#ingredientsList input[type="checkbox"]:checked');
    checkboxes.forEach(checkbox => {
        excludedIngredients.push(parseInt(checkbox.value));
    });

    const specialInstructions = document.getElementById('modalSpecialInstructions').value;

    const button = document.querySelector('[onclick*="addCustomizedItemToCart"]');
    const originalText = button.innerHTML;

    // تغيير النص أثناء التحميل
    button.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري الإضافة...';
    button.disabled = true;

    fetch('/customer/cart/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            menu_item_id: currentModalItem.id,
            quantity: currentModalItem.quantity,
            special_instructions: specialInstructions,
            excluded_ingredients: excludedIngredients
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إغلاق النافذة
            closeCustomizeModal();

            // إظهار رسالة نجاح
            showSuccessMessage(data.message);

            // تحديث عداد السلة
            updateCartCount(data.cart_count);
        } else {
            showErrorMessage(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorMessage('حدث خطأ أثناء إضافة العنصر للسلة');
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// دوال الرسائل
function showSuccessMessage(message) {
    // يمكن استخدام نفس نظام الإشعارات الموجود
    if (typeof showNotification === 'function') {
        showNotification(message, 'success');
    } else {
        alert(message);
    }
}

function showErrorMessage(message) {
    if (typeof showNotification === 'function') {
        showNotification(message, 'error');
    } else {
        alert(message);
    }
}

// إضافة للسلة
function addToCart(itemId, itemName, itemPrice) {
    const button = document.querySelector(`[data-item-id="${itemId}"]`);
    const originalText = button.innerHTML;

    // تغيير النص أثناء التحميل
    button.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i>جاري الإضافة...';
    button.disabled = true;

    fetch('/customer/cart/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            menu_item_id: itemId,
            quantity: 1
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث عداد السلة في الـ header
            updateCartCount(data.cart_count);

            // تغيير النص مؤقتاً
            button.innerHTML = '<i class="fas fa-check ml-1"></i>تم الإضافة!';
            button.classList.remove('bg-primary', 'hover:bg-primary/90');
            button.classList.add('bg-green-500');

            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('bg-green-500');
                button.classList.add('bg-primary', 'hover:bg-primary/90');
                button.disabled = false;
            }, 2000);

            showNotification(data.message, 'success');
        } else {
            // إظهار رسالة خطأ مع تفاصيل أكثر
            let errorMessage = data.message || 'حدث خطأ أثناء إضافة العنصر';

            // إذا كانت المشكلة في المكونات، أظهر رسالة خاصة
            if (errorMessage.includes('مكونات') || errorMessage.includes('مخزون')) {
                showNotification(errorMessage, 'warning');
                // تعطيل الزر نهائياً إذا كانت المكونات غير متوفرة
                button.innerHTML = '<i class="fas fa-times ml-1"></i>غير متوفر';
                button.classList.remove('bg-primary', 'hover:bg-primary/90');
                button.classList.add('bg-gray-400', 'cursor-not-allowed');
                button.disabled = true;
            } else {
                showNotification(errorMessage, 'error');
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }
    })
    .catch(error => {
        button.innerHTML = originalText;
        button.disabled = false;
        showNotification('حدث خطأ أثناء إضافة العنصر للسلة', 'error');
    });
}

// تحديث عداد السلة
function updateCartCount(count) {
    const cartCount = document.getElementById('cartCount');
    if (cartCount) {
        cartCount.textContent = count;
    }
}

// عرض الإشعارات
function showNotification(message, type) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `fixed top-20 left-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} ml-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // إظهار الإشعار
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // إخفاء الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}
</script>
@endpush