// مخططات تقرير المبيعات

// دالة تهيئة المخططات
function initSalesCharts() {
    // التحقق من وجود ApexCharts
    if (typeof ApexCharts === 'undefined') {
        console.error('ApexCharts غير محملة');
        return;
    }

    // الحصول على البيانات من النافذة العامة
    const dailySalesData = window.salesData ? window.salesData.daily : [];
    const categoryData = window.salesData ? window.salesData.category : [];

    // مخطط المبيعات اليومية
    initDailySalesChart(dailySalesData);

    // مخطط الفئات
    initCategoryChart(categoryData);
}

// مخطط المبيعات اليومية
function initDailySalesChart(salesData) {
    const chartElement = document.getElementById("dailySalesChart");
    if (!chartElement) {
        return;
    }

    let dates = [];
    let values = [];

    // تحويل البيانات إلى تنسيق مناسب للمخطط
    if (Array.isArray(salesData) && salesData.length > 0) {
        dates = salesData.map(item => item.date);
        values = salesData.map(item => parseFloat(item.total_sales || item.total || 0));
    } else {
        // بيانات تجريبية إذا لم توجد بيانات حقيقية
        dates = ['2025-08-15', '2025-08-16', '2025-08-17', '2025-08-18', '2025-08-19'];
        values = [210, 185, 320, 280, 623];
    }

        const options = {
            series: [
                {
                    name: "المبيعات اليومية",
                    data: values,
                },
            ],
            chart: {
                type: "line",
                height: 350,
                fontFamily: "Cairo, sans-serif",
                toolbar: {
                    show: true,
                },
            },
            dataLabels: {
                enabled: false,
            },
            stroke: {
                curve: "smooth",
                width: 3,
            },
            xaxis: {
                categories: dates,
                title: {
                    text: "التاريخ",
                },
            },
            yaxis: {
                title: {
                    text: "المبيعات (د.ل)",
                },
                labels: {
                    formatter: function (val) {
                        return val.toFixed(2) + " د.ل";
                    },
                },
            },
            tooltip: {
                y: {
                    formatter: function (val) {
                        return val.toFixed(2) + " د.ل";
                    },
                },
            },
            colors: ["#3B82F6"],
            grid: {
                borderColor: "#e7e7e7",
                row: {
                    colors: ["#f3f3f3", "transparent"],
                    opacity: 0.5,
                },
            },
        };

    try {
        const chart = new ApexCharts(chartElement, options);
        chart.render();
        console.log('تم إنشاء مخطط المبيعات اليومية بنجاح');
    } catch (error) {
        console.error('خطأ في إنشاء مخطط المبيعات اليومية:', error);
        chartElement.innerHTML = '<div class="text-center text-red-500 p-8">خطأ في تحميل المخطط</div>';
    }
    }

    // مخطط المبيعات حسب الفئة
    function initCategoryChart(categoryData) {
        if (!document.getElementById("categoryChart") || !categoryData) {
            return;
        }

        const categoryLabels = [];
        const categoryValues = [];

        if (Array.isArray(categoryData) && categoryData.length > 0) {
            categoryData.forEach((item) => {
                categoryLabels.push(item.category || item.name || "غير محدد");
                categoryValues.push(parseFloat(item.total || item.value || 0));
            });
        } else {
            // بيانات تجريبية إذا لم توجد بيانات حقيقية
            categoryLabels.push('الأطباق الرئيسية', 'المقبلات', 'المشروبات', 'الحلويات');
            categoryValues.push(1519, 300, 15, 150);
        }

        const categoryOptions = {
            series: categoryValues,
            chart: {
                type: "donut",
                height: 350,
                fontFamily: "Cairo, sans-serif",
            },
            labels: categoryLabels,
            colors: [
                "#3B82F6",
                "#10B981",
                "#F59E0B",
                "#EF4444",
                "#8B5CF6",
                "#06B6D4",
            ],
            legend: {
                position: "bottom",
            },
            tooltip: {
                y: {
                    formatter: function (val) {
                        return val.toFixed(2) + " د.ل";
                    },
                },
            },
            plotOptions: {
                pie: {
                    donut: {
                        size: "70%",
                    },
                },
            },
        };

        // إزالة رسالة التحميل
        const categoryContainer = document.querySelector("#categoryChart");
        categoryContainer.innerHTML = "";

        // التحقق من وجود ApexCharts
        if (typeof ApexCharts === "undefined") {
            console.error("ApexCharts غير محملة");
            categoryContainer.innerHTML =
                '<div class="text-red-500 text-center">خطأ: مكتبة المخططات غير محملة</div>';
            return;
        }

        const categoryChart = new ApexCharts(
            categoryContainer,
            categoryOptions
        );
        categoryChart.render();
    }

    // تهيئة المخططات عند تحميل الصفحة
    window.initSalesCharts = function (dailySalesData, categoryData) {
        try {
            if (dailySalesData && Array.isArray(dailySalesData)) {
                initDailySalesChart(dailySalesData);
            }

            if (categoryData && Array.isArray(categoryData)) {
                initCategoryChart(categoryData);
            }
        } catch (error) {
            console.error("خطأ في تهيئة المخططات:", error);
        }
    };

    // تهيئة تلقائية إذا كانت البيانات متوفرة في النافذة
    if (typeof window.salesChartData !== "undefined") {
        window.initSalesCharts(
            window.salesChartData.dailySales || [],
            window.salesChartData.categoryData || []
        );
    }
});
