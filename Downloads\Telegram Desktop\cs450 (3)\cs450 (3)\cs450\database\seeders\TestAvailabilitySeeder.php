<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MenuItem;
use App\Models\Ingredient;
use App\Models\Recipe;
use App\Models\Inventory;

class TestAvailabilitySeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // إنشاء مكونات للاختبار
        $ingredients = [
            ['name' => 'دقيق', 'unit' => 'كيلو'],
            ['name' => 'لحم مفروم', 'unit' => 'كيلو'],
            ['name' => 'جبن موزاريلا', 'unit' => 'كيلو'],
            ['name' => 'طماطم', 'unit' => 'كيلو'],
            ['name' => 'بصل', 'unit' => 'كيلو'],
        ];

        foreach ($ingredients as $ingredientData) {
            $ingredient = Ingredient::firstOrCreate(
                ['name' => $ingredientData['name']],
                $ingredientData
            );

            // إضافة مخزون للمكونات
            Inventory::firstOrCreate([
                'ingredient_id' => $ingredient->ingredient_id,
                'quantity' => rand(0, 10), // كمية عشوائية من 0 إلى 10
                'unit_cost' => rand(5, 20),
                'supplier' => 'مورد تجريبي',
                'expiry_date' => now()->addDays(30),
                'notes' => 'مخزون تجريبي للاختبار'
            ]);
        }

        // إنشاء منتجات للاختبار
        $menuItems = [
            [
                'name' => 'بيتزا مارجريتا',
                'price' => 25.00,
                'category' => 'main',
                'description' => 'بيتزا كلاسيكية بالجبن والطماطم',
                'is_available' => true
            ],
            [
                'name' => 'برجر لحم',
                'price' => 18.00,
                'category' => 'main',
                'description' => 'برجر لحم مع الخضار',
                'is_available' => true
            ]
        ];

        foreach ($menuItems as $itemData) {
            $menuItem = MenuItem::firstOrCreate(
                ['name' => $itemData['name']],
                $itemData
            );

            // إضافة وصفات للمنتجات
            if ($menuItem->name === 'بيتزا مارجريتا') {
                $doughIngredient = Ingredient::where('name', 'دقيق')->first();
                $cheeseIngredient = Ingredient::where('name', 'جبن موزاريلا')->first();
                $tomatoIngredient = Ingredient::where('name', 'طماطم')->first();

                Recipe::firstOrCreate([
                    'menu_item_id' => $menuItem->item_id,
                    'ingredient_id' => $doughIngredient->ingredient_id,
                    'quantity' => 0.3 // 300 جرام دقيق
                ]);

                Recipe::firstOrCreate([
                    'menu_item_id' => $menuItem->item_id,
                    'ingredient_id' => $cheeseIngredient->ingredient_id,
                    'quantity' => 0.2 // 200 جرام جبن
                ]);

                Recipe::firstOrCreate([
                    'menu_item_id' => $menuItem->item_id,
                    'ingredient_id' => $tomatoIngredient->ingredient_id,
                    'quantity' => 0.1 // 100 جرام طماطم
                ]);
            }

            if ($menuItem->name === 'برجر لحم') {
                $meatIngredient = Ingredient::where('name', 'لحم مفروم')->first();
                $onionIngredient = Ingredient::where('name', 'بصل')->first();

                Recipe::firstOrCreate([
                    'menu_item_id' => $menuItem->item_id,
                    'ingredient_id' => $meatIngredient->ingredient_id,
                    'quantity' => 0.15 // 150 جرام لحم
                ]);

                Recipe::firstOrCreate([
                    'menu_item_id' => $menuItem->item_id,
                    'ingredient_id' => $onionIngredient->ingredient_id,
                    'quantity' => 0.05 // 50 جرام بصل
                ]);
            }
        }

        $this->command->info('تم إنشاء بيانات الاختبار بنجاح!');
        $this->command->info('يمكنك الآن اختبار نظام التحقق من توفر المكونات.');
    }
}
