<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Reservation;
use App\Models\User;
use App\Models\Table;
use Carbon\Carbon;

class ReservationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // الحصول على مستخدم عميل
        $customer = User::where('user_type', 'customer')->first();

        if (!$customer) {
            // إنشاء عميل إذا لم يوجد
            $customer = User::create([
                'first_name' => 'أحمد',
                'last_name' => 'محمد',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'phone' => '0123456789',
                'user_type' => 'customer',
                'email_verified_at' => now(),
            ]);
        }

        // الحصول على طاولة
        $table = Table::first();

        if (!$table) {
            // إنشاء طاولة إذا لم توجد
            $table = Table::create([
                'table_number' => 1,
                'capacity' => 4,
                'status' => 'available',
                'location' => 'main_hall'
            ]);
        }

        // إنشاء حجوزات تجريبية
        $reservations = [
            [
                'user_id' => $customer->user_id,
                'table_id' => $table->table_id,
                'reservation_time' => Carbon::today()->addDays(1)->setTime(19, 0),
                'duration' => 120,
                'party_size' => 4,
                'status' => 'confirmed',
                'special_requests' => 'حجز عشاء رومانسي',
                'contact_phone' => '0123456789',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'user_id' => $customer->user_id,
                'table_id' => $table->table_id,
                'reservation_time' => Carbon::today()->addDays(2)->setTime(20, 0),
                'duration' => 90,
                'party_size' => 2,
                'status' => 'pending',
                'special_requests' => 'حجز لعيد ميلاد',
                'contact_phone' => '0123456789',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'user_id' => $customer->user_id,
                'table_id' => $table->table_id,
                'reservation_time' => Carbon::today()->addDays(3)->setTime(18, 30),
                'duration' => 150,
                'party_size' => 6,
                'status' => 'confirmed',
                'special_requests' => 'حجز عائلي',
                'contact_phone' => '0123456789',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($reservations as $reservation) {
            Reservation::create($reservation);
        }
    }
}
