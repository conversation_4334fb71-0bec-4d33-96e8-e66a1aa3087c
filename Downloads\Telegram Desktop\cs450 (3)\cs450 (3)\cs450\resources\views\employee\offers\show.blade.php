@extends('employee.layouts.app')

@section('title', 'تفاصيل العرض - ' . $offer->title)

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">{{ $offer->title }}</h1>
            <p class="text-gray-600 dark:text-gray-400">تفاصيل العرض وإحصائيات الأداء</p>
        </div>
        <div class="mt-4 lg:mt-0 flex flex-wrap gap-2">
            <a href="{{ route('employee.offers.edit', $offer) }}" 
               class="inline-flex items-center px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded-lg transition-colors">
                <i class="fas fa-edit ml-2"></i>
                تعديل العرض
            </a>
            <form action="{{ route('employee.offers.toggleStatus', $offer) }}" method="POST" class="inline">
                @csrf
                @method('PATCH')
                <button type="submit" 
                        class="inline-flex items-center px-4 py-2 {{ $offer->is_active ? 'bg-orange-500 hover:bg-orange-600' : 'bg-green-500 hover:bg-green-600' }} text-white rounded-lg transition-colors">
                    <i class="fas fa-{{ $offer->is_active ? 'pause' : 'play' }} ml-2"></i>
                    {{ $offer->is_active ? 'إيقاف العرض' : 'تفعيل العرض' }}
                </button>
            </form>
            <a href="{{ route('employee.offers.index') }}" 
               class="inline-flex items-center px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- العمود الرئيسي -->
        <div class="lg:col-span-2 space-y-8">
            <!-- معلومات العرض الأساسية -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                <!-- صورة العرض -->
                @if($offer->image_path)
                    <div class="h-64 bg-gray-200 dark:bg-gray-700">
                        <img src="{{ $offer->image_url }}" alt="{{ $offer->title }}" 
                             class="w-full h-full object-cover">
                    </div>
                @endif

                <div class="p-6">
                    <!-- حالة العرض -->
                    <div class="flex items-center justify-between mb-6">
                        @php
                            $statusClasses = [
                                'active' => 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
                                'upcoming' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
                                'expired' => 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
                                'inactive' => 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'
                            ];
                        @endphp
                        <span class="px-4 py-2 rounded-full text-sm font-medium {{ $statusClasses[$offer->status] ?? 'bg-gray-100 text-gray-800' }}">
                            {{ $offer->status_text }}
                        </span>
                        <span class="px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium">
                            @switch($offer->offer_type)
                                @case('percentage')
                                    نسبة مئوية
                                    @break
                                @case('fixed_amount')
                                    مبلغ ثابت
                                    @break
                                @case('buy_get')
                                    اشتري واحد واحصل على آخر
                                    @break
                                @case('combo')
                                    عرض مجموعة
                                    @break
                                @default
                                    {{ $offer->offer_type }}
                            @endswitch
                        </span>
                    </div>

                    <!-- الوصف -->
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">وصف العرض</h3>
                        <p class="text-gray-600 dark:text-gray-400 leading-relaxed">{{ $offer->description }}</p>
                    </div>

                    <!-- معلومات الخصم -->
                    @if($offer->discount_percentage || $offer->discount_amount)
                        <div class="bg-primary/5 rounded-lg p-4 mb-6">
                            <h4 class="text-lg font-semibold text-primary mb-2">تفاصيل الخصم</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                @if($offer->discount_percentage)
                                    <div>
                                        <span class="text-sm text-gray-600 dark:text-gray-400">نسبة الخصم</span>
                                        <p class="text-2xl font-bold text-primary">{{ $offer->discount_percentage }}%</p>
                                    </div>
                                @endif
                                @if($offer->discount_amount)
                                    <div>
                                        <span class="text-sm text-gray-600 dark:text-gray-400">مبلغ الخصم</span>
                                        <p class="text-2xl font-bold text-primary">{{ $offer->discount_amount }} د.ل</p>
                                    </div>
                                @endif
                                @if($offer->original_price)
                                    <div>
                                        <span class="text-sm text-gray-600 dark:text-gray-400">السعر الأصلي</span>
                                        <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ $offer->original_price }} د.ل</p>
                                    </div>
                                @endif
                                @if($offer->discounted_price)
                                    <div>
                                        <span class="text-sm text-gray-600 dark:text-gray-400">السعر بعد الخصم</span>
                                        <p class="text-lg font-semibold text-green-600">{{ $offer->discounted_price }} د.ل</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif

                    <!-- التواريخ -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <h4 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">تاريخ البداية</h4>
                            <p class="text-lg font-semibold text-gray-900 dark:text-white">
                                {{ $offer->start_date->format('Y/m/d H:i') }}
                            </p>
                        </div>
                        @if($offer->end_date)
                            <div>
                                <h4 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">تاريخ النهاية</h4>
                                <p class="text-lg font-semibold text-gray-900 dark:text-white">
                                    {{ $offer->end_date->format('Y/m/d H:i') }}
                                </p>
                                @if($offer->days_remaining !== null)
                                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                        {{ $offer->days_remaining > 0 ? 'باقي ' . $offer->days_remaining . ' يوم' : 'انتهى' }}
                                    </p>
                                @endif
                            </div>
                        @else
                            <div>
                                <h4 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">تاريخ النهاية</h4>
                                <p class="text-lg font-semibold text-green-600">عرض مفتوح</p>
                            </div>
                        @endif
                    </div>

                    <!-- الشروط والأحكام -->
                    @if($offer->terms_conditions)
                        <div class="mb-6">
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">الشروط والأحكام</h4>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                @if(is_array($offer->terms_conditions))
                                    <ul class="list-disc list-inside text-gray-600 dark:text-gray-400 leading-relaxed space-y-2">
                                        @foreach($offer->terms_conditions as $term)
                                            <li>{{ $term }}</li>
                                        @endforeach
                                    </ul>
                                @else
                                    <p class="text-gray-600 dark:text-gray-400 leading-relaxed">{{ $offer->terms_conditions }}</p>
                                @endif
                            </div>
                        </div>
                    @endif

                    <!-- عناصر القائمة المطبقة -->
                    @if($offer->menuItems->count() > 0)
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">عناصر القائمة المطبقة</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                @foreach($offer->menuItems as $item)
                                    <div class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                        <div class="flex-1">
                                            <p class="font-medium text-gray-900 dark:text-white">{{ $item->name }}</p>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $item->price }} د.ل</p>
                                        </div>
                                        <span class="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">
                                            {{ $item->category }}
                                        </span>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @else
                        <div class="text-center py-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <p class="text-gray-500 dark:text-gray-400">ينطبق على جميع عناصر القائمة</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- الشريط الجانبي -->
        <div class="space-y-6">
            <!-- إحصائيات العرض -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">إحصائيات العرض</h3>
                
                <div class="space-y-4">
                    <!-- إجمالي الطلبات -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center ml-3">
                                <i class="fas fa-shopping-cart text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي الطلبات</p>
                                <p class="text-xl font-bold text-gray-900 dark:text-white">{{ $stats['total_orders'] }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- إجمالي الحجوزات -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center ml-3">
                                <i class="fas fa-calendar-check text-green-600 dark:text-green-400"></i>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي الحجوزات</p>
                                <p class="text-xl font-bold text-gray-900 dark:text-white">{{ $stats['total_reservations'] }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- إجمالي الإيرادات -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center ml-3">
                                <i class="fas fa-dollar-sign text-yellow-600 dark:text-yellow-400"></i>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي الإيرادات</p>
                                <p class="text-xl font-bold text-gray-900 dark:text-white">{{ number_format($stats['total_revenue'], 2) }} د.ل</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معدل الاستخدام -->
            @if($offer->max_uses)
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">معدل الاستخدام</h3>
                    
                    <div class="space-y-4">
                        <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                            <span>الحد الأقصى للاستخدام</span>
                            <span>{{ $offer->max_uses }}</span>
                        </div>

                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                            <div class="bg-primary h-3 rounded-full transition-all duration-300" style="width: 30%"></div>
                        </div>

                        <p class="text-center text-lg font-semibold text-primary">
                            30%
                        </p>
                    </div>
                </div>
            @endif

            <!-- معلومات إضافية -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">معلومات إضافية</h3>
                
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">رمز العرض</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $offer->slug }}</span>
                    </div>
                    
                    @if($offer->min_order_amount)
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">الحد الأدنى للطلب</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $offer->min_order_amount }} د.ل</span>
                        </div>
                    @endif
                    
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">تاريخ الإنشاء</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $offer->created_at->format('Y/m/d') }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">آخر تحديث</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $offer->updated_at->format('Y/m/d') }}</span>
                    </div>
                </div>
            </div>

            <!-- إجراءات سريعة -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">إجراءات سريعة</h3>
                
                <div class="space-y-3">
                    <a href="{{ route('customer.offers.show', $offer->slug) }}" target="_blank"
                       class="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors">
                        <i class="fas fa-external-link-alt ml-2"></i>
                        معاينة للعملاء
                    </a>
                    
                    <button onclick="copyOfferLink()" 
                            class="w-full inline-flex items-center justify-center px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors">
                        <i class="fas fa-copy ml-2"></i>
                        نسخ رابط العرض
                    </button>
                    
                    <button type="button" onclick="openDeleteModal()"
                            class="w-full inline-flex items-center justify-center px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors">
                        <i class="fas fa-trash ml-2"></i>
                        حذف العرض
                    </button>

                    <form id="delete-form" action="{{ route('employee.offers.destroy', $offer) }}" method="POST" class="hidden">
                        @csrf
                        @method('DELETE')
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function copyOfferLink() {
    const link = "{{ route('customer.offers.show', $offer->slug) }}";
    navigator.clipboard.writeText(link).then(function() {
        // إظهار رسالة نجاح
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check ml-2"></i>تم النسخ!';
        button.classList.remove('bg-green-500', 'hover:bg-green-600');
        button.classList.add('bg-green-600');
        
        setTimeout(function() {
            button.innerHTML = originalText;
            button.classList.remove('bg-green-600');
            button.classList.add('bg-green-500', 'hover:bg-green-600');
        }, 2000);
    });
}

// فتح نافذة الحذف
function openDeleteModal() {
    const modal = document.getElementById('deleteModal');
    const modalContent = modal.querySelector('div');

    modal.classList.remove('hidden');

    // تأثير الظهور
    setTimeout(() => {
        modalContent.classList.remove('scale-95');
        modalContent.classList.add('scale-100');
    }, 10);
}

// إغلاق نافذة الحذف
function closeDeleteModal() {
    const modal = document.getElementById('deleteModal');
    const modalContent = modal.querySelector('div');

    modalContent.classList.remove('scale-100');
    modalContent.classList.add('scale-95');

    setTimeout(() => {
        modal.classList.add('hidden');
    }, 150);
}

// تأكيد الحذف
function confirmDelete() {
    document.getElementById('delete-form').submit();
}

// إغلاق النافذة عند الضغط على الخلفية
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});

// إغلاق النافذة عند الضغط على Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && !document.getElementById('deleteModal').classList.contains('hidden')) {
        closeDeleteModal();
    }
});
</script>
@endpush

<!-- نافذة تأكيد الحذف المتقدمة -->
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-gray-800 rounded-2xl max-w-md w-full shadow-2xl transform transition-all scale-95">
        <!-- رأس النافذة -->
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400 text-xl animate-pulse"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">تأكيد حذف العرض</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">هذا الإجراء لا يمكن التراجع عنه</p>
                </div>
            </div>
        </div>

        <!-- محتوى النافذة -->
        <div class="p-6">
            <div class="text-center">
                <p class="text-gray-700 dark:text-gray-300 mb-4">هل أنت متأكد من حذف العرض:</p>
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4 mb-6">
                    <h4 class="text-lg font-bold text-gray-900 dark:text-white">{{ $offer->title }}</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        <i class="fas fa-calendar ml-1"></i>
                        تاريخ الإنشاء: {{ $offer->created_at->format('Y/m/d') }}
                    </p>
                </div>

                <div class="bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-500 text-lg mt-0.5 ml-3"></i>
                        </div>
                        <div class="text-right">
                            <h4 class="text-sm font-semibold text-red-800 dark:text-red-300 mb-1">تحذير مهم</h4>
                            <p class="text-sm text-red-700 dark:text-red-300 leading-relaxed">
                                سيتم حذف العرض نهائياً من النظام ولن يكون بإمكان العملاء رؤيته أو استخدامه.
                                هذا الإجراء لا يمكن التراجع عنه.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار النافذة -->
        <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700/50 rounded-b-2xl flex gap-3">
            <button onclick="closeDeleteModal()"
                    class="flex-1 px-6 py-3 bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 rounded-xl transition-all duration-200 font-medium shadow-sm hover:shadow-md">
                <i class="fas fa-times ml-2"></i>
                إلغاء الأمر
            </button>
            <button onclick="confirmDelete()"
                    class="flex-1 px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white rounded-xl transition-all duration-200 font-medium shadow-sm hover:shadow-md">
                <i class="fas fa-trash ml-2"></i>
                تأكيد الحذف
            </button>
        </div>
    </div>
</div>

@endsection
