<?php
require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "فحص الطلبات في قاعدة البيانات:\n";
echo "================================\n";

try {
    $orders = \App\Models\Order::all(['order_id', 'status', 'total_amount']);
    
    echo "إجمالي الطلبات: " . $orders->count() . "\n\n";
    
    foreach ($orders as $order) {
        echo "طلب #{$order->order_id} - حالة: {$order->status} - المبلغ: {$order->total_amount}\n";
    }
    
    echo "\n================================\n";
    
    // التحقق من الطلب رقم 8 تحديداً
    $order8 = \App\Models\Order::find(8);
    if ($order8) {
        echo "✅ طلب #8 موجود - الحالة: {$order8->status} - المبلغ: {$order8->total_amount}\n";
    } else {
        echo "❌ طلب #8 غير موجود\n";
    }
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage() . "\n";
}
?>
