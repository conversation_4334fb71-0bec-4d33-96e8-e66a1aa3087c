<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Reservation;
use App\Models\ReservationReview;
use Carbon\Carbon;

class ReservationReviewSeeder extends Seeder
{
    public function run()
    {
        // البحث عن مستخدمين عملاء
        $customers = User::where('user_type', 'customer')->get();
        
        if ($customers->isEmpty()) {
            // إنشاء عملاء تجريبيين إذا لم يوجدوا
            $customers = collect([
                User::create([
                    'first_name' => 'أحمد',
                    'last_name' => 'محمد',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'phone' => '0912345678',
                    'user_type' => 'customer',
                    'email_verified_at' => now(),
                ]),
                User::create([
                    'first_name' => 'فاطمة',
                    'last_name' => 'علي',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'phone' => '0923456789',
                    'user_type' => 'customer',
                    'email_verified_at' => now(),
                ]),
                User::create([
                    'first_name' => 'محمد',
                    'last_name' => 'سالم',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'phone' => '0934567890',
                    'user_type' => 'customer',
                    'email_verified_at' => now(),
                ])
            ]);
        }

        // البحث عن حجوزات مكتملة أو إنشاؤها
        $reservations = Reservation::where('status', 'completed')->get();
        
        if ($reservations->isEmpty()) {
            // إنشاء حجوزات مكتملة تجريبية
            $table = \App\Models\Table::first();
            if (!$table) {
                $table = \App\Models\Table::create([
                    'table_number' => 1,
                    'capacity' => 4,
                    'is_available' => true
                ]);
            }

            $reservations = collect();
            foreach ($customers->take(3) as $customer) {
                $reservation = Reservation::create([
                    'user_id' => $customer->user_id,
                    'table_id' => $table->table_id,
                    'reservation_time' => Carbon::now()->subDays(rand(1, 7)),
                    'duration' => 120,
                    'party_size' => rand(2, 6),
                    'status' => 'completed',
                    'special_requests' => 'حجز تجريبي',
                    'contact_phone' => $customer->phone,
                ]);
                $reservations->push($reservation);
            }
        }

        // تقييمات تجريبية للحجوزات
        $reviewComments = [
            [
                'rating' => 5,
                'comment' => 'تجربة رائعة! الخدمة ممتازة والأجواء مريحة جداً. أنصح بالحجز هنا.'
            ],
            [
                'rating' => 4,
                'comment' => 'مكان جميل وطعام لذيذ. الموظفون ودودون والخدمة سريعة.'
            ],
            [
                'rating' => 5,
                'comment' => 'أفضل مطعم في المدينة! الديكور أنيق والطعام شهي. سأعود بالتأكيد.'
            ],
            [
                'rating' => 4,
                'comment' => 'خدمة ممتازة وطعام شهي. المكان نظيف والأسعار معقولة.'
            ],
            [
                'rating' => 5,
                'comment' => 'تجربة لا تُنسى! كل شيء كان مثالياً من الاستقبال حتى الوداع.'
            ],
            [
                'rating' => 3,
                'comment' => 'الطعام جيد لكن الخدمة كانت بطيئة قليلاً. المكان جميل.'
            ]
        ];

        // إنشاء التقييمات
        foreach ($reservations->take(6) as $index => $reservation) {
            $customer = $customers->get($index % $customers->count());
            $reviewData = $reviewComments[$index % count($reviewComments)];
            
            ReservationReview::create([
                'user_id' => $customer->user_id,
                'reservation_id' => $reservation->reservation_id,
                'rating' => $reviewData['rating'],
                'comment' => $reviewData['comment'],
                'is_approved' => true,
                'created_at' => Carbon::now()->subDays(rand(0, 5)),
                'updated_at' => Carbon::now()->subDays(rand(0, 5)),
            ]);
        }

        $this->command->info('تم إنشاء تقييمات الحجوزات التجريبية بنجاح!');
    }
}
