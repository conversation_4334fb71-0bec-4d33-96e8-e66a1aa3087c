// مخططات التقرير المالي مع دعم الوضع المظلم
class FinancialCharts {
    constructor(monthlyData, expensesByCategory) {
        this.monthlyData = monthlyData;
        this.expensesByCategory = expensesByCategory;
        this.salesExpensesChart = null;
        this.expensesDistChart = null;
        this.init();
    }

    init() {
        // انتظار تحميل مدير الوضع المظلم
        if (typeof window.darkModeManager !== 'undefined') {
            this.initCharts();
        } else {
            setTimeout(() => this.init(), 100);
        }
    }

    initCharts() {
        this.initSalesExpensesChart();
        this.initExpensesDistChart();
        
        // تسجيل المخططات مع مدير الوضع المظلم
        window.registerChart('salesExpensesChart', () => this.initSalesExpensesChart());
        window.registerChart('expensesDistChart', () => this.initExpensesDistChart());
        
        console.log('✅ تم تحميل مخططات التقرير المالي');
    }

    createSalesExpensesChart() {
        const colors = window.getThemeColors();
        const themeSettings = window.getThemeSettings();

        return {
            series: [{
                name: 'المبيعات',
                data: this.monthlyData.map(item => item.sales)
            }, {
                name: 'تكلفة المواد',
                data: this.monthlyData.map(item => item.cogs)
            }, {
                name: 'المصروفات التشغيلية',
                data: this.monthlyData.map(item => item.expenses)
            }, {
                name: 'صافي الربح',
                data: this.monthlyData.map(item => item.net_profit)
            }],
            chart: {
                height: 320,
                type: 'bar',
                fontFamily: 'Cairo, sans-serif',
                background: 'transparent',
                toolbar: {
                    show: false
                }
            },
            ...themeSettings,
            colors: [colors.success, colors.warning, colors.danger, colors.info],
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: '55%',
                    endingShape: 'rounded'
                }
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                show: true,
                width: 2,
                colors: ['transparent']
            },
            xaxis: {
                ...themeSettings.xaxis,
                categories: this.monthlyData.map(item => item.month_name)
            },
            yaxis: {
                ...themeSettings.yaxis,
                labels: {
                    ...themeSettings.yaxis.labels,
                    formatter: function(value) {
                        return value.toFixed(0) + ' د.ل';
                    }
                }
            },
            fill: {
                opacity: 1
            },
            tooltip: {
                ...themeSettings.tooltip,
                y: {
                    formatter: function(value) {
                        return value.toFixed(2) + ' د.ل';
                    }
                }
            }
        };
    }

    createExpensesDistChart() {
        const colors = window.getThemeColors();
        const themeSettings = window.getThemeSettings();

        return {
            series: this.expensesByCategory.map(item => item.total),
            chart: {
                height: 320,
                type: 'donut',
                fontFamily: 'Cairo, sans-serif',
                background: 'transparent',
                toolbar: {
                    show: false
                }
            },
            ...themeSettings,
            labels: this.expensesByCategory.map(item => item.category_name || 'غير محدد'),
            colors: [colors.info, colors.success, colors.warning, colors.danger, colors.purple],
            legend: {
                position: 'bottom',
                fontFamily: 'Cairo, sans-serif',
                labels: {
                    colors: colors.text
                }
            },
            plotOptions: {
                pie: {
                    donut: {
                        size: '65%'
                    }
                }
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 300
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }],
            tooltip: {
                ...themeSettings.tooltip,
                y: {
                    formatter: function(value) {
                        return value.toFixed(2) + ' د.ل';
                    }
                }
            }
        };
    }

    initSalesExpensesChart() {
        if (document.getElementById('salesExpensesChart')) {
            if (this.salesExpensesChart) {
                this.salesExpensesChart.destroy();
            }
            const chartOptions = this.createSalesExpensesChart();
            this.salesExpensesChart = new ApexCharts(document.getElementById('salesExpensesChart'), chartOptions);
            this.salesExpensesChart.render();
            console.log('✅ تم إنشاء مخطط المبيعات والمصروفات');
        }
    }

    initExpensesDistChart() {
        if (document.getElementById('expensesDistributionChart')) {
            if (this.expensesDistChart) {
                this.expensesDistChart.destroy();
            }
            const chartOptions = this.createExpensesDistChart();
            this.expensesDistChart = new ApexCharts(document.getElementById('expensesDistributionChart'), chartOptions);
            this.expensesDistChart.render();
            console.log('✅ تم إنشاء مخطط توزيع المصروفات');
        }
    }

    // دوال تحديث المخططات
    updateSalesChart() {
        const period = document.getElementById('salesPeriodFilter').value;
        
        // مؤشر تحميل
        const chartElement = document.getElementById('salesExpensesChart');
        chartElement.innerHTML = `
            <div class="flex items-center justify-center h-80">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <span class="mr-3 text-gray-600 dark:text-gray-400">جاري التحديث...</span>
            </div>
        `;

        fetch(`/admin/reports/financial-chart-data?period=${period}&type=sales`)
            .then(response => response.json())
            .then(data => {
                this.monthlyData = data;
                this.initSalesExpensesChart();
            })
            .catch(error => {
                console.error('خطأ في تحديث مخطط المبيعات:', error);
                chartElement.innerHTML = `
                    <div class="flex items-center justify-center h-80 text-red-500">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <span>حدث خطأ في تحديث المخطط</span>
                    </div>
                `;
            });
    }

    updateExpensesChart() {
        const period = document.getElementById('expensesPeriodFilter').value;
        
        // مؤشر تحميل
        const chartElement = document.getElementById('expensesDistributionChart');
        chartElement.innerHTML = `
            <div class="flex items-center justify-center h-80">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <span class="mr-3 text-gray-600 dark:text-gray-400">جاري التحديث...</span>
            </div>
        `;

        fetch(`/admin/reports/financial-chart-data?period=${period}&type=expenses`)
            .then(response => response.json())
            .then(data => {
                this.expensesByCategory = data;
                this.initExpensesDistChart();
            })
            .catch(error => {
                console.error('خطأ في تحديث مخطط المصروفات:', error);
                chartElement.innerHTML = `
                    <div class="flex items-center justify-center h-80 text-red-500">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <span>حدث خطأ في تحديث المخطط</span>
                    </div>
                `;
            });
    }
}

// تصدير الكلاس للاستخدام العام
window.FinancialCharts = FinancialCharts;
