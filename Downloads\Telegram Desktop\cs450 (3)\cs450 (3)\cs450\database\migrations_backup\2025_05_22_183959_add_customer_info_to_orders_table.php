<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // إضافة حقول معلومات العميل للزبائن غير المسجلين
            $table->string('customer_name', 100)->nullable()->after('user_id');
            $table->string('customer_phone', 20)->nullable()->after('customer_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // إزالة حقول معلومات العميل
            $table->dropColumn(['customer_name', 'customer_phone']);
        });
    }
};
