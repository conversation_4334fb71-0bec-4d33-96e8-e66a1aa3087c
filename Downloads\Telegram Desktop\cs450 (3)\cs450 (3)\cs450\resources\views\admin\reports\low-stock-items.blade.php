@extends('layouts.admin')

@section('title', 'المنتجات التي تحتاج تجديد المخزون')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800 dark:text-white">
                <i class="fas fa-exclamation-triangle text-yellow-500 ml-2"></i>
                المنتجات التي تحتاج تجديد المخزون
            </h1>
            <a href="{{ route('admin.inventory.index') }}" class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-lg transition">
                <i class="fas fa-warehouse ml-1"></i>إدارة المخزون
            </a>
        </div>

        @if($unavailableItems->count() > 0)
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white dark:bg-gray-800">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            المنتج
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الفئة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            المكونات الناقصة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الحالة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($unavailableItems as $item)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <img class="h-10 w-10 rounded-full object-cover" 
                                         src="{{ $item->image_path ?? 'https://images.unsplash.com/photo-1513104890138-7c749659a591?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80' }}" 
                                         alt="{{ $item->name }}">
                                </div>
                                <div class="mr-4">
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                                        {{ $item->name }}
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ number_format($item->price, 2) }} د.ل
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            @switch($item->category)
                                @case('main')
                                    الأطباق الرئيسية
                                    @break
                                @case('appetizer')
                                    المقبلات
                                    @break
                                @case('dessert')
                                    الحلويات
                                    @break
                                @case('beverage')
                                    المشروبات
                                    @break
                                @default
                                    {{ $item->category }}
                            @endswitch
                        </td>
                        <td class="px-6 py-4">
                            @php
                                $missingIngredients = $item->getMissingIngredients();
                            @endphp
                            @if(count($missingIngredients) > 0)
                                <div class="space-y-1">
                                    @foreach($missingIngredients as $missing)
                                    <div class="text-sm">
                                        <span class="font-medium text-red-600 dark:text-red-400">{{ $missing['ingredient']->name }}</span>
                                        <span class="text-gray-500 dark:text-gray-400">
                                            (نقص: {{ $missing['missing'] }} {{ $missing['ingredient']->unit }})
                                        </span>
                                    </div>
                                    @endforeach
                                </div>
                            @else
                                <span class="text-green-600 dark:text-green-400">جميع المكونات متوفرة</span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @if($item->is_available && $item->hasAvailableIngredients())
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    متوفر
                                </span>
                            @elseif($item->is_available)
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                    مكونات ناقصة
                                </span>
                            @else
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                    غير متوفر
                                </span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{{ route('admin.menu.edit', $item->item_id) }}" 
                               class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 ml-3">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @else
        <div class="text-center py-12">
            <i class="fas fa-check-circle text-6xl text-green-500 mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">
                جميع المنتجات متوفرة!
            </h3>
            <p class="text-gray-500 dark:text-gray-500">
                جميع المنتجات في القائمة لديها مكونات كافية في المخزون
            </p>
        </div>
        @endif
    </div>
</div>
@endsection
