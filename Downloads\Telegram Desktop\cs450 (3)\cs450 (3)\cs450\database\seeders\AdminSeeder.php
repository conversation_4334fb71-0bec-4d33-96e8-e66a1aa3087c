<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    public function run(): void
    {
        // إنشاء الأدوار
        Role::firstOrCreate(['name' => 'admin']);
        Role::firstOrCreate(['name' => 'customer']);
        Role::firstOrCreate(['name' => 'employee']);

        // إنشاء مدير النظام
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'مدير',
                'last_name' => 'النظام',
                'password' => Hash::make('admin123'),
                'phone' => '0912345678',
                'user_type' => 'admin',
                'is_active' => true
            ]
        );
        
        $admin->assignRole('admin');

        echo "تم إنشاء المدير: <EMAIL> / admin123\n";
    }
}
