<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Eat Hub - <?php echo $__env->yieldContent('title'); ?></title>

    <!-- تطبيق الوضع المظلم فوراً لمنع الوميض -->
    <script>
        (function() {
            const savedTheme = localStorage.getItem('darkMode');
            if (savedTheme === 'true' || savedTheme === 'enabled') {
                document.documentElement.classList.add('dark');
            } else if (savedTheme === 'false' || savedTheme === 'disabled') {
                document.documentElement.classList.remove('dark');
            } else {
                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                    document.documentElement.classList.add('dark');
                    localStorage.setItem('darkMode', 'true');
                } else {
                    localStorage.setItem('darkMode', 'false');
                }
            }
        })();
    </script>

    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#FF6B35',
                        secondary: '#4CAF50',
                        accent: '#FFEB3B',
                        warmBrown: '#C8A080',
                        darkText: '#333333',
                    },
                    fontFamily: {
                        sans: ['Cairo', 'sans-serif'],
                    },
                }
            }
        };
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Cairo', sans-serif;
        }
        
        .auth-bg {
            background-image: url('https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-1.2.1&auto=format&fit=crop&w=1567&q=80');
            background-size: cover;
            background-position: center;
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .auth-form-container {
            backdrop-filter: blur(8px);
            background-color: rgba(255, 255, 255, 0.9);
        }
        
        .dark .auth-form-container {
            background-color: rgba(30, 41, 59, 0.9);
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 min-h-screen">
    <div class="flex min-h-screen">
        <!-- Left Side - Authentication Form -->
        <div class="w-full lg:w-1/2 flex items-center justify-center p-4">
            <?php echo $__env->yieldContent('content'); ?>
        </div>
        
        <!-- Right Side - Image and Text -->
        <div class="hidden lg:block lg:w-1/2 auth-bg relative">
            <div class="absolute inset-0 bg-gradient-to-b from-primary/80 to-primary/40 flex flex-col justify-center items-center text-white p-12">
                <div class="max-w-md text-center">
                    <h2 class="text-4xl font-bold mb-6">مرحباً بك في Eat Hub</h2>
                    <p class="text-xl mb-8">استمتع بتجربة طعام فريدة مع أشهى المأكولات والمشروبات</p>
                    <div class="space-y-6">
                        <div class="flex items-center">
                            <div class="bg-white/20 p-2 rounded-full mr-4">
                                <i class="fas fa-utensils text-2xl"></i>
                            </div>
                            <p class="text-left">قائمة متنوعة من الأطباق العالمية والمحلية</p>
                        </div>
                        <div class="flex items-center">
                            <div class="bg-white/20 p-2 rounded-full mr-4">
                                <i class="fas fa-shopping-cart text-2xl"></i>
                            </div>
                            <p class="text-left">سهولة في طلب وتتبع الطعام</p>
                        </div>
                        <div class="flex items-center">
                            <div class="bg-white/20 p-2 rounded-full mr-4">
                                <i class="fas fa-calendar-check text-2xl"></i>
                            </div>
                            <p class="text-left">إمكانية حجز طاولة في أي وقت</p>
                        </div>
                    </div>
                </div>
                
                <div class="absolute bottom-4 left-4 flex items-center">
                    <button id="darkModeToggle" class="p-2 rounded-full bg-white/10 text-white hover:bg-white/20">
                        <i id="darkModeIcon" class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- النظام الموحد للوضع المظلم -->
    <script src="<?php echo e(asset('js/unified-dark-mode.js')); ?>"></script>
    <script>
        // ربط زر الوضع المظلم بالنظام الموحد
        document.addEventListener('DOMContentLoaded', function() {
            const darkModeToggle = document.getElementById('darkModeToggle');
            if (darkModeToggle) {
                darkModeToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (typeof toggleUnifiedDarkMode === 'function') {
                        toggleUnifiedDarkMode();
                    }
                });
            }
        });
    </script>
    
    <?php echo $__env->yieldContent('scripts'); ?>
</body>
</html><?php /**PATH C:\Users\<USER>\Downloads\Telegram Desktop\cs450 (3)\cs450 (3)\cs450\resources\views/auth/layouts/auth.blade.php ENDPATH**/ ?>