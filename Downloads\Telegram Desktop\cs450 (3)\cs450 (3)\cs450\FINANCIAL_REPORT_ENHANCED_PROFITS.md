# 💰 تم تحسين التقرير المالي ليظهر الأرباح الصافية بدقة عالية!

## 🎯 التحسينات المطبقة:

### 1. 🧮 حساب الأرباح الدقيق:

#### أ) إجمالي الربح (Gross Profit):
```php
// حساب تكلفة المواد الخام للمنتجات المباعة (COGS)
$costOfGoodsSold = $this->calculateCostOfGoodsSold($startDate, $endDate);

// حساب إجمالي الربح (المبيعات - تكلفة المواد)
$grossProfit = $totalSales - $costOfGoodsSold;

// حساب هامش الربح الإجمالي
$grossProfitMargin = $totalSales > 0 ? ($grossProfit / $totalSales) * 100 : 0;
```

#### ب) صافي الربح (Net Profit):
```php
// حساب صافي الربح (إجمالي الربح - المصروفات التشغيلية)
$netProfit = $grossProfit - $totalExpenses;

// حساب هامش الربح الصافي
$netProfitMargin = $totalSales > 0 ? ($netProfit / $totalSales) * 100 : 0;
```

### 2. 🔧 دوال حساب التكلفة:

#### دالة حساب تكلفة المواد الخام (COGS):
```php
private function calculateCostOfGoodsSold($startDate, $endDate)
{
    $totalCOGS = 0;

    // جلب جميع المنتجات المباعة في الفترة المحددة
    $soldItems = OrderItem::whereHas('order', function($query) use ($startDate, $endDate) {
        $query->whereBetween('created_at', [$startDate, $endDate])
              ->whereIn('status', ['completed', 'preparing']);
    })->get();

    foreach ($soldItems as $item) {
        $productCost = $this->calculateProductCost($item->menu_item_id);
        $totalCOGS += $productCost * $item->quantity;
    }

    return $totalCOGS;
}
```

#### دالة حساب تكلفة منتج واحد:
```php
private function calculateProductCost($menuItemId)
{
    $totalCost = 0;

    // جلب جميع المكونات المطلوبة للمنتج من جدول الوصفات
    $recipes = DB::table('recipes')
        ->join('inventory', 'recipes.ingredient_id', '=', 'inventory.ingredient_id')
        ->where('recipes.menu_item_id', $menuItemId)
        ->select('recipes.quantity', 'inventory.cost_per_unit')
        ->get();

    foreach ($recipes as $recipe) {
        $totalCost += $recipe->quantity * $recipe->cost_per_unit;
    }

    return $totalCost;
}
```

### 3. 📊 البطاقات الإحصائية المحسنة:

#### 6 بطاقات شاملة:
1. **إجمالي المبيعات** - المبلغ الإجمالي للمبيعات المؤكدة
2. **تكلفة المواد الخام (COGS)** - تكلفة المكونات المستخدمة
3. **إجمالي الربح** - المبيعات - تكلفة المواد + هامش الربح الإجمالي
4. **المصروفات التشغيلية** - الرواتب، المرافق، الصيانة، إلخ
5. **صافي الربح** - الربح النهائي بعد خصم جميع التكاليف + هامش الربح الصافي
6. **متوسط قيمة الطلب** - متوسط قيمة الطلب الواحد

#### ملخص الربحية:
```blade
<div class="bg-gradient-to-r from-blue-50 to-green-50 dark:from-gray-800 dark:to-gray-700 rounded-lg p-6 mb-6">
    <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">ملخص الربحية</h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="text-center">
            <p class="text-sm text-gray-600 dark:text-gray-400">هامش الربح الإجمالي</p>
            <p class="text-2xl font-bold {{ $grossProfitMargin >= 0 ? 'text-blue-600' : 'text-red-600' }}">
                {{ number_format($grossProfitMargin, 1) }}%
            </p>
            <p class="text-xs text-gray-500">المبيعات - تكلفة المواد</p>
        </div>
        <div class="text-center">
            <p class="text-sm text-gray-600 dark:text-gray-400">هامش الربح الصافي</p>
            <p class="text-2xl font-bold {{ $netProfitMargin >= 0 ? 'text-green-600' : 'text-red-600' }}">
                {{ number_format($netProfitMargin, 1) }}%
            </p>
            <p class="text-xs text-gray-500">بعد خصم جميع المصروفات</p>
        </div>
        <div class="text-center">
            <p class="text-sm text-gray-600 dark:text-gray-400">نسبة المصروفات</p>
            <p class="text-2xl font-bold text-orange-600">
                {{ $totalSales > 0 ? number_format(($totalExpenses / $totalSales) * 100, 1) : 0 }}%
            </p>
            <p class="text-xs text-gray-500">من إجمالي المبيعات</p>
        </div>
    </div>
</div>
```

### 4. 📈 المخططات المحسنة:

#### مخطط المبيعات والأرباح (4 خطوط):
```javascript
const salesExpensesOptions = {
    series: [{
        name: 'المبيعات',
        data: monthlyData.map(item => item.sales)
    }, {
        name: 'تكلفة المواد',
        data: monthlyData.map(item => item.cogs)
    }, {
        name: 'المصروفات التشغيلية',
        data: monthlyData.map(item => item.expenses)
    }, {
        name: 'صافي الربح',
        data: monthlyData.map(item => item.net_profit)
    }],
    colors: ['#10b981', '#f59e0b', '#ef4444', '#3b82f6'],
    // ...
};
```

### 5. 📋 جدول أفضل المنتجات المحسن:

#### أعمدة الجدول الجديدة:
- **المنتج** - اسم المنتج
- **الكمية** - الكمية المباعة
- **المبيعات** - إجمالي المبيعات
- **التكلفة** - تكلفة المواد الخام
- **الربح** - الربح الصافي للمنتج
- **هامش الربح** - نسبة الربح مع ألوان تحذيرية

#### حساب ربحية المنتجات:
```php
->map(function($product) use ($startDate, $endDate) {
    // حساب تكلفة المنتج
    $product->cost_per_unit = $this->calculateProductCost($product->menu_item_id);
    $product->total_cost = $product->total_quantity * $product->cost_per_unit;
    $product->profit = $product->total_revenue - $product->total_cost;
    $product->profit_margin = $product->total_revenue > 0 ? 
        ($product->profit / $product->total_revenue) * 100 : 0;
    return $product;
});
```

#### ألوان هامش الربح:
```blade
<span class="px-2 py-1 text-xs rounded-full {{ $product->profit_margin >= 30 ? 'bg-green-100 text-green-800' : ($product->profit_margin >= 15 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
    {{ number_format($product->profit_margin, 1) }}%
</span>
```

### 6. 👥 أداء الموظفين المحسن:

#### إصلاح العمود المرجعي:
```php
// تم تغيير من created_by إلى user_id
$employeePerformance = Order::whereBetween('created_at', [$startDate, $endDate])
    ->whereNotNull('user_id')  // ✅ العمود الصحيح
    ->whereIn('status', ['completed', 'preparing'])
    ->select('user_id', 
            DB::raw('COUNT(*) as orders_count'), 
            DB::raw('SUM(total_amount) as total_sales'),
            DB::raw('AVG(total_amount) as avg_order_value'))
    ->with('user')  // ✅ العلاقة الصحيحة
    ->groupBy('user_id')
    ->orderBy('total_sales', 'desc')
    ->limit(10)
    ->get();
```

### 7. 📊 إحصائيات شاملة:

#### إحصائيات مفصلة:
```php
$stats = [
    'total_orders' => Order::whereBetween('created_at', [$startDate, $endDate])->count(),
    'completed_orders' => Order::whereBetween('created_at', [$startDate, $endDate])
        ->where('status', 'completed')->count(),
    'preparing_orders' => Order::whereBetween('created_at', [$startDate, $endDate])
        ->where('status', 'preparing')->count(),
    'pending_orders' => Order::whereBetween('created_at', [$startDate, $endDate])
        ->where('status', 'pending')->count(),
    'cancelled_orders' => Order::whereBetween('created_at', [$startDate, $endDate])
        ->where('status', 'canceled')->count(),
    'unique_customers' => Order::whereBetween('created_at', [$startDate, $endDate])
        ->whereNotNull('user_id')
        ->distinct('user_id')->count('user_id'),
    'guest_orders' => Order::whereBetween('created_at', [$startDate, $endDate])
        ->whereNull('user_id')->count(),
];
```

## 🎯 المميزات الجديدة:

### 1. دقة الحسابات:
- ✅ **حساب تكلفة المواد الخام** من جداول الوصفات والمخزون
- ✅ **إجمالي الربح** = المبيعات - تكلفة المواد
- ✅ **صافي الربح** = إجمالي الربح - المصروفات التشغيلية
- ✅ **هوامش الربح** بالنسب المئوية

### 2. التحليل المتقدم:
- ✅ **ربحية كل منتج** على حدة
- ✅ **هوامش الربح** مع ألوان تحذيرية
- ✅ **تكلفة المواد الخام** لكل منتج
- ✅ **تحليل الاتجاهات** الشهرية

### 3. الواجهة المحسنة:
- ✅ **6 بطاقات إحصائية** شاملة
- ✅ **ملخص الربحية** مع النسب
- ✅ **ألوان ذكية** للأرباح والخسائر
- ✅ **مخططات متقدمة** بـ 4 خطوط

### 4. البيانات الدقيقة:
- ✅ **الطلبات المؤكدة فقط** (completed + preparing)
- ✅ **استبعاد الطلبات الملغية**
- ✅ **حساب التكاليف الحقيقية**
- ✅ **ربط الجداول بشكل صحيح**

## 🚀 كيفية قراءة التقرير:

### 1. البطاقات الإحصائية:
- **إجمالي المبيعات**: المبلغ الإجمالي للمبيعات
- **تكلفة المواد الخام**: تكلفة المكونات المستخدمة
- **إجمالي الربح**: الربح قبل المصروفات التشغيلية
- **المصروفات التشغيلية**: الرواتب، المرافق، الصيانة
- **صافي الربح**: الربح النهائي (أخضر = ربح، أحمر = خسارة)
- **متوسط قيمة الطلب**: متوسط قيمة الطلب الواحد

### 2. ملخص الربحية:
- **هامش الربح الإجمالي**: نسبة الربح قبل المصروفات التشغيلية
- **هامش الربح الصافي**: نسبة الربح النهائي
- **نسبة المصروفات**: نسبة المصروفات من المبيعات

### 3. جدول أفضل المنتجات:
- **أخضر**: هامش ربح ممتاز (30%+)
- **أصفر**: هامش ربح جيد (15-30%)
- **أحمر**: هامش ربح ضعيف (أقل من 15%)

## 💡 نصائح للاستفادة:

### 1. مراقبة الربحية:
- راقب **هامش الربح الصافي** - يجب أن يكون إيجابياً
- تحقق من **نسبة المصروفات** - يجب ألا تتجاوز 70%
- راجع **المنتجات ذات الهامش الضعيف** وحسن أسعارها

### 2. تحسين الأداء:
- ركز على **المنتجات عالية الربحية**
- قلل **تكلفة المواد الخام** بتحسين الوصفات
- راقب **المصروفات التشغيلية** وقللها إن أمكن

### 3. اتخاذ القرارات:
- استخدم **فلتر التاريخ** لمقارنة الفترات
- راجع **الاتجاهات الشهرية** في المخططات
- حلل **أداء الموظفين** وحفزهم

**🎉 الآن التقرير المالي يظهر الأرباح الصافية بدقة عالية ويساعد في اتخاذ قرارات مالية صحيحة! 💰📈**
