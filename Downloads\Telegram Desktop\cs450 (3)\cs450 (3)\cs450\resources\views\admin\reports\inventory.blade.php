@extends('layouts.admin')

@section('title', 'تقرير المخزون')

@section('content')
<div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <div>
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white">📦 تقرير المخزون</h2>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">حالة المخزون والمنتجات منخفضة المخزون</p>
    </div>
    <div class="mt-4 md:mt-0 flex space-x-2 space-x-reverse">
        <!-- أزرار التصدير والطباعة -->
        <button onclick="window.open('{{ route('admin.reports.export.inventory') }}', '_blank')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center transition-all">
            <i class="fas fa-download mr-2"></i>
            تصدير
        </button>
        <button onclick="window.print()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center transition-all">
            <i class="fas fa-print mr-2"></i>
            طباعة
        </button>
        <a href="{{ route('admin.reports') }}" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-arrow-right ml-2"></i>
            <span>العودة للتقارير</span>
        </a>
    </div>
</div>

<!-- فلاتر التقرير -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">🔍 فلاتر التقرير</h3>
    <form method="GET" action="{{ route('admin.reports.inventory') }}" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- فترة زمنية -->
        <div>
            <label for="period" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">📅 الفترة الزمنية</label>
            <select id="period" name="period" onchange="toggleCustomDates(this.value)" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                <option value="all" {{ request('period') == 'all' ? 'selected' : '' }}>🌐 جميع الفترات</option>
                <option value="today" {{ request('period') == 'today' ? 'selected' : '' }}>📅 اليوم</option>
                <option value="week" {{ request('period') == 'week' ? 'selected' : '' }}>📊 هذا الأسبوع</option>
                <option value="month" {{ request('period') == 'month' ? 'selected' : '' }}>📈 هذا الشهر</option>
                <option value="quarter" {{ request('period') == 'quarter' ? 'selected' : '' }}>📋 هذا الربع</option>
                <option value="year" {{ request('period') == 'year' ? 'selected' : '' }}>📆 هذا العام</option>
                <option value="custom" {{ request('period') == 'custom' ? 'selected' : '' }}>🎯 فترة مخصصة</option>
            </select>
        </div>

        <!-- حالة المخزون -->
        <div>
            <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">📊 حالة المخزون</label>
            <select id="status" name="status" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                <option value="all" {{ request('status') == 'all' ? 'selected' : '' }}>🌐 جميع الحالات</option>
                <option value="in_stock" {{ request('status') == 'in_stock' ? 'selected' : '' }}>✅ متوفر</option>
                <option value="low_stock" {{ request('status') == 'low_stock' ? 'selected' : '' }}>⚠️ منخفض المخزون</option>
                <option value="out_of_stock" {{ request('status') == 'out_of_stock' ? 'selected' : '' }}>❌ غير متوفر</option>
            </select>
        </div>

        <!-- البحث -->
        <div>
            <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">🔍 البحث</label>
            <input type="text" id="search" name="search" value="{{ request('search') }}" placeholder="اسم المكون..." class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
        </div>

        <!-- زر التطبيق -->
        <div class="flex items-end">
            <button type="submit" class="w-full bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-md transition-all">
                <i class="fas fa-search ml-1"></i>
                <span>تطبيق الفلتر</span>
            </button>
        </div>

        <!-- التواريخ المخصصة -->
        <div id="custom_dates" class="grid grid-cols-1 md:grid-cols-2 gap-4 md:col-span-4" style="{{ request('period') == 'custom' ? '' : 'display: none;' }}">
            <div>
                <label for="custom_start" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">📅 من تاريخ</label>
                <input type="date" id="custom_start" name="start_date" value="{{ request('start_date') }}" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
            </div>
            <div>
                <label for="custom_end" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">📅 إلى تاريخ</label>
                <input type="date" id="custom_end" name="end_date" value="{{ request('end_date') }}" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
            </div>
        </div>
    </form>
</div>

<!-- ملخص المخزون المحسن -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border-l-4 border-blue-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي المكونات</p>
                <p class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ $totalIngredients }}</p>
                <p class="text-xs text-gray-500 mt-1">مكون مختلف</p>
            </div>
            <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3">
                <i class="fas fa-boxes text-blue-500 text-2xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border-l-4 border-green-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">المكونات المتوفرة</p>
                <p class="text-3xl font-bold text-green-600 dark:text-green-400">{{ $inStockCount }}</p>
                <p class="text-xs text-gray-500 mt-1">في المخزون</p>
            </div>
            <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3">
                <i class="fas fa-check-circle text-green-500 text-2xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border-l-4 border-yellow-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">منخفضة المخزون</p>
                <p class="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{{ $lowStockCount }}</p>
                <p class="text-xs text-gray-500 mt-1">تحتاج إعادة تموين</p>
            </div>
            <div class="rounded-full bg-yellow-100 dark:bg-yellow-900/30 p-3">
                <i class="fas fa-exclamation-triangle text-yellow-500 text-2xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border-l-4 border-red-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">غير متوفرة</p>
                <p class="text-3xl font-bold text-red-600 dark:text-red-400">{{ $outOfStockCount }}</p>
                <p class="text-xs text-gray-500 mt-1">نفدت من المخزون</p>
            </div>
            <div class="rounded-full bg-red-100 dark:bg-red-900/30 p-3">
                <i class="fas fa-times-circle text-red-500 text-2xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- قيمة المخزون والإحصائيات المالية -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border-l-4 border-green-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي قيمة المخزون</p>
                <p class="text-3xl font-bold text-green-600 dark:text-green-400">{{ number_format($totalInventoryValue, 2) }}</p>
                <p class="text-xs text-gray-500 mt-1">دينار ليبي</p>
            </div>
            <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3">
                <i class="fas fa-money-bill-wave text-green-500 text-2xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border-l-4 border-purple-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">متوسط قيمة المكون</p>
                <p class="text-3xl font-bold text-purple-600 dark:text-purple-400">
                    {{ $totalIngredients > 0 ? number_format($totalInventoryValue / $totalIngredients, 2) : '0.00' }}
                </p>
                <p class="text-xs text-gray-500 mt-1">دينار ليبي</p>
            </div>
            <div class="rounded-full bg-purple-100 dark:bg-purple-900/30 p-3">
                <i class="fas fa-calculator text-purple-500 text-2xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border-l-4 border-orange-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي عناصر المخزون</p>
                <p class="text-3xl font-bold text-orange-600 dark:text-orange-400">{{ \App\Models\Inventory::sum('quantity') }}</p>
                <p class="text-xs text-gray-500 mt-1">وحدة</p>
            </div>
            <div class="rounded-full bg-orange-100 dark:bg-orange-900/30 p-3">
                <i class="fas fa-warehouse text-orange-500 text-2xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- تنبيهات المخزون -->
@if($nearExpiryItems->count() > 0 || $lowStockCount > 0 || $outOfStockCount > 0)
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4 flex items-center">
        <i class="fas fa-bell text-yellow-500 ml-2"></i>
        تنبيهات المخزون
    </h3>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        @if($outOfStockCount > 0)
        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div class="flex items-center">
                <i class="fas fa-times-circle text-red-500 text-xl ml-3"></i>
                <div>
                    <p class="font-semibold text-red-800 dark:text-red-400">مكونات نفدت</p>
                    <p class="text-sm text-red-600 dark:text-red-300">{{ $outOfStockCount }} مكون نفد من المخزون</p>
                </div>
            </div>
        </div>
        @endif

        @if($lowStockCount > 0)
        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-yellow-500 text-xl ml-3"></i>
                <div>
                    <p class="font-semibold text-yellow-800 dark:text-yellow-400">مخزون منخفض</p>
                    <p class="text-sm text-yellow-600 dark:text-yellow-300">{{ $lowStockCount }} مكون يحتاج إعادة تموين</p>
                </div>
            </div>
        </div>
        @endif

        @if($nearExpiryItems->count() > 0)
        <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
            <div class="flex items-center">
                <i class="fas fa-clock text-orange-500 text-xl ml-3"></i>
                <div>
                    <p class="font-semibold text-orange-800 dark:text-orange-400">قريبة الانتهاء</p>
                    <p class="text-sm text-orange-600 dark:text-orange-300">{{ $nearExpiryItems->count() }} عنصر ينتهي خلال 30 يوم</p>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>
@endif

<!-- التحليلات والرسوم البيانية -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
    <!-- رسم بياني لحالة المخزون -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4 flex items-center">
            <i class="fas fa-chart-pie text-blue-500 ml-2"></i>
            توزيع حالة المخزون
        </h3>
        <div class="h-64">
            <canvas id="inventoryStatusChart"></canvas>
        </div>
    </div>

    <!-- أعلى 5 مكونات من حيث القيمة -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4 flex items-center">
            <i class="fas fa-trophy text-yellow-500 ml-2"></i>
            أعلى المكونات قيمة
        </h3>
        <div class="space-y-3">
            @foreach($topValueIngredients as $index => $ingredient)
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center">
                        <span class="w-6 h-6 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center ml-3">{{ $index + 1 }}</span>
                        <div>
                            <p class="font-semibold text-gray-800 dark:text-white">{{ $ingredient->name }}</p>
                            <p class="text-xs text-gray-500">{{ $ingredient->quantity }} {{ $ingredient->unit }}</p>
                        </div>
                    </div>
                    <div class="text-left">
                        <p class="font-bold text-green-600 dark:text-green-400">{{ number_format($ingredient->value, 2) }}</p>
                        <p class="text-xs text-gray-500">د.ل</p>
                    </div>
                </div>
            @endforeach
        </div>
    </div>

    <!-- المكونات القريبة من انتهاء الصلاحية -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4 flex items-center">
            <i class="fas fa-clock text-orange-500 ml-2"></i>
            قريبة الانتهاء
        </h3>
        @if($nearExpiryItems->count() > 0)
            <div class="space-y-3 max-h-64 overflow-y-auto">
                @foreach($nearExpiryItems->take(10) as $item)
                    <div class="flex items-center justify-between p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
                        <div>
                            <p class="font-semibold text-gray-800 dark:text-white">{{ $item->ingredient->name }}</p>
                            <p class="text-xs text-gray-500">{{ $item->quantity }} {{ $item->ingredient->unit }}</p>
                        </div>
                        <div class="text-left">
                            <p class="text-sm font-semibold text-orange-600 dark:text-orange-400">
                                {{ \Carbon\Carbon::parse($item->expiry_date)->diffForHumans() }}
                            </p>
                            <p class="text-xs text-gray-500">{{ \Carbon\Carbon::parse($item->expiry_date)->format('Y/m/d') }}</p>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-8">
                <i class="fas fa-check-circle text-green-500 text-3xl mb-2"></i>
                <p class="text-gray-500">لا توجد مكونات قريبة من انتهاء الصلاحية</p>
            </div>
        @endif
    </div>
</div>

<!-- قائمة المخزون المحسنة -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="mb-4 md:mb-0">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white flex items-center">
                    <i class="fas fa-list text-blue-500 ml-2"></i>
                    قائمة المخزون التفصيلية
                </h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">عرض تفصيلي لجميع مكونات المخزون</p>
            </div>

            <!-- فلاتر البحث والتصفية -->
            <div class="flex flex-col md:flex-row gap-4">
                <form action="{{ route('admin.reports.inventory') }}" method="GET" class="flex">
                    <input type="text" name="search" value="{{ request('search') }}" placeholder="بحث في المكونات..."
                           class="px-4 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 w-64">
                    <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-l-md hover:bg-blue-600 transition-all">
                        <i class="fas fa-search"></i>
                    </button>
                </form>


            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full">
            <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        <div class="flex items-center">
                            <i class="fas fa-box ml-2"></i>
                            المكون
                        </div>
                    </th>
                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        <div class="flex items-center">
                            <i class="fas fa-weight ml-2"></i>
                            الكمية المتوفرة
                        </div>
                    </th>
                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        <div class="flex items-center">
                            <i class="fas fa-ruler ml-2"></i>
                            وحدة القياس
                        </div>
                    </th>
                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        <div class="flex items-center">
                            <i class="fas fa-tag ml-2"></i>
                            سعر الوحدة
                        </div>
                    </th>
                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        <div class="flex items-center">
                            <i class="fas fa-calculator ml-2"></i>
                            القيمة الإجمالية
                        </div>
                    </th>
                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        <div class="flex items-center">
                            <i class="fas fa-info-circle ml-2"></i>
                            الحالة
                        </div>
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                @forelse($inventory as $item)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center ml-3">
                                    <i class="fas fa-cube text-blue-500"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold text-gray-800 dark:text-white">{{ $item->name }}</p>
                                    <p class="text-xs text-gray-500">مكون أساسي</p>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm">
                                <p class="font-semibold text-gray-800 dark:text-white">{{ number_format($item->quantity, 1) }}</p>
                                @if($item->quantity <= 0)
                                    <p class="text-xs text-red-500">نفد المخزون</p>
                                @elseif($item->quantity < 10)
                                    <p class="text-xs text-yellow-500">مخزون منخفض</p>
                                @else
                                    <p class="text-xs text-green-500">متوفر</p>
                                @endif
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">
                            <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-xs">{{ $item->unit }}</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <p class="font-semibold text-gray-800 dark:text-white">{{ number_format($item->unit_price, 2) }}</p>
                            <p class="text-xs text-gray-500">دينار ليبي</p>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <p class="font-bold text-green-600 dark:text-green-400">{{ number_format($item->quantity * $item->unit_price, 2) }}</p>
                            <p class="text-xs text-gray-500">دينار ليبي</p>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            @if($item->quantity <= 0)
                                <span class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400">
                                    <i class="fas fa-times-circle ml-1"></i>
                                    غير متوفر
                                </span>
                            @elseif($item->quantity < 10)
                                <span class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
                                    <i class="fas fa-exclamation-triangle ml-1"></i>
                                    منخفض
                                </span>
                            @else
                                <span class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                    <i class="fas fa-check-circle ml-1"></i>
                                    متوفر
                                </span>
                            @endif
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center">
                            <div class="flex flex-col items-center">
                                <i class="fas fa-box-open text-gray-400 text-4xl mb-4"></i>
                                <p class="text-gray-500 text-lg">لا توجد مكونات في المخزون</p>
                                <p class="text-gray-400 text-sm">قم بإضافة مكونات جديدة لبدء إدارة المخزون</p>
                            </div>
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    @if($inventory->hasPages())
    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        {{ $inventory->links() }}
    </div>
    @endif
</div>

<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
setTimeout(function() {
    console.log('Initializing chart...');

    // رسم بياني لحالة المخزون
    var statusCtx = document.getElementById('inventoryStatusChart');
    if (statusCtx && typeof Chart !== 'undefined') {
        var ctx = statusCtx.getContext('2d');

        // البيانات
        var inStockCount = {{ $inStockCount ?? 0 }};
        var lowStockCount = {{ $lowStockCount ?? 0 }};
        var outOfStockCount = {{ $outOfStockCount ?? 0 }};

        console.log('Chart data:', inStockCount, lowStockCount, outOfStockCount);

        var chart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['متوفر', 'منخفض', 'غير متوفر'],
                datasets: [{
                    data: [inStockCount, lowStockCount, outOfStockCount],
                    backgroundColor: [
                        '#22c55e',
                        '#fbbf24',
                        '#ef4444'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var label = context.label || '';
                                var value = context.parsed;
                                var total = context.dataset.data.reduce(function(a, b) { return a + b; }, 0);
                                var percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                                return label + ': ' + value + ' (' + percentage + '%)';
                            }
                        }
                    }
                },
                cutout: '60%'
            }
        });

        console.log('Chart created');
    } else {
        console.log('Chart.js not available or element not found');
    }
}, 1000);
</script>

<style>
    @media print {
        .no-print {
            display: none !important;
        }

        .print-only {
            display: block !important;
        }

        body {
            font-size: 12px;
        }

        .bg-white {
            background: white !important;
        }

        .text-gray-800 {
            color: black !important;
        }

        .border {
            border: 1px solid #ccc !important;
        }
    }
</style>

@endsection

<script>
// تبديل عرض التواريخ المخصصة
function toggleCustomDates(period) {
    const customDates = document.getElementById('custom_dates');
    if (period === 'custom') {
        customDates.style.display = 'grid';
    } else {
        customDates.style.display = 'none';
    }
}

// تحديث الصفحة عند تغيير الفترة الزمنية
document.getElementById('period').addEventListener('change', function() {
    if (this.value !== 'custom') {
        // إرسال النموذج تلقائياً عند اختيار فترة محددة مسبقاً
        this.form.submit();
    }
});
</script>

