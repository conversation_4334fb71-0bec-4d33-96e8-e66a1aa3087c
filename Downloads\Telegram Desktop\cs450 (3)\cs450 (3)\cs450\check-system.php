<?php

/**
 * فحص صحة النظام والتأكد من عمل جميع المكونات
 */

require_once 'vendor/autoload.php';

echo "🔍 فحص صحة النظام...\n";
echo "====================\n\n";

// تحميل Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

$errors = [];
$warnings = [];
$success = [];

// 1. فحص قاعدة البيانات
echo "📊 فحص قاعدة البيانات...\n";
try {
    $pdo = DB::connection()->getPdo();
    $success[] = "✅ الاتصال بقاعدة البيانات ناجح";
} catch (Exception $e) {
    $errors[] = "❌ فشل الاتصال بقاعدة البيانات: " . $e->getMessage();
}

// 2. فحص الجداول الأساسية
$requiredTables = [
    'users', 'menu_items', 'orders', 'tables', 'offers', 
    'cart', 'cart_offers', 'payments', 'reviews'
];

foreach ($requiredTables as $table) {
    try {
        DB::table($table)->count();
        $success[] = "✅ جدول $table موجود";
    } catch (Exception $e) {
        $errors[] = "❌ جدول $table مفقود أو تالف";
    }
}

// 3. فحص المستخدمين
echo "\n👥 فحص المستخدمين...\n";
try {
    $adminCount = App\Models\User::where('user_type', 'admin')->count();
    $customerCount = App\Models\User::where('user_type', 'customer')->count();
    
    if ($adminCount > 0) {
        $success[] = "✅ يوجد $adminCount مدير في النظام";
    } else {
        $warnings[] = "⚠️  لا يوجد مديرين في النظام";
    }
    
    if ($customerCount > 0) {
        $success[] = "✅ يوجد $customerCount عميل في النظام";
    } else {
        $warnings[] = "⚠️  لا يوجد عملاء في النظام";
    }
} catch (Exception $e) {
    $errors[] = "❌ خطأ في فحص المستخدمين: " . $e->getMessage();
}

// 4. فحص قائمة الطعام
echo "\n🍽️  فحص قائمة الطعام...\n";
try {
    $menuCount = App\Models\MenuItem::count();
    $availableCount = App\Models\MenuItem::where('is_available', true)->count();
    
    if ($menuCount > 0) {
        $success[] = "✅ يوجد $menuCount عنصر في قائمة الطعام";
        $success[] = "✅ $availableCount عنصر متاح للطلب";
    } else {
        $warnings[] = "⚠️  قائمة الطعام فارغة";
    }
} catch (Exception $e) {
    $errors[] = "❌ خطأ في فحص قائمة الطعام: " . $e->getMessage();
}

// 5. فحص الطاولات
echo "\n🪑 فحص الطاولات...\n";
try {
    $tableCount = App\Models\Table::count();
    $availableTables = App\Models\Table::where('status', 'available')->count();
    
    if ($tableCount > 0) {
        $success[] = "✅ يوجد $tableCount طاولة في النظام";
        $success[] = "✅ $availableTables طاولة متاحة";
    } else {
        $warnings[] = "⚠️  لا توجد طاولات في النظام";
    }
} catch (Exception $e) {
    $errors[] = "❌ خطأ في فحص الطاولات: " . $e->getMessage();
}

// 6. فحص العروض
echo "\n🎁 فحص العروض...\n";
try {
    $offerCount = App\Models\Offer::count();
    $activeOffers = App\Models\Offer::where('is_active', true)->count();
    
    if ($offerCount > 0) {
        $success[] = "✅ يوجد $offerCount عرض في النظام";
        $success[] = "✅ $activeOffers عرض نشط";
    } else {
        $warnings[] = "⚠️  لا توجد عروض في النظام";
    }
} catch (Exception $e) {
    $errors[] = "❌ خطأ في فحص العروض: " . $e->getMessage();
}

// 7. فحص التخزين
echo "\n📁 فحص التخزين...\n";
$storageDirs = [
    'storage/app/public',
    'storage/app/public/menu',
    'storage/app/public/offers',
    'public/storage'
];

foreach ($storageDirs as $dir) {
    if (is_dir($dir)) {
        $success[] = "✅ مجلد $dir موجود";
    } else {
        $warnings[] = "⚠️  مجلد $dir مفقود";
    }
}

// 8. فحص الصور الافتراضية
$defaultImages = [
    'public/images/default-food.svg',
    'public/images/default-offer.svg',
    'public/images/default-avatar.svg'
];

foreach ($defaultImages as $image) {
    if (file_exists($image)) {
        $success[] = "✅ صورة افتراضية موجودة: " . basename($image);
    } else {
        $warnings[] = "⚠️  صورة افتراضية مفقودة: " . basename($image);
    }
}

// 9. فحص الصلاحيات
echo "\n🔐 فحص الصلاحيات...\n";
try {
    if (class_exists('Spatie\Permission\Models\Role')) {
        $roleCount = Spatie\Permission\Models\Role::count();
        if ($roleCount > 0) {
            $success[] = "✅ يوجد $roleCount دور في النظام";
        } else {
            $warnings[] = "⚠️  لا توجد أدوار في النظام";
        }
    } else {
        $warnings[] = "⚠️  حزمة الصلاحيات غير مثبتة";
    }
} catch (Exception $e) {
    $warnings[] = "⚠️  خطأ في فحص الصلاحيات: " . $e->getMessage();
}

// عرض النتائج
echo "\n" . str_repeat("=", 50) . "\n";
echo "📋 تقرير فحص النظام\n";
echo str_repeat("=", 50) . "\n\n";

if (!empty($success)) {
    echo "✅ النجاحات (" . count($success) . "):\n";
    foreach ($success as $item) {
        echo "   $item\n";
    }
    echo "\n";
}

if (!empty($warnings)) {
    echo "⚠️  التحذيرات (" . count($warnings) . "):\n";
    foreach ($warnings as $item) {
        echo "   $item\n";
    }
    echo "\n";
}

if (!empty($errors)) {
    echo "❌ الأخطاء (" . count($errors) . "):\n";
    foreach ($errors as $item) {
        echo "   $item\n";
    }
    echo "\n";
}

// تقييم عام
$totalIssues = count($errors) + count($warnings);
if (count($errors) == 0) {
    if (count($warnings) == 0) {
        echo "🎉 النظام يعمل بشكل مثالي!\n";
    } else {
        echo "✅ النظام يعمل بشكل جيد مع بعض التحذيرات البسيطة\n";
    }
} else {
    echo "⚠️  النظام يحتاج إلى إصلاحات قبل الاستخدام\n";
}

echo "\n💡 نصائح:\n";
echo "   - إذا كانت هناك أخطاء، جرب تشغيل: php reset-database.php\n";
echo "   - للحصول على مساعدة، راجع ملف SETUP-GUIDE.md\n";
echo "   - تأكد من تشغيل: php artisan storage:link\n\n";
