<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Ingredient;

class IngredientSeeder extends Seeder
{
    public function run(): void
    {
        $ingredients = [
            ['name' => 'البصل', 'unit' => 'كيلو', 'cost_per_unit' => 2.50, 'is_active' => true],
            ['name' => 'الثوم', 'unit' => 'كيلو', 'cost_per_unit' => 8.00, 'is_active' => true],
            ['name' => 'الطماطم', 'unit' => 'كيلو', 'cost_per_unit' => 3.00, 'is_active' => true],
            ['name' => 'الجبن', 'unit' => 'كيلو', 'cost_per_unit' => 15.00, 'is_active' => true],
            ['name' => 'الفلفل الحار', 'unit' => 'كيلو', 'cost_per_unit' => 5.00, 'is_active' => true],
            ['name' => 'المايونيز', 'unit' => 'لتر', 'cost_per_unit' => 4.00, 'is_active' => true],
            ['name' => 'الخس', 'unit' => 'كيلو', 'cost_per_unit' => 2.00, 'is_active' => true],
            ['name' => 'الخيار', 'unit' => 'كيلو', 'cost_per_unit' => 1.50, 'is_active' => true],
            ['name' => 'الفطر', 'unit' => 'كيلو', 'cost_per_unit' => 6.00, 'is_active' => true],
            ['name' => 'الزيتون', 'unit' => 'كيلو', 'cost_per_unit' => 12.00, 'is_active' => true],
            ['name' => 'الفلفل الأخضر', 'unit' => 'كيلو', 'cost_per_unit' => 3.50, 'is_active' => true],
            ['name' => 'الجزر', 'unit' => 'كيلو', 'cost_per_unit' => 2.00, 'is_active' => true]
        ];

        foreach ($ingredients as $ingredient) {
            Ingredient::firstOrCreate(['name' => $ingredient['name']], $ingredient);
        }

        echo "تم إضافة " . count($ingredients) . " مكون تجريبي\n";
    }
}
