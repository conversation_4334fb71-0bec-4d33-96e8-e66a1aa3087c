<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class ArabicPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        echo "🇱🇾 إضافة صلاحيات باللغة العربية للإدارة...\n\n";

        // صلاحيات مباشرة باللغة العربية
        $arabicPermissions = [
            // إدارة النظام
            'عرض_لوحة_التحكم' => 'الوصول للوحة تحكم المدير',
            'إدارة_النظام' => 'إدارة إعدادات النظام العامة',
            'وضع_الصيانة' => 'تفعيل وإلغاء وضع الصيانة',
            'مسح_الذاكرة_المؤقتة' => 'مسح ذاكرة النظام المؤقتة',
            'عرض_سجلات_النظام' => 'عرض وتحليل سجلات النظام',
            'تحميل_السجلات' => 'تحميل ملفات سجلات النظام',
            'النسخ_الاحتياطي' => 'إنشاء نسخ احتياطية لقاعدة البيانات',
            'استعادة_البيانات' => 'استعادة البيانات من النسخ الاحتياطية',

            // إدارة المستخدمين
            'عرض_المستخدمين' => 'عرض قائمة جميع المستخدمين',
            'إضافة_مستخدمين' => 'إنشاء حسابات مستخدمين جديدة',
            'تعديل_المستخدمين' => 'تعديل بيانات المستخدمين',
            'حذف_المستخدمين' => 'حذف حسابات المستخدمين',
            'إدارة_الصلاحيات' => 'منح وإزالة صلاحيات المستخدمين',
            'تفعيل_المستخدمين' => 'تفعيل وإلغاء تفعيل الحسابات',
            'تصدير_بيانات_المستخدمين' => 'تصدير قوائم المستخدمين',

            // إدارة القائمة والطعام
            'عرض_القائمة' => 'عرض قائمة الطعام والمشروبات',
            'إضافة_أصناف' => 'إضافة أصناف جديدة للقائمة',
            'تعديل_الأصناف' => 'تعديل تفاصيل الأصناف',
            'حذف_الأصناف' => 'حذف أصناف من القائمة',
            'إدارة_الأسعار' => 'تعديل أسعار الأصناف',
            'إدارة_التوفر' => 'تحديد توفر الأصناف',
            'إدارة_الوصفات' => 'إدارة وصفات الطبخ والمكونات',

            // إدارة الطلبات
            'عرض_الطلبات' => 'عرض جميع طلبات العملاء',
            'إنشاء_طلبات' => 'إنشاء طلبات جديدة',
            'تعديل_الطلبات' => 'تعديل تفاصيل الطلبات',
            'إلغاء_الطلبات' => 'إلغاء الطلبات',
            'تحديث_حالة_الطلبات' => 'تغيير حالة الطلبات',
            'طباعة_الطلبات' => 'طباعة فواتير الطلبات',
            'تصدير_الطلبات' => 'تصدير بيانات الطلبات',

            // إدارة المخزون
            'عرض_المخزون' => 'عرض حالة المخزون',
            'إضافة_مخزون' => 'إضافة مواد للمخزون',
            'تعديل_المخزون' => 'تعديل كميات المخزون',
            'نقل_المخزون' => 'نقل المواد بين المخازن',
            'تسوية_المخزون' => 'تسوية كميات المخزون',
            'تقارير_المخزون' => 'عرض تقارير المخزون',

            // إدارة المدفوعات والمالية
            'عرض_المدفوعات' => 'عرض جميع المدفوعات',
            'إدارة_المدفوعات' => 'إدارة طرق الدفع',
            'عرض_التقارير_المالية' => 'عرض التقارير المالية',
            'تقارير_الضرائب' => 'إنشاء تقارير الضرائب',
            'تقارير_الربح_والخسارة' => 'عرض تقارير الربح والخسارة',
            'تقارير_التدفق_النقدي' => 'عرض تقارير التدفق النقدي',
            'تخطيط_الميزانية' => 'إدارة وتخطيط الميزانية',

            // إدارة الموظفين
            'عرض_الموظفين' => 'عرض قائمة الموظفين',
            'إدارة_جداول_العمل' => 'إنشاء وتعديل جداول العمل',
            'تقييم_الأداء' => 'تقييم أداء الموظفين',
            'إدارة_التدريب' => 'إدارة برامج التدريب',
            'إدارة_الرواتب' => 'إدارة رواتب الموظفين',

            // إدارة العملاء
            'عرض_العملاء' => 'عرض قائمة العملاء',
            'إدارة_برنامج_الولاء' => 'إدارة برنامج ولاء العملاء',
            'إدارة_التقييمات' => 'إدارة تقييمات العملاء',
            'التواصل_مع_العملاء' => 'إرسال رسائل للعملاء',

            // إدارة المطعم
            'إدارة_الطاولات' => 'إدارة طاولات المطعم',
            'إدارة_الحجوزات' => 'إدارة حجوزات العملاء',
            'إدارة_تخطيط_المطعم' => 'تعديل تخطيط المطعم',
            'إدارة_المعدات' => 'إدارة معدات المطعم',
            'مراقبة_الجودة' => 'مراقبة جودة الطعام والخدمة',

            // الأمان والحماية
            'عرض_سجل_التدقيق' => 'عرض سجل أنشطة المستخدمين',
            'إدارة_الجلسات' => 'إدارة جلسات تسجيل الدخول',
            'حظر_عناوين_IP' => 'حظر عناوين IP المشبوهة',
            'المصادقة_الثنائية' => 'إدارة المصادقة الثنائية',

            // التحليلات والتقارير
            'التحليلات_المتقدمة' => 'عرض التحليلات المتقدمة',
            'التحليلات_الفورية' => 'عرض التحليلات الفورية',
            'تصدير_التحليلات' => 'تصدير جميع التحليلات',
            'إنشاء_تقارير_مخصصة' => 'إنشاء تقارير مخصصة',

            // صلاحيات الطوارئ
            'الوصول_في_الطوارئ' => 'الوصول للنظام في حالات الطوارئ',
            'تجاوز_القيود' => 'تجاوز قيود النظام في الطوارئ',
            'إيقاف_النظام' => 'إيقاف النظام في حالات الطوارئ',

            // إدارة المحتوى
            'إدارة_الإعلانات' => 'إنشاء وإدارة الإعلانات',
            'إدارة_العروض' => 'إنشاء وإدارة العروض الترويجية',
            'إدارة_وسائل_التواصل' => 'إدارة حسابات وسائل التواصل',
        ];

        echo '1️⃣ إنشاء الصلاحيات العربية...\n';
        foreach ($arabicPermissions as $name => $description) {
            Permission::firstOrCreate([
                'name' => $name,
                'guard_name' => 'web'
            ]);
            echo '✅ ' . $description . '\n';
        }

        echo '\n2️⃣ إنشاء دور المدير العربي...\n';
        $arabicAdminRole = Role::firstOrCreate(['name' => 'مدير_عام', 'guard_name' => 'web']);
        $arabicAdminRole->givePermissionTo(Permission::all());
        echo '✅ تم إنشاء دور المدير العام\n';

        echo '\n3️⃣ تحديث صلاحيات المديرين...\n';
        $adminUsers = User::where('user_type', 'admin')->get();
        foreach ($adminUsers as $user) {
            // إعطاء الدور العربي
            $user->assignRole('مدير_عام');

            // إعطاء جميع الصلاحيات العربية
            $user->givePermissionTo(array_keys($arabicPermissions));

            echo '✅ تم تحديث: ' . $user->first_name . ' ' . $user->last_name . '\n';
        }

        echo '\n🎉 تم إنشاء نظام الصلاحيات العربي بنجاح!\n';
        echo '📊 إجمالي الصلاحيات: ' . Permission::count() . '\n';
        echo '👥 إجمالي الأدوار: ' . Role::count() . '\n';
    }
}
