@extends('customer.layouts.simple')

@section('title', 'حجز طاولة - Eat Hub')

@section('content')
<style>
.food-item-card {
    transition: all 0.3s ease;
}
.food-item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}
.quantity-controls button {
    transition: all 0.2s ease;
}
.quantity-controls button:hover {
    transform: scale(1.1);
}
.order-summary-item {
    animation: slideIn 0.3s ease;
}
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
</style>

<!-- المحتوى الرئيسي -->
<main class="min-h-screen pt-20">
    <div class="container mx-auto px-4 py-8">
        <!-- عنوان الصفحة -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-800 dark:text-white mb-4">احجز طاولة</h1>
            <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                احجز طاولتك الآن واستمتع بتجربة طعام لا تُنسى في أجواء مريحة
            </p>
        </div>

        <div class="max-w-2xl mx-auto">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-8">
                <form action="{{ route('customer.reservations.store') }}" method="POST">
                    @csrf

                    <!-- معلومات العرض المحسنة (إن وجد) -->
                    @if(request('offer'))
                        @php
                            $offer = \App\Models\Offer::where('slug', request('offer'))->first();
                        @endphp
                        @if($offer)
                            <input type="hidden" name="offer_slug" value="{{ $offer->slug }}">
                            <input type="hidden" name="is_offer_reservation" value="1">

                            <!-- بطاقة العرض المميزة -->
                            <div class="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border-2 border-purple-200 dark:border-purple-800 rounded-2xl p-6 mb-8 shadow-lg relative overflow-hidden">
                                <!-- تأثير الخلفية -->
                                <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-200/30 to-pink-200/30 rounded-full -translate-y-16 translate-x-16"></div>
                                <div class="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-pink-200/30 to-purple-200/30 rounded-full translate-y-12 -translate-x-12"></div>

                                <div class="relative">
                                    <!-- رأس البطاقة -->
                                    <div class="flex items-center justify-between mb-4">
                                        <div class="flex items-center">
                                            <div class="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-3 rounded-xl shadow-lg">
                                                <i class="fas fa-star text-xl"></i>
                                            </div>
                                            <div class="mr-4">
                                                <h3 class="text-xl font-bold text-purple-800 dark:text-purple-200">🎉 حجز عرض خاص</h3>
                                                <p class="text-sm text-purple-600 dark:text-purple-400">تم تطبيق العرض على هذا الحجز</p>
                                            </div>
                                        </div>
                                        <div class="bg-red-500 text-white px-4 py-2 rounded-full font-bold text-sm animate-pulse">
                                            عرض حصري!
                                        </div>
                                    </div>

                                    <!-- تفاصيل العرض -->
                                    <div class="bg-white dark:bg-gray-800 rounded-xl p-4 border border-purple-100 dark:border-purple-800">
                                        <h4 class="font-bold text-gray-800 dark:text-white mb-2">{{ $offer->title }}</h4>
                                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-3">{{ $offer->description }}</p>

                                        <!-- معلومات السعر -->
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-4 space-x-reverse">
                                                @if($offer->discounted_price)
                                                    <div class="text-2xl font-bold text-green-600">
                                                        {{ number_format($offer->discounted_price, 2) }} د.ل
                                                    </div>
                                                    @if($offer->original_price)
                                                        <div class="text-lg text-gray-500 line-through">
                                                            {{ number_format($offer->original_price, 2) }} د.ل
                                                        </div>
                                                        <div class="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                                                            وفر {{ number_format($offer->original_price - $offer->discounted_price, 2) }} د.ل
                                                        </div>
                                                    @endif
                                                @elseif($offer->discount_percentage)
                                                    <div class="bg-gradient-to-r from-red-500 to-orange-500 text-white px-4 py-2 rounded-full font-bold">
                                                        خصم {{ $offer->discount_percentage }}%
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>

                                    <!-- تنبيه مهم -->
                                    <div class="mt-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                                        <div class="flex items-center">
                                            <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 ml-2"></i>
                                            <span class="text-sm text-blue-800 dark:text-blue-200 font-medium">
                                                ⚠️ هذا حجز عرض خاص - سيتم إشعار الموظف تلقائياً بتفاصيل العرض
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    @endif

                    <!-- معلومات الحجز -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <!-- تاريخ الحجز -->
                        <div>
                            <label for="reservation_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                تاريخ الحجز
                            </label>
                            <input type="date"
                                   id="reservation_date"
                                   name="reservation_date"
                                   min="{{ date('Y-m-d') }}"
                                   value="{{ date('Y-m-d') }}"
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                   required>
                        </div>

                        <!-- وقت الحجز -->
                        <div>
                            <label for="reservation_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                وقت الحجز
                            </label>
                            <select id="reservation_time"
                                    name="reservation_time"
                                    class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                    required>
                                <option value="">اختر الوقت</option>
                                <option value="12:00" selected>12:00 ظهراً</option>
                                <option value="12:30">12:30 ظهراً</option>
                                <option value="13:00">1:00 ظهراً</option>
                                <option value="13:30">1:30 ظهراً</option>
                                <option value="14:00">2:00 ظهراً</option>
                                <option value="14:30">2:30 ظهراً</option>
                                <option value="15:00">3:00 عصراً</option>
                                <option value="19:00">7:00 مساءً</option>
                                <option value="19:30">7:30 مساءً</option>
                                <option value="20:00">8:00 مساءً</option>
                                <option value="20:30">8:30 مساءً</option>
                                <option value="21:00">9:00 مساءً</option>
                                <option value="21:30">9:30 مساءً</option>
                                <option value="22:00">10:00 مساءً</option>
                            </select>
                        </div>
                    </div>

                    <!-- عدد الأشخاص -->
                    <div class="mb-6">
                        <label for="party_size" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            عدد الأشخاص
                        </label>
                        <select id="party_size"
                                name="party_size"
                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                required>
                            <option value="">اختر عدد الأشخاص</option>
                            @for($i = 1; $i <= 10; $i++)
                            <option value="{{ $i }}" {{ $i == 2 ? 'selected' : '' }}>{{ $i }} {{ $i == 1 ? 'شخص' : 'أشخاص' }}</option>
                            @endfor
                        </select>
                    </div>

                    <!-- زر البحث عن الطاولات -->
                    <div class="mb-6">
                        <button type="button" id="checkAvailabilityBtn"
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-search ml-2"></i>
                            البحث عن الطاولات المتاحة
                        </button>
                    </div>

                    <!-- عرض الطاولات المتاحة -->
                    <div id="availableTablesSection" class="mb-6 hidden">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                            اختر الطاولة المناسبة:
                        </label>
                        <div id="availableTablesList" class="space-y-6">
                            <!-- سيتم ملء هذا القسم بـ JavaScript -->
                        </div>
                        <input type="hidden" name="selected_table" id="selected_table" required>
                    </div>

                    <!-- خيار طلب الطعام -->
                    <div class="mb-6 bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-xl p-6 border border-orange-200 dark:border-orange-700">
                        <div class="flex items-center mb-4">
                            <input type="checkbox" id="orderFood" name="order_food" value="1"
                                   class="w-5 h-5 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary dark:focus:ring-primary dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="orderFood" class="mr-3 text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                <i class="fas fa-utensils text-primary ml-2"></i>
                                أريد طلب طعام مع الحجز
                            </label>
                        </div>
                        <p class="text-gray-600 dark:text-gray-400 text-sm">
                            اختر هذا الخيار إذا كنت تريد طلب الطعام مسبقاً ليكون جاهزاً عند وصولك للمطعم
                        </p>
                    </div>

                    <!-- قائمة الطعام للطلب المسبق -->
                    <div id="foodOrderSection" class="mb-6 hidden">
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-200 dark:border-gray-700">
                            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                                <h3 class="text-xl font-bold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-clipboard-list text-primary ml-3"></i>
                                    اختر الأطباق
                                </h3>
                                <p class="text-gray-600 dark:text-gray-400 text-sm mt-2">
                                    اختر الأطباق التي تريد طلبها مسبقاً (سيتم تحضيرها قبل وصولك)
                                </p>
                            </div>

                            <div class="p-6">
                                <div id="menuItemsContainer">
                                    <!-- سيتم تحميل قائمة الطعام هنا بـ JavaScript -->
                                    <div class="text-center py-8">
                                        <i class="fas fa-spinner fa-spin text-primary text-2xl mb-4"></i>
                                        <p class="text-gray-600 dark:text-gray-400">جاري تحميل قائمة الطعام...</p>
                                    </div>
                                </div>

                                <!-- ملخص الطلب -->
                                <div id="orderSummary" class="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hidden">
                                    <h4 class="font-semibold text-gray-800 dark:text-white mb-3 flex items-center">
                                        <i class="fas fa-receipt text-primary ml-2"></i>
                                        ملخص الطلب
                                    </h4>
                                    <div id="selectedItems" class="space-y-2 mb-4">
                                        <!-- سيتم عرض الأطباق المختارة هنا -->
                                    </div>
                                    <div class="border-t border-gray-300 dark:border-gray-600 pt-3">
                                        <div class="flex justify-between items-center font-bold text-lg">
                                            <span class="text-gray-800 dark:text-white">المجموع:</span>
                                            <span id="totalPrice" class="text-primary">0.00 د.ل</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- عرض جميع الطاولات مجمعة حسب المناطق -->
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                            <i class="fas fa-map-marked-alt ml-2"></i>
                            استعراض الطاولات حسب المناطق
                        </h3>

                        @if(isset($sortedTables) && $sortedTables->count() > 0)
                            @foreach($sortedTables as $area => $tables)
                                <div class="mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                                    <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 rounded-t-lg">
                                        <h4 class="font-semibold text-gray-800 dark:text-white flex items-center">
                                            @switch($area)
                                                @case('main')
                                                    <i class="fas fa-utensils text-gray-500 ml-2"></i>
                                                    <span class="text-gray-600 dark:text-gray-400">{{ $areaNames[$area] ?? $area }}</span>
                                                    @break
                                                @case('vip')
                                                    <i class="fas fa-crown text-yellow-500 ml-2"></i>
                                                    <span class="text-yellow-600 dark:text-yellow-400">{{ $areaNames[$area] ?? $area }}</span>
                                                    @break
                                                @case('window')
                                                    <i class="fas fa-mountain text-blue-500 ml-2"></i>
                                                    <span class="text-blue-600 dark:text-blue-400">{{ $areaNames[$area] ?? $area }}</span>
                                                    @break
                                                @case('outdoor')
                                                    <i class="fas fa-tree text-green-600 ml-2"></i>
                                                    <span class="text-green-700 dark:text-green-300">{{ $areaNames[$area] ?? $area }}</span>
                                                    @break
                                                @default
                                                    <i class="fas fa-chair text-gray-500 ml-2"></i>
                                                    <span class="text-gray-600 dark:text-gray-400">{{ $areaNames[$area] ?? $area }}</span>
                                            @endswitch
                                            <span class="mr-2 text-sm text-gray-500">({{ $tables->count() }} طاولة)</span>
                                        </h4>
                                    </div>
                                    <div class="p-4">
                                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                            @foreach($tables as $table)
                                                <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:border-primary hover:bg-primary/5 transition">
                                                    <div class="flex items-center justify-between mb-2">
                                                        <h5 class="font-semibold text-gray-800 dark:text-white">طاولة #{{ $table->table_number }}</h5>
                                                        <span class="inline-block bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 px-2 py-1 rounded-full text-xs">
                                                            {{ $table->status == 'available' ? 'متاحة' : ($table->status == 'occupied' ? 'مشغولة' : 'محجوزة') }}
                                                        </span>
                                                    </div>
                                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">
                                                        <i class="fas fa-map-marker-alt ml-1"></i>
                                                        {{ $table->location }}
                                                    </p>
                                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                                        <i class="fas fa-users ml-1"></i>
                                                        سعة: {{ $table->capacity }} أشخاص
                                                    </p>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                                <i class="fas fa-chair text-4xl mb-4"></i>
                                <p>لا توجد طاولات متاحة حالياً</p>
                            </div>
                        @endif
                    </div>

                    <!-- ملاحظات خاصة -->
                    <div class="mb-6">
                        <label for="special_requests" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            ملاحظات خاصة (اختياري)
                        </label>
                        <textarea id="special_requests"
                                  name="special_requests"
                                  rows="4"
                                  class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                  placeholder="أي طلبات خاصة أو ملاحظات..."></textarea>
                    </div>

                    <!-- رسوم الحجز والدفع -->
                    <div class="mb-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                            <i class="fas fa-credit-card ml-2 text-primary"></i>رسوم الحجز والدفع
                        </h3>

                        <!-- رسوم الحجز -->
                        <div class="mb-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-gray-600 dark:text-gray-400">رسوم حجز الطاولة:</span>
                                <span class="font-bold text-lg text-primary">25.00 د.ل</span>
                            </div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                رسوم الحجز مطلوبة لتأكيد الحجز وسيتم خصمها من قيمة الطلب النهائي
                            </p>
                        </div>

                        <!-- طرق الدفع -->
                        <div class="space-y-3">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                اختر طريقة الدفع:
                            </label>

                            <label class="flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600">
                                <input type="radio" name="payment_method" value="cash" class="ml-2" checked>
                                <i class="fas fa-money-bill-wave text-green-500 ml-3"></i>
                                <div>
                                    <div class="font-medium text-gray-800 dark:text-white">الدفع في المطعم</div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400">ادفع رسوم الحجز عند الوصول للمطعم</div>
                                </div>
                            </label>

                            <label class="flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600">
                                <input type="radio" name="payment_method" value="card" class="ml-2">
                                <i class="fas fa-credit-card text-blue-500 ml-3"></i>
                                <div>
                                    <div class="font-medium text-gray-800 dark:text-white">بطاقة ائتمان</div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400">ادفع الآن بالبطاقة الائتمانية</div>
                                </div>
                            </label>

                            <label class="flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600">
                                <input type="radio" name="payment_method" value="bank_transfer" class="ml-2">
                                <i class="fas fa-university text-purple-500 ml-3"></i>
                                <div>
                                    <div class="font-medium text-gray-800 dark:text-white">تحويل بنكي</div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400">تحويل مباشر للحساب البنكي</div>
                                </div>
                            </label>
                        </div>

                        <!-- تفاصيل الدفع بالبطاقة -->
                        <div id="cardPaymentDetails" class="mt-4 space-y-4 hidden">
                            <div class="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
                                <h4 class="font-semibold text-gray-800 dark:text-white mb-3">تفاصيل البطاقة</h4>
                                <div class="grid grid-cols-1 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم البطاقة</label>
                                        <input type="text" name="card_number" placeholder="1234 5678 9012 3456" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                                    </div>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ الانتهاء</label>
                                            <input type="text" name="card_expiry" placeholder="MM/YY" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">CVV</label>
                                            <input type="text" name="card_cvv" placeholder="123" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تفاصيل التحويل البنكي -->
                        <div id="bankTransferDetails" class="mt-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 hidden">
                            <h4 class="font-semibold text-gray-800 dark:text-white mb-2">تفاصيل الحساب البنكي:</h4>
                            <div class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                <p><strong>اسم البنك:</strong>   مصرف الجمهورية فرع المهاري</p>
                                <p><strong>رقم الحساب:</strong> ************</p>
                                <p><strong>اسم المستفيد:</strong> مطعم Eat Hub</p>
                                <p><strong>المبلغ:</strong> 25.00 د.ل</p>
                                <p class="text-red-600 dark:text-red-400 mt-2">
                                    <i class="fas fa-exclamation-triangle ml-1"></i>
                                    يرجى إرسال إيصال التحويل عبر الواتساب: 0919676123
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button type="submit"
                                class="flex-1 bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition">
                            <i class="fas fa-calendar-check ml-2"></i>
                            تأكيد الحجز
                        </button>
                        <a href="{{ route('customer.index') }}"
                           class="flex-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-bold py-3 px-6 rounded-lg transition text-center">
                            <i class="fas fa-times ml-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>

            <!-- معلومات إضافية -->
            <div class="mt-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-3">
                    <i class="fas fa-info-circle ml-2"></i>
                    معلومات مهمة
                </h3>
                <ul class="text-blue-700 dark:text-blue-300 space-y-2">
                    <li><i class="fas fa-check ml-2"></i>يمكن إلغاء الحجز حتى ساعتين قبل الموعد</li>
                    <li><i class="fas fa-check ml-2"></i>سيتم تأكيد الحجز خلال 30 دقيقة</li>
                    <li><i class="fas fa-check ml-2"></i>في حالة التأخير أكثر من 15 دقيقة، قد يتم إلغاء الحجز</li>
                    <li><i class="fas fa-check ml-2"></i>للمجموعات الكبيرة (أكثر من 8 أشخاص)، يرجى الاتصال بنا مباشرة</li>
                </ul>
            </div>
        </div>
    </div>
</div>
</main>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // عناصر النموذج
    const checkAvailabilityBtn = document.getElementById('checkAvailabilityBtn');
    const availableTablesSection = document.getElementById('availableTablesSection');
    const availableTablesList = document.getElementById('availableTablesList');
    const selectedTableInput = document.getElementById('selected_table');
    const reservationForm = document.querySelector('form[action*="reservations"]');

    console.log('Form found:', reservationForm ? 'Yes' : 'No');
    if (reservationForm) {
        console.log('Form action:', reservationForm.action);
    }

    // التحقق من توفر الطاولات
    checkAvailabilityBtn.addEventListener('click', function() {
        const date = document.getElementById('reservation_date').value;
        const time = document.getElementById('reservation_time').value;
        const partySize = document.getElementById('party_size').value;

        console.log('Form values:', { date, time, partySize });

        if (!date || !time || !partySize) {
            showNotification('يرجى إدخال التاريخ والوقت وعدد الأشخاص أولاً', 'error');
            return;
        }

        // تعطيل الزر وإظهار حالة التحميل
        checkAvailabilityBtn.disabled = true;
        checkAvailabilityBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري البحث...';

        // التحقق من CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        console.log('CSRF token element:', csrfToken);
        console.log('CSRF token value:', csrfToken ? csrfToken.getAttribute('content') : 'Not found');

        // إرسال طلب AJAX
        fetch('{{ route("customer.reservations.check-availability") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                date: date,
                time: time,
                party_size: parseInt(partySize)
            })
        })
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                displayAvailableTables(data.available_tables);
                showNotification(data.message, 'success');
            } else {
                showNotification(data.message || 'لا توجد طاولات متاحة', 'error');
                availableTablesSection.classList.add('hidden');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('حدث خطأ أثناء البحث عن الطاولات: ' + error.message, 'error');
        })
        .finally(() => {
            // إعادة تفعيل الزر
            checkAvailabilityBtn.disabled = false;
            checkAvailabilityBtn.innerHTML = '<i class="fas fa-search ml-2"></i>البحث عن الطاولات المتاحة';
        });
    });

    // عرض الطاولات المتاحة
    function displayAvailableTables(tables) {
        availableTablesList.innerHTML = '';

        if (tables.length === 0) {
            availableTablesSection.classList.add('hidden');
            return;
        }

        // تجميع الطاولات حسب المناطق
        const groupedTables = {};
        tables.forEach(table => {
            if (!groupedTables[table.area]) {
                groupedTables[table.area] = [];
            }
            groupedTables[table.area].push(table);
        });

        // ترتيب المناطق حسب المخطط
        const areaOrder = ['main', 'vip', 'window', 'outdoor'];
        const areaNames = {
            'main': 'المنطقة الرئيسية',
            'vip': 'منطقة VIP',
            'window': 'المنطقة العلوية',
            'outdoor': 'المنطقة الخارجية'
        };

        const areaIcons = {
            'main': 'fas fa-utensils text-gray-500',
            'vip': 'fas fa-crown text-yellow-500',
            'window': 'fas fa-mountain text-blue-500',
            'outdoor': 'fas fa-tree text-green-600'
        };

        // عرض الطاولات مجمعة حسب المناطق
        areaOrder.forEach(area => {
            if (groupedTables[area] && groupedTables[area].length > 0) {
                createAreaSection(area, groupedTables[area], areaNames[area], areaIcons[area]);
            }
        });

        // عرض أي مناطق أخرى
        Object.keys(groupedTables).forEach(area => {
            if (!areaOrder.includes(area)) {
                createAreaSection(area, groupedTables[area], areaNames[area] || area, areaIcons[area] || 'fas fa-chair text-gray-500');
            }
        });

        availableTablesSection.classList.remove('hidden');
    }

    // إنشاء قسم للمنطقة
    function createAreaSection(area, tables, areaName, areaIcon) {
        const areaSection = document.createElement('div');
        areaSection.className = 'mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700';

        areaSection.innerHTML = `
            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 rounded-t-lg">
                <h4 class="font-semibold text-gray-800 dark:text-white flex items-center">
                    <i class="${areaIcon} ml-2"></i>
                    <span>${areaName}</span>
                    <span class="mr-2 text-sm text-gray-500">(${tables.length} طاولة متاحة)</span>
                </h4>
            </div>
            <div class="p-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4" id="area-${area}">
                </div>
            </div>
        `;

        availableTablesList.appendChild(areaSection);

        // إضافة الطاولات لهذه المنطقة
        const areaContainer = document.getElementById(`area-${area}`);
        tables.forEach(table => {
            const tableCard = createTableCard(table, area);
            areaContainer.appendChild(tableCard);
        });
    }

    // إنشاء بطاقة الطاولة
    function createTableCard(table, area) {
        const tableCard = document.createElement('div');
        tableCard.className = 'table-option border border-gray-300 dark:border-gray-600 rounded-lg p-4 cursor-pointer hover:border-primary hover:bg-primary/5 transition';
        tableCard.dataset.tableId = table.table_id;

        // تحديد الأيقونة واللون حسب المنطقة
        let areaIcon = 'fas fa-chair';
        let areaColor = 'text-primary';
        let areaBadge = 'bg-gray-100 text-gray-600';
        let areaName = 'عادية';

        switch(table.area) {
            case 'main':
                areaIcon = 'fas fa-utensils';
                areaColor = 'text-gray-500';
                areaBadge = 'bg-gray-100 text-gray-600';
                areaName = 'المنطقة الرئيسية';
                break;
            case 'vip':
                areaIcon = 'fas fa-crown';
                areaColor = 'text-yellow-500';
                areaBadge = 'bg-yellow-100 text-yellow-600';
                areaName = 'VIP';
                break;
            case 'window':
                areaIcon = 'fas fa-mountain';
                areaColor = 'text-blue-500';
                areaBadge = 'bg-blue-100 text-blue-600';
                areaName = 'المنطقة العلوية';
                break;
            case 'outdoor':
                areaIcon = 'fas fa-tree';
                areaColor = 'text-green-600';
                areaBadge = 'bg-green-100 text-green-700';
                areaName = 'المنطقة الخارجية';
                break;
            default:
                areaIcon = 'fas fa-chair';
                areaColor = 'text-primary';
                areaBadge = 'bg-gray-100 text-gray-600';
                areaName = 'عادية';
        }

            tableCard.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="flex items-center gap-2 mb-2">
                            <h4 class="font-bold text-gray-800 dark:text-white">طاولة #${table.table_number}</h4>
                            <span class="inline-block ${areaBadge} px-2 py-1 rounded-full text-xs font-medium">
                                ${areaName}
                            </span>
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">
                            <i class="fas fa-map-marker-alt ml-1"></i>
                            ${table.location}
                        </p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            <i class="fas fa-users ml-1"></i>
                            سعة: ${table.capacity} أشخاص
                        </p>
                    </div>
                    <div class="${areaColor}">
                        <i class="${areaIcon} text-2xl"></i>
                    </div>
                </div>
                <div class="mt-3 flex justify-between items-center">
                    <span class="inline-block bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 px-2 py-1 rounded-full text-xs">
                        <i class="fas fa-check-circle ml-1"></i>
                        متاحة
                    </span>
                    <span class="text-xs text-gray-500">
                        انقر للاختيار
                    </span>
                </div>
            `;

        tableCard.addEventListener('click', function() {
            // إزالة التحديد من جميع الطاولات
            document.querySelectorAll('.table-option').forEach(option => {
                option.classList.remove('border-primary', 'bg-primary/10');
                option.classList.add('border-gray-300', 'dark:border-gray-600');
            });

            // تحديد الطاولة المختارة
            this.classList.remove('border-gray-300', 'dark:border-gray-600');
            this.classList.add('border-primary', 'bg-primary/10');

            selectedTableInput.value = table.table_id;
        });

        return tableCard;
    }

    // التحكم في قسم طلب الطعام
    const orderFoodCheckbox = document.getElementById('orderFood');
    const foodOrderSection = document.getElementById('foodOrderSection');
    const menuItemsContainer = document.getElementById('menuItemsContainer');
    const orderSummary = document.getElementById('orderSummary');
    const selectedItems = document.getElementById('selectedItems');
    const totalPrice = document.getElementById('totalPrice');

    let selectedMenuItems = [];
    let menuItems = [];

    // تفعيل/إلغاء تفعيل قسم طلب الطعام
    orderFoodCheckbox.addEventListener('change', function() {
        if (this.checked) {
            // التحقق من اختيار طاولة أولاً
            if (!selectedTableInput.value) {
                this.checked = false;
                showNotification('يرجى البحث عن الطاولات المتاحة واختيار طاولة أولاً', 'error');
                return;
            }
            foodOrderSection.classList.remove('hidden');
            loadMenuItems();
        } else {
            foodOrderSection.classList.add('hidden');
            selectedMenuItems = [];
            updateOrderSummary();
        }
    });

    // تحميل قائمة الطعام
    async function loadMenuItems() {
        try {
            menuItemsContainer.innerHTML = `
                <div class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-primary text-2xl mb-4"></i>
                    <p class="text-gray-600 dark:text-gray-400">جاري تحميل قائمة الطعام...</p>
                </div>
            `;

            const response = await fetch('/api/menu-items');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.error) {
                throw new Error(data.error);
            }

            menuItems = data;
            displayMenuItems(menuItems);
        } catch (error) {
            console.error('خطأ في تحميل قائمة الطعام:', error);
            menuItemsContainer.innerHTML = `
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-triangle text-red-500 text-2xl mb-4"></i>
                    <p class="text-red-600 dark:text-red-400">حدث خطأ في تحميل قائمة الطعام</p>
                    <p class="text-gray-500 text-sm mt-2">${error.message}</p>
                    <button onclick="loadMenuItems()" class="mt-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition">
                        <i class="fas fa-redo ml-2"></i>
                        إعادة المحاولة
                    </button>
                </div>
            `;
        }
    }

    // عرض قائمة الطعام
    function displayMenuItems(items) {
        if (!items || items.length === 0) {
            menuItemsContainer.innerHTML = `
                <div class="text-center py-12">
                    <i class="fas fa-utensils text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2">لا توجد أطباق متاحة حالياً</h3>
                    <p class="text-gray-500 dark:text-gray-500 text-sm">يمكنك المتابعة بالحجز بدون طلب مسبق</p>
                    <button onclick="loadMenuItems()" class="mt-4 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition">
                        <i class="fas fa-redo ml-2"></i>
                        إعادة التحميل
                    </button>
                </div>
            `;
            return;
        }

        // تجميع الأطباق حسب الفئة
        const groupedItems = items.reduce((groups, item) => {
            const category = item.category || 'أخرى';
            if (!groups[category]) {
                groups[category] = [];
            }
            groups[category].push(item);
            return groups;
        }, {});

        let html = '';
        Object.keys(groupedItems).forEach(category => {
            html += `
                <div class="mb-8">
                    <h4 class="text-lg font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                        <i class="fas fa-tag text-primary ml-2"></i>
                        ${category}
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            `;

            groupedItems[category].forEach(item => {
                const isSelected = selectedMenuItems.find(selected => selected.id === item.id);
                html += `
                    <div class="food-item-card border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:border-primary transition ${isSelected ? 'border-primary bg-primary/5' : ''}">
                        <div class="flex justify-between items-start mb-2">
                            <h5 class="font-semibold text-gray-800 dark:text-white">${item.name}</h5>
                            <span class="text-primary font-bold text-lg">${parseFloat(item.price).toFixed(2)} د.ل</span>
                        </div>
                        ${item.description ? `<p class="text-gray-600 dark:text-gray-400 text-sm mb-3">${item.description}</p>` : ''}
                        <div class="flex items-center justify-between">
                            <div class="quantity-controls flex items-center space-x-3 space-x-reverse">
                                <button type="button" onclick="removeFromOrder(${item.id})" class="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center hover:bg-red-500 hover:text-white transition ${isSelected ? '' : 'opacity-50 cursor-not-allowed'}" ${!isSelected ? 'disabled' : ''}>
                                    <i class="fas fa-minus text-sm"></i>
                                </button>
                                <span id="quantity-${item.id}" class="font-semibold text-gray-800 dark:text-white min-w-[30px] text-center bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">${isSelected ? isSelected.quantity : 0}</span>
                                <button type="button" onclick="addToOrder(${item.id}, '${item.name.replace(/'/g, "\\'")}', ${item.price})" class="w-8 h-8 bg-primary rounded-full flex items-center justify-center hover:bg-primary/90 text-white transition">
                                    <i class="fas fa-plus text-sm"></i>
                                </button>
                            </div>
                            ${isSelected ? `<span class="text-green-600 dark:text-green-400 text-sm font-medium animate-pulse"><i class="fas fa-check ml-1"></i>مضاف للطلب</span>` : `<span class="text-gray-400 text-sm">اضغط + للإضافة</span>`}
                        </div>
                    </div>
                `;
            });

            html += `
                    </div>
                </div>
            `;
        });

        menuItemsContainer.innerHTML = html;
    }

    // إضافة طبق للطلب
    window.addToOrder = function(id, name, price) {
        const existingItem = selectedMenuItems.find(item => item.id === id);
        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            selectedMenuItems.push({ id, name, price, quantity: 1 });
        }
        updateQuantityDisplay(id);
        updateOrderSummary();

        // إظهار رسالة تأكيد
        showNotification(`تم إضافة ${name} للطلب`, 'success');
    };

    // إزالة طبق من الطلب
    window.removeFromOrder = function(id) {
        const existingItem = selectedMenuItems.find(item => item.id === id);
        if (existingItem) {
            existingItem.quantity -= 1;
            if (existingItem.quantity <= 0) {
                selectedMenuItems = selectedMenuItems.filter(item => item.id !== id);
            }
        }
        updateQuantityDisplay(id);
        updateOrderSummary();
    };

    // تحديث عرض الكمية
    function updateQuantityDisplay(id) {
        const quantityElement = document.getElementById(`quantity-${id}`);
        const item = selectedMenuItems.find(item => item.id === id);
        if (quantityElement) {
            quantityElement.textContent = item ? item.quantity : 0;
        }
        // إعادة عرض القائمة لتحديث الحالة
        displayMenuItems(menuItems);
    }

    // تحديث ملخص الطلب
    function updateOrderSummary() {
        if (selectedMenuItems.length === 0) {
            orderSummary.classList.add('hidden');
            return;
        }

        orderSummary.classList.remove('hidden');

        let html = '';
        let total = 0;

        selectedMenuItems.forEach(item => {
            const itemTotal = item.price * item.quantity;
            total += itemTotal;
            html += `
                <div class="order-summary-item flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-600 last:border-b-0">
                    <div class="flex-1">
                        <span class="text-gray-800 dark:text-white font-medium">${item.name}</span>
                        <span class="text-gray-500 text-sm"> × ${item.quantity}</span>
                    </div>
                    <span class="text-primary font-bold">${itemTotal.toFixed(2)} د.ل</span>
                </div>
            `;
        });

        selectedItems.innerHTML = html;
        totalPrice.textContent = total.toFixed(2) + ' د.ل';

        // إضافة البيانات للنموذج
        updateFormData();
    }

    // تحديث بيانات النموذج
    function updateFormData() {
        // إزالة الحقول القديمة
        document.querySelectorAll('input[name^="menu_items"]').forEach(input => input.remove());

        // إضافة الحقول الجديدة
        selectedMenuItems.forEach((item, index) => {
            const form = document.querySelector('form[action*="reservations"]');

            if (!form) {
                console.error('Form not found!');
                return;
            }

            const idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.name = `menu_items[${index}][id]`;
            idInput.value = item.id;
            form.appendChild(idInput);

            const quantityInput = document.createElement('input');
            quantityInput.type = 'hidden';
            quantityInput.name = `menu_items[${index}][quantity]`;
            quantityInput.value = item.quantity;
            form.appendChild(quantityInput);

            console.log('Added menu item to form:', {
                id: item.id,
                quantity: item.quantity,
                name: item.name
            });
        });

        console.log('Total menu items in form:', selectedMenuItems.length);
    }

    // التحكم في طرق الدفع
    const paymentMethodRadios = document.querySelectorAll('input[name="payment_method"]');
    const cardPaymentDetails = document.getElementById('cardPaymentDetails');
    const bankTransferDetails = document.getElementById('bankTransferDetails');

    paymentMethodRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            // إخفاء جميع التفاصيل
            cardPaymentDetails.classList.add('hidden');
            bankTransferDetails.classList.add('hidden');

            // إظهار التفاصيل المناسبة
            if (this.value === 'card') {
                cardPaymentDetails.classList.remove('hidden');
            } else if (this.value === 'bank_transfer') {
                bankTransferDetails.classList.remove('hidden');
            }
        });
    });

    // تنسيق رقم البطاقة
    const cardNumberInput = document.querySelector('input[name="card_number"]');
    if (cardNumberInput) {
        cardNumberInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            if (formattedValue.length > 19) formattedValue = formattedValue.substr(0, 19);
            e.target.value = formattedValue;
        });
    }

    // تنسيق تاريخ انتهاء البطاقة
    const cardExpiryInput = document.querySelector('input[name="card_expiry"]');
    if (cardExpiryInput) {
        cardExpiryInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + '/' + value.substring(2, 4);
            }
            e.target.value = value;
        });
    }

    // تنسيق CVV
    const cardCvvInput = document.querySelector('input[name="card_cvv"]');
    if (cardCvvInput) {
        cardCvvInput.addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/\D/g, '').substring(0, 3);
        });
    }

    // التحقق من النموذج قبل الإرسال
    reservationForm.addEventListener('submit', function(e) {
        console.log('Form submission started');
        console.log('Order food checked:', orderFoodCheckbox.checked);
        console.log('Selected menu items:', selectedMenuItems);

        // التحقق من اختيار طاولة
        if (!selectedTableInput.value) {
            e.preventDefault();
            showNotification('يرجى البحث عن الطاولات المتاحة واختيار طاولة', 'error');
            return;
        }

        // التحقق من طلب الطعام
        if (orderFoodCheckbox.checked && selectedMenuItems.length === 0) {
            e.preventDefault();
            showNotification('يرجى اختيار أطباق للطلب المسبق أو إلغاء تفعيل خيار طلب الطعام', 'error');
            return;
        }

        const paymentMethod = document.querySelector('input[name="payment_method"]:checked');
        if (!paymentMethod) {
            e.preventDefault();
            showNotification('يرجى اختيار طريقة الدفع', 'error');
            return;
        }

        // تحديث بيانات النموذج قبل الإرسال
        if (orderFoodCheckbox.checked && selectedMenuItems.length > 0) {
            updateFormData();
            console.log('Form data updated before submission');
        }

        if (paymentMethod.value === 'card') {
            const cardNumber = document.querySelector('input[name="card_number"]').value;
            const cardExpiry = document.querySelector('input[name="card_expiry"]').value;
            const cardCvv = document.querySelector('input[name="card_cvv"]').value;

            if (!cardNumber || !cardExpiry || !cardCvv) {
                e.preventDefault();
                showNotification('يرجى إدخال جميع بيانات البطاقة', 'error');
                return;
            }

            if (cardNumber.replace(/\s/g, '').length < 16) {
                e.preventDefault();
                showNotification('رقم البطاقة غير صحيح', 'error');
                return;
            }
        }

        // إظهار رسالة تأكيد
        showNotification('جاري معالجة طلب الحجز...', 'info');
    });
});

// عرض الإشعارات
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-20 left-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${
                type === 'success' ? 'fa-check-circle' :
                type === 'error' ? 'fa-exclamation-circle' :
                'fa-info-circle'
            } ml-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}
</script>
@endpush
