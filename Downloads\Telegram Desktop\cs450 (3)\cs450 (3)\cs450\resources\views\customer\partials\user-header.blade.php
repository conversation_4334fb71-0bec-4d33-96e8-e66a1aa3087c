{{-- User Pages Unified Header Component --}}
<div class="bg-gradient-to-r from-primary to-primary/80 shadow-xl mb-8">
    <div class="container mx-auto px-4 py-8">
        <!-- User Info Section -->
        <div class="flex flex-col md:flex-row items-center justify-between text-white">
            <div class="flex items-center mb-6 md:mb-0">
                <div class="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white text-2xl font-bold ml-6 shadow-lg">
                    {{ auth()->check() ? substr(auth()->user()->first_name ?? 'أ', 0, 1) : 'أ' }}
                </div>
                <div>
                    <h1 class="text-3xl font-bold mb-2">
                        {{ $title ?? 'صفحة المستخدم' }}
                    </h1>
                    <p class="text-white/90 text-lg mb-2">
                        مرحباً، {{ auth()->check() ? (auth()->user()->first_name ?? 'المستخدم') . ' ' . (auth()->user()->last_name ?? '') : 'المستخدم' }}
                    </p>
                    <div class="flex items-center">
                        <span class="inline-flex items-center px-3 py-1 bg-green-500/20 backdrop-blur-sm text-green-100 rounded-full text-sm">
                            <i class="fas fa-check-circle ml-2"></i>
                            حساب نشط
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- Quick Stats -->
            @if(isset($showStats) && $showStats)
            <div class="text-center md:text-right">
                <div class="grid grid-cols-2 gap-4 text-center">
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/20 transition-all duration-200">
                        <div class="text-2xl md:text-3xl font-bold">{{ \App\Models\Order::where('user_id', auth()->user()->user_id)->where('order_type', '!=', 'preorder')->count() }}</div>
                        <div class="text-sm md:text-base text-white/80">طلب</div>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/20 transition-all duration-200">
                        <div class="text-2xl md:text-3xl font-bold">{{ \App\Models\Reservation::where('user_id', auth()->user()->user_id)->where('status', 'confirmed')->count() }}</div>
                        <div class="text-sm md:text-base text-white/80">حجز</div>
                    </div>
                </div>
                <div class="mt-4 text-sm text-white/70">
                    عضو منذ {{ auth()->user()->created_at ? auth()->user()->created_at->format('Y/m/d') : 'غير محدد' }}
                </div>
            </div>
            @endif
        </div>
    </div>
    
    <!-- Navigation Tabs -->
    <div class="bg-white/10 backdrop-blur-sm border-t border-white/20">
        <div class="container mx-auto px-4">
            <div class="flex flex-wrap justify-center md:justify-start space-x-2 space-x-reverse py-4">
                <a href="{{ route('customer.dashboard') }}" 
                   class="nav-tab {{ request()->routeIs('customer.dashboard') ? 'active' : '' }}">
                    <i class="fas fa-tachometer-alt ml-2"></i>
                    لوحة التحكم
                </a>
                <a href="{{ route('customer.profile') }}" 
                   class="nav-tab {{ request()->routeIs('customer.profile*') ? 'active' : '' }}">
                    <i class="fas fa-user ml-2"></i>
                    الملف الشخصي
                </a>
                <a href="{{ route('customer.orders') }}" 
                   class="nav-tab {{ request()->routeIs('customer.orders*') ? 'active' : '' }}">
                    <i class="fas fa-shopping-bag ml-2"></i>
                    طلباتي
                </a>
                <a href="{{ route('customer.reservations') }}" 
                   class="nav-tab {{ request()->routeIs('customer.reservations*') ? 'active' : '' }}">
                    <i class="fas fa-calendar-alt ml-2"></i>
                    حجوزاتي
                </a>
                <a href="{{ route('customer.menu') }}" 
                   class="nav-tab {{ request()->routeIs('customer.menu*') ? 'active' : '' }}">
                    <i class="fas fa-utensils ml-2"></i>
                    القائمة
                </a>
                <a href="{{ route('customer.index') }}" 
                   class="nav-tab">
                    <i class="fas fa-home ml-2"></i>
                    الرئيسية
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.nav-tab {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
}

.nav-tab:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.nav-tab.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
    .nav-tab {
        font-size: 0.75rem;
        padding: 0.5rem 0.75rem;
        margin-bottom: 0.5rem;
    }
}
</style>
