@startuml Restaurant System - Detailed Use Cases with Permissions

!theme plain
skinparam backgroundColor #FFFFFF
skinparam actor {
    BackgroundColor #E3F2FD
    BorderColor #1976D2
    FontColor #0D47A1
}
skinparam usecase {
    BackgroundColor #FFF8E1
    BorderColor #F57C00
    FontColor #E65100
}
skinparam rectangle {
    BackgroundColor #F1F8E9
    BorderColor #388E3C
    FontColor #1B5E20
}
skinparam package {
    BackgroundColor #FCE4EC
    BorderColor #C2185B
    FontColor #880E4F
}

title نظام إدارة المطعم - مخطط مفصل للصلاحيات وحالات الاستخدام

' الممثلين الرئيسيين
actor "العميل" as Customer
actor "النادل" as Waiter
actor "الكاشير" as Cashier
actor "مدير المطبخ" as KitchenManager
actor "مدير المخزون" as InventoryManager
actor "المحاسب" as Accountant
actor "مدير المبيعات" as SalesManager
actor "المدير العام" as GeneralManager
actor "مدير النظام" as SystemAdmin

' النظام الرئيسي
package "نظام إدارة المطعم" {
    
    ' وحدة إدارة العملاء
    rectangle "وحدة العملاء" {
        usecase "التسجيل والدخول" as CustomerAuth
        usecase "تصفح القائمة" as BrowseMenu
        usecase "إدارة السلة" as ManageCart
        usecase "إجراء الطلبات" as PlaceOrder
        usecase "تتبع الطلبات" as TrackOrder
        usecase "حجز الطاولات" as ReserveTable
        usecase "الدفع الإلكتروني" as OnlinePayment
        usecase "تقييم الخدمة" as RateService
        usecase "عرض التاريخ" as ViewHistory
    }
    
    ' وحدة إدارة الطلبات
    rectangle "وحدة الطلبات" {
        usecase "استقبال الطلبات" as ReceiveOrders
        usecase "تحديث حالة الطلب" as UpdateOrderStatus
        usecase "طباعة الفواتير" as PrintInvoices
        usecase "إدارة طلبات التوصيل" as ManageDelivery
        usecase "إلغاء الطلبات" as CancelOrders
    }
    
    ' وحدة إدارة الحجوزات
    rectangle "وحدة الحجوزات" {
        usecase "إدارة الحجوزات" as ManageReservations
        usecase "تأكيد الحجوزات" as ConfirmReservations
        usecase "إدارة الطاولات" as ManageTables
        usecase "جدولة الحجوزات" as ScheduleReservations
    }
    
    ' وحدة المدفوعات
    rectangle "وحدة المدفوعات" {
        usecase "معالجة المدفوعات" as ProcessPayments
        usecase "إدارة الخصومات" as ManageDiscounts
        usecase "إصدار الفواتير" as IssueInvoices
        usecase "تسوية الحسابات" as ReconcileAccounts
    }
    
    ' وحدة إدارة القائمة
    rectangle "وحدة القائمة" {
        usecase "إدارة الأطباق" as ManageDishes
        usecase "تحديث الأسعار" as UpdatePrices
        usecase "إدارة الفئات" as ManageCategories
        usecase "إدارة العروض" as ManageOffers
        usecase "تحديد التوفر" as SetAvailability
    }
    
    ' وحدة إدارة المخزون
    rectangle "وحدة المخزون" {
        usecase "إدارة المخزون" as ManageInventory
        usecase "تتبع المكونات" as TrackIngredients
        usecase "تحديث الكميات" as UpdateQuantities
        usecase "تقارير المخزون" as InventoryReports
        usecase "إنذارات النفاد" as LowStockAlerts
    }
    
    ' وحدة التقارير والتحليلات
    rectangle "وحدة التقارير" {
        usecase "تقارير المبيعات" as SalesReports
        usecase "التقارير المالية" as FinancialReports
        usecase "تقارير الأداء" as PerformanceReports
        usecase "تحليل البيانات" as DataAnalysis
        usecase "تصدير التقارير" as ExportReports
    }
    
    ' وحدة إدارة النظام
    rectangle "وحدة النظام" {
        usecase "إدارة المستخدمين" as ManageUsers
        usecase "إدارة الصلاحيات" as ManagePermissions
        usecase "إدارة الأدوار" as ManageRoles
        usecase "إعدادات النظام" as SystemSettings
        usecase "النسخ الاحتياطي" as BackupSystem
        usecase "مراقبة النظام" as MonitorSystem
    }
}

' ربط العملاء
Customer --> CustomerAuth
Customer --> BrowseMenu
Customer --> ManageCart
Customer --> PlaceOrder
Customer --> TrackOrder
Customer --> ReserveTable
Customer --> OnlinePayment
Customer --> RateService
Customer --> ViewHistory

' ربط النادل
Waiter --> ReceiveOrders
Waiter --> UpdateOrderStatus
Waiter --> ManageReservations
Waiter --> ConfirmReservations
Waiter --> ManageTables

' ربط الكاشير
Cashier --> ProcessPayments
Cashier --> PrintInvoices
Cashier --> IssueInvoices
Cashier --> ManageDiscounts
Cashier --> ReceiveOrders

' ربط مدير المطبخ
KitchenManager --> UpdateOrderStatus
KitchenManager --> ManageDishes
KitchenManager --> SetAvailability
KitchenManager --> TrackIngredients

' ربط مدير المخزون
InventoryManager --> ManageInventory
InventoryManager --> TrackIngredients
InventoryManager --> UpdateQuantities
InventoryManager --> InventoryReports
InventoryManager --> LowStockAlerts

' ربط المحاسب
Accountant --> FinancialReports
Accountant --> ProcessPayments
Accountant --> ReconcileAccounts
Accountant --> ExportReports

' ربط مدير المبيعات
SalesManager --> SalesReports
SalesManager --> ManageOffers
SalesManager --> UpdatePrices
SalesManager --> DataAnalysis
SalesManager --> ManageDiscounts

' ربط المدير العام
GeneralManager --> ManageUsers
GeneralManager --> PerformanceReports
GeneralManager --> FinancialReports
GeneralManager --> SalesReports
GeneralManager --> ManageOffers
GeneralManager --> SystemSettings

' ربط مدير النظام
SystemAdmin --> ManageUsers
SystemAdmin --> ManagePermissions
SystemAdmin --> ManageRoles
SystemAdmin --> SystemSettings
SystemAdmin --> BackupSystem
SystemAdmin --> MonitorSystem

' علاقات التضمين والتوسع
PlaceOrder ..> ManageCart : <<include>>
PlaceOrder ..> OnlinePayment : <<extend>>
ProcessPayments ..> IssueInvoices : <<include>>
ManageInventory ..> LowStockAlerts : <<extend>>
FinancialReports ..> ExportReports : <<extend>>
SalesReports ..> ExportReports : <<extend>>

' ملاحظات
note right of SystemAdmin : مدير النظام لديه\nصلاحيات كاملة\nلجميع الوحدات
note right of GeneralManager : المدير العام لديه\nصلاحيات إدارية\nشاملة
note left of Customer : العميل يتفاعل مع\nالواجهة الأمامية فقط

@enduml
