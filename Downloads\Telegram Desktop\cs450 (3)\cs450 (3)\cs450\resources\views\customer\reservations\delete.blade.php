@extends('customer.layouts.app')

@section('title', 'حذف الحجز - Eat Hub')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <!-- شريط التنقل السريع -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
            <div class="flex flex-wrap gap-2 justify-center md:justify-start">
                <a href="{{ route('customer.dashboard') }}" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white px-4 py-2 rounded-lg text-sm transition">
                    <i class="fas fa-tachometer-alt ml-1"></i>لوحة التحكم
                </a>
                <a href="{{ route('customer.reservations') }}" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white px-4 py-2 rounded-lg text-sm transition">
                    <i class="fas fa-calendar-alt ml-1"></i>حجوزاتي
                </a>
                <span class="bg-red-500 text-white px-4 py-2 rounded-lg text-sm">
                    <i class="fas fa-trash ml-1"></i>حذف الحجز
                </span>
            </div>
        </div>

        <!-- تحذير الحذف -->
        <div class="bg-red-50 dark:bg-red-900/20 border-2 border-red-200 dark:border-red-800 rounded-lg p-6 mb-6">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center ml-4">
                    <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                </div>
                <div>
                    <h1 class="text-xl font-bold text-red-800 dark:text-red-200">تأكيد حذف الحجز</h1>
                    <p class="text-red-600 dark:text-red-400 text-sm">هذا الإجراء لا يمكن التراجع عنه</p>
                </div>
            </div>
        </div>

        <!-- معلومات الحجز المراد حذفه -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-bold text-gray-800 dark:text-white mb-4">
                <i class="fas fa-info-circle text-blue-500 ml-2"></i>
                معلومات الحجز المراد حذفه
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-hashtag text-primary text-lg ml-3"></i>
                        <div>
                            <p class="font-medium text-gray-800 dark:text-white">رقم الحجز</p>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">R0002</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-calendar text-primary text-lg ml-3"></i>
                        <div>
                            <p class="font-medium text-gray-800 dark:text-white">التاريخ والوقت</p>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">May 2025 28 - 12:30</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-chair text-primary text-lg ml-3"></i>
                        <div>
                            <p class="font-medium text-gray-800 dark:text-white">الطاولة</p>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">طاولة #2 - منطقة عامة</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-users text-primary text-lg ml-3"></i>
                        <div>
                            <p class="font-medium text-gray-800 dark:text-white">عدد الأشخاص</p>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">4 أشخاص</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ملاحظات خاصة -->
            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                <p class="text-yellow-800 dark:text-yellow-200 text-sm">
                    <i class="fas fa-sticky-note ml-1"></i>
                    <strong>ملاحظة خاصة:</strong> طاولة بجانب النافذة، احتفال بعيد ميلاد
                </p>
            </div>
        </div>

        <!-- أسباب الحذف -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">
                <i class="fas fa-question-circle text-orange-500 ml-2"></i>
                سبب إلغاء الحجز (اختياري)
            </h3>
            <form id="deleteReservationForm" action="{{ route('customer.reservations.cancel', $reservation->reservation_id ?? $reservation->id) }}" method="POST">
                @csrf
                @method('DELETE')

                <div class="space-y-3 mb-4">
                    <label class="flex items-center">
                        <input type="radio" name="cancellation_reason" value="change_plans" class="ml-2 text-primary">
                        <span class="text-gray-700 dark:text-gray-300">تغيير في الخطط</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="cancellation_reason" value="emergency" class="ml-2 text-primary">
                        <span class="text-gray-700 dark:text-gray-300">ظرف طارئ</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="cancellation_reason" value="found_better" class="ml-2 text-primary">
                        <span class="text-gray-700 dark:text-gray-300">وجدت خيار أفضل</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="cancellation_reason" value="technical_issue" class="ml-2 text-primary">
                        <span class="text-gray-700 dark:text-gray-300">مشكلة تقنية في الحجز</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="cancellation_reason" value="other" class="ml-2 text-primary">
                        <span class="text-gray-700 dark:text-gray-300">سبب آخر</span>
                    </label>
                </div>

                <div class="mb-4">
                    <label for="additional_notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        ملاحظات إضافية (اختياري)
                    </label>
                    <textarea id="additional_notes"
                              name="additional_notes"
                              rows="3"
                              placeholder="أي ملاحظات إضافية حول سبب الإلغاء..."
                              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"></textarea>
                </div>
            </form>
        </div>

        <!-- تحذيرات مهمة -->
        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
            <div class="flex items-start">
                <i class="fas fa-exclamation-triangle text-yellow-600 ml-2 mt-1"></i>
                <div>
                    <h3 class="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">تحذيرات مهمة</h3>
                    <ul class="text-yellow-700 dark:text-yellow-300 text-sm space-y-1">
                        <li>• سيتم إلغاء الحجز نهائياً ولا يمكن استرداده</li>
                        <li>• إذا كان الحجز مدفوع، سيتم استرداد المبلغ خلال 3-5 أيام عمل</li>
                        <li>• سيتم إشعار المطعم بالإلغاء فوراً</li>
                        <li>• يمكنك إنشاء حجز جديد في أي وقت</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row gap-4 justify-between">
                <div class="flex flex-col sm:flex-row gap-4">
                    <button type="button"
                            onclick="confirmDelete()"
                            class="bg-red-500 hover:bg-red-600 text-white font-bold py-3 px-6 rounded-lg transition flex items-center justify-center">
                        <i class="fas fa-trash ml-2"></i>
                        تأكيد حذف الحجز
                    </button>
                    <a href="{{ route('customer.reservations') }}"
                       class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-lg transition flex items-center justify-center">
                        <i class="fas fa-times ml-2"></i>
                        إلغاء العملية
                    </a>
                </div>
                <a href="{{ route('customer.reservations.edit', $reservation->reservation_id ?? $reservation->id) }}"
                   class="bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition flex items-center justify-center">
                    <i class="fas fa-edit ml-2"></i>
                    تعديل بدلاً من الحذف
                </a>
            </div>
        </div>

        <!-- معلومات الاتصال -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mt-6">
            <div class="flex items-start">
                <i class="fas fa-phone text-blue-600 ml-2 mt-1"></i>
                <div>
                    <h3 class="font-semibold text-blue-800 dark:text-blue-200">هل تحتاج مساعدة؟</h3>
                    <p class="text-blue-700 dark:text-blue-300 text-sm mt-1">
                        يمكنك الاتصال بنا على: <strong>+218 91 234 5678</strong> أو مراسلتنا عبر الواتساب للمساعدة في تعديل الحجز بدلاً من حذفه.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

<!-- نافذة تأكيد الحذف المنسقة -->
<div id="deleteConfirmModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full transform transition-all duration-300 scale-95 opacity-0" id="modalContent">
        <!-- رأس النافذة -->
        <div class="bg-gradient-to-r from-red-500 to-red-600 text-white p-6 rounded-t-2xl">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center ml-3">
                        <i class="fas fa-exclamation-triangle text-2xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold">تأكيد إلغاء الحجز</h3>
                        <p class="text-red-100 text-sm">هذا الإجراء لا يمكن التراجع عنه</p>
                    </div>
                </div>
                <button onclick="hideDeleteModal()" class="text-white hover:text-red-200 transition">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- محتوى النافذة -->
        <div class="p-6">
            <!-- معلومات الحجز -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                <h4 class="font-bold text-gray-800 dark:text-white mb-3 flex items-center">
                    <i class="fas fa-info-circle text-blue-500 ml-2"></i>
                    تفاصيل الحجز
                </h4>
                <div id="reservationDetails" class="space-y-2 text-sm">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <!-- السبب المختار -->
            <div id="selectedReason" class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3 mb-4 hidden">
                <p class="text-yellow-800 dark:text-yellow-200 text-sm">
                    <i class="fas fa-tag ml-1"></i>
                    <strong>السبب:</strong> <span id="reasonText"></span>
                </p>
            </div>

            <!-- الملاحظات -->
            <div id="selectedNotes" class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3 mb-6 hidden">
                <p class="text-blue-800 dark:text-blue-200 text-sm">
                    <i class="fas fa-sticky-note ml-1"></i>
                    <strong>ملاحظات إضافية:</strong> <span id="notesText"></span>
                </p>
            </div>

            <!-- تحذير -->
            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
                <div class="flex items-start">
                    <i class="fas fa-exclamation-triangle text-red-500 mt-1 ml-2"></i>
                    <div>
                        <h5 class="font-bold text-red-800 dark:text-red-200 mb-1">تنبيه مهم</h5>
                        <p class="text-red-700 dark:text-red-300 text-sm">
                            بعد إلغاء الحجز، لن تتمكن من استرداده. إذا كنت تريد تعديل الحجز بدلاً من إلغائه، يمكنك الضغط على "تعديل" بدلاً من الحذف.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="bg-gray-50 dark:bg-gray-700 px-6 py-4 rounded-b-2xl flex flex-col sm:flex-row gap-3 justify-end">
            <button onclick="hideDeleteModal()"
                    class="px-6 py-3 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium rounded-lg transition flex items-center justify-center">
                <i class="fas fa-times ml-2"></i>
                إلغاء العملية
            </button>
            <button onclick="proceedWithDelete()"
                    id="confirmDeleteBtn"
                    class="px-6 py-3 bg-red-500 hover:bg-red-600 text-white font-bold rounded-lg transition flex items-center justify-center">
                <i class="fas fa-trash ml-2"></i>
                تأكيد الحذف
            </button>
        </div>
    </div>
</div>

@push('scripts')
<script>
function confirmDelete() {
    const form = document.getElementById('deleteReservationForm');
    const reason = form.querySelector('input[name="cancellation_reason"]:checked');
    const notes = form.querySelector('#additional_notes').value;

    // ملء تفاصيل الحجز
    document.getElementById('reservationDetails').innerHTML = `
        <div class="flex items-center justify-between py-1">
            <span class="text-gray-600 dark:text-gray-400">رقم الحجز:</span>
            <span class="font-semibold text-gray-800 dark:text-white">#{{ $reservation->reservation_id ?? 'R0002' }}</span>
        </div>
        <div class="flex items-center justify-between py-1">
            <span class="text-gray-600 dark:text-gray-400">التاريخ والوقت:</span>
            <span class="font-semibold text-gray-800 dark:text-white">{{ \Carbon\Carbon::parse($reservation->reservation_time ?? '2025-05-28 12:30')->format('d M Y - H:i') }}</span>
        </div>
        <div class="flex items-center justify-between py-1">
            <span class="text-gray-600 dark:text-gray-400">رقم الطاولة:</span>
            <span class="font-semibold text-gray-800 dark:text-white">#{{ $reservation->table_id ?? '2' }}</span>
        </div>
        <div class="flex items-center justify-between py-1">
            <span class="text-gray-600 dark:text-gray-400">عدد الأشخاص:</span>
            <span class="font-semibold text-gray-800 dark:text-white">{{ $reservation->party_size ?? '4' }} أشخاص</span>
        </div>
    `;

    // إظهار السبب إذا تم اختياره
    if (reason) {
        const reasonText = reason.nextElementSibling.textContent;
        document.getElementById('reasonText').textContent = reasonText;
        document.getElementById('selectedReason').classList.remove('hidden');
    } else {
        document.getElementById('selectedReason').classList.add('hidden');
    }

    // إظهار الملاحظات إذا تم إدخالها
    if (notes.trim()) {
        document.getElementById('notesText').textContent = notes.trim();
        document.getElementById('selectedNotes').classList.remove('hidden');
    } else {
        document.getElementById('selectedNotes').classList.add('hidden');
    }

    // إظهار النافذة
    showDeleteModal();
}

function showDeleteModal() {
    const modal = document.getElementById('deleteConfirmModal');
    const content = document.getElementById('modalContent');

    modal.classList.remove('hidden');
    setTimeout(() => {
        content.classList.remove('scale-95', 'opacity-0');
        content.classList.add('scale-100', 'opacity-100');
    }, 10);
}

function hideDeleteModal() {
    const modal = document.getElementById('deleteConfirmModal');
    const content = document.getElementById('modalContent');

    content.classList.remove('scale-100', 'opacity-100');
    content.classList.add('scale-95', 'opacity-0');

    setTimeout(() => {
        modal.classList.add('hidden');
    }, 300);
}

function proceedWithDelete() {
    const form = document.getElementById('deleteReservationForm');
    const button = document.getElementById('confirmDeleteBtn');

    // تغيير شكل الزر لإظهار التحميل
    button.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري الحذف...';
    button.disabled = true;
    button.classList.add('opacity-50');

    // جمع بيانات النموذج
    const formData = new FormData(form);
    const reason = form.querySelector('input[name="cancellation_reason"]:checked');
    const notes = form.querySelector('#additional_notes').value;

    // إرسال طلب AJAX
    fetch(form.action, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            cancellation_reason: reason ? reason.value : null,
            additional_notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إخفاء النافذة
            hideDeleteModal();

            // إظهار رسالة نجاح
            showSuccessMessage('تم إلغاء الحجز بنجاح');

            // إعادة توجيه بعد ثانيتين
            setTimeout(() => {
                window.location.href = '/customer/reservations';
            }, 2000);
        } else {
            // إظهار رسالة خطأ
            showErrorMessage(data.message || 'حدث خطأ أثناء إلغاء الحجز');

            // إعادة تعيين الزر
            button.innerHTML = '<i class="fas fa-trash ml-2"></i>تأكيد الحذف';
            button.disabled = false;
            button.classList.remove('opacity-50');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorMessage('حدث خطأ في الاتصال');

        // إعادة تعيين الزر
        button.innerHTML = '<i class="fas fa-trash ml-2"></i>تأكيد الحذف';
        button.disabled = false;
        button.classList.remove('opacity-50');
    });
}

// إغلاق النافذة عند النقر خارجها
document.getElementById('deleteConfirmModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideDeleteModal();
    }
});

// إغلاق النافذة بمفتاح Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        hideDeleteModal();
    }
});

// دالة إظهار رسالة النجاح
function showSuccessMessage(message) {
    // إنشاء عنصر الرسالة
    const messageDiv = document.createElement('div');
    messageDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-4 rounded-lg shadow-lg z-50 flex items-center';
    messageDiv.innerHTML = `
        <i class="fas fa-check-circle text-xl ml-3"></i>
        <span>${message}</span>
    `;

    // إضافة الرسالة للصفحة
    document.body.appendChild(messageDiv);

    // إزالة الرسالة بعد 3 ثوان
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}

// دالة إظهار رسالة الخطأ
function showErrorMessage(message) {
    // إنشاء عنصر الرسالة
    const messageDiv = document.createElement('div');
    messageDiv.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-4 rounded-lg shadow-lg z-50 flex items-center';
    messageDiv.innerHTML = `
        <i class="fas fa-exclamation-circle text-xl ml-3"></i>
        <span>${message}</span>
    `;

    // إضافة الرسالة للصفحة
    document.body.appendChild(messageDiv);

    // إزالة الرسالة بعد 5 ثوان
    setTimeout(() => {
        messageDiv.remove();
    }, 5000);
}

// إضافة تأثيرات تفاعلية
document.addEventListener('DOMContentLoaded', function() {
    // تمييز السبب المختار
    document.querySelectorAll('input[name="cancellation_reason"]').forEach(radio => {
        radio.addEventListener('change', function() {
            // إزالة التمييز من جميع الخيارات
            document.querySelectorAll('label').forEach(label => {
                label.classList.remove('bg-primary/10', 'border-primary');
            });

            // إضافة التمييز للخيار المختار
            if (this.checked) {
                this.closest('label').classList.add('bg-primary/10', 'border-primary', 'border', 'rounded-lg', 'p-2');
            }
        });
    });
});
</script>
@endpush
