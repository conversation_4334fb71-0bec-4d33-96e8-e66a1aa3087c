@include('customer.partials.head')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/cart-modals.css') }}">
@endpush

<body class="bg-gray-50 dark:bg-gray-900">

@include('customer.partials.header')

<!-- المحتوى الرئيسي -->
<main class="min-h-screen pt-20">
    <div class="container mx-auto px-4 py-8">
        <!-- عنوان الصفحة -->
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2">سلة التسوق</h1>
                <p class="text-gray-600 dark:text-gray-400">
                    @if($itemsCount > 0)
                        لديك {{ $itemsCount }} عنصر في السلة
                    @else
                        السلة فارغة
                    @endif
                </p>
            </div>
            @if($cartItems->count() > 0)
            <button onclick="clearCart()" class="bg-red-500 hover:bg-red-600 hover:shadow-lg text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 transform hover:scale-105">
                <i class="fas fa-trash ml-2"></i>
                مسح السلة
            </button>
            @endif
        </div>

        @if($cartItems->count() > 0)
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- عناصر السلة -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                    <div class="p-6">
                        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">عناصر السلة</h2>

                        <div class="space-y-4" id="cart-items">
                            @foreach($cartItems as $item)
                            <div class="cart-item flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg" data-cart-id="{{ $item->cart_id }}">
                                <!-- صورة المنتج -->
                                <div class="w-20 h-20 flex-shrink-0 ml-4">
                                    <img src="{{ $item->menuItem->image_url ?? 'https://images.unsplash.com/photo-1513104890138-7c749659a591' }}"
                                         alt="{{ $item->menuItem->name }}"
                                         class="w-full h-full object-cover rounded-lg">
                                </div>

                                <!-- تفاصيل المنتج -->
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-800 dark:text-white">{{ $item->menuItem->name }}</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ $item->menuItem->description }}</p>
                                    @if($item->special_instructions)
                                    <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">
                                        <i class="fas fa-sticky-note ml-1"></i>{{ $item->special_instructions }}
                                    </p>
                                    @endif

                                    @if($item->excluded_ingredients && count($item->excluded_ingredients) > 0)
                                    <div class="text-xs text-red-600 dark:text-red-400 mt-1">
                                        <i class="fas fa-ban ml-1"></i>
                                        <span class="font-medium">بدون:</span>
                                        @php
                                            $excludedNames = \App\Models\Ingredient::whereIn('ingredient_id', $item->excluded_ingredients)->pluck('name')->toArray();
                                        @endphp
                                        {{ implode('، ', $excludedNames) }}
                                    </div>
                                    @endif
                                    <div class="flex items-center mt-2">
                                        <span class="text-lg font-bold text-primary">{{ number_format($item->price, 2) }} د.ل</span>
                                    </div>
                                </div>

                                <!-- التحكم في الكمية -->
                                <div class="flex items-center space-x-2 space-x-reverse ml-4">
                                    <button onclick="changeQuantity({{ $item->cart_id }}, -1)"
                                            class="decrease-btn w-8 h-8 rounded-full flex items-center justify-center transition {{ $item->quantity <= 1 ? 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed' : 'bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300' }}"
                                            data-cart-id="{{ $item->cart_id }}" data-action="decrease"
                                            {{ $item->quantity <= 1 ? 'disabled' : '' }}>
                                        <i class="fas fa-minus text-sm"></i>
                                    </button>
                                    <span class="quantity-display w-12 text-center font-medium text-gray-800 dark:text-white" data-cart-id="{{ $item->cart_id }}">{{ $item->quantity }}</span>
                                    <button onclick="changeQuantity({{ $item->cart_id }}, 1)"
                                            class="increase-btn w-8 h-8 rounded-full flex items-center justify-center transition {{ $item->quantity >= 10 ? 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed' : 'bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300' }}"
                                            data-cart-id="{{ $item->cart_id }}" data-action="increase"
                                            {{ $item->quantity >= 10 ? 'disabled' : '' }}>
                                        <i class="fas fa-plus text-sm"></i>
                                    </button>
                                </div>

                                <!-- المجموع الفرعي -->
                                <div class="text-left ml-4">
                                    <div class="item-subtotal text-lg font-bold text-gray-800 dark:text-white">
                                        {{ number_format($item->subtotal, 2) }} د.ل
                                    </div>
                                    <button onclick="removeItem({{ $item->cart_id }})"
                                            class="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 text-sm mt-1 transition-all duration-200 px-2 py-1 rounded-md">
                                        <i class="fas fa-trash ml-1"></i>حذف
                                    </button>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <!-- ملخص الطلب -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 sticky top-24">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">ملخص الطلب</h2>

                    <div class="space-y-4 mb-6">
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">المجموع الفرعي:</span>
                            <span id="cart-total" class="font-medium text-gray-800 dark:text-white">{{ number_format($total, 2) }} د.ل</span>
                        </div>

                        @if(isset($cartOffers) && $cartOffers->count() > 0)
                        <!-- العروض المطبقة -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4" id="applied-offers">
                            <h4 class="text-sm font-medium text-gray-800 dark:text-white mb-3">العروض المطبقة:</h4>
                            @foreach($cartOffers as $cartOffer)
                            <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3 mb-2">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i class="fas fa-tag text-green-600 dark:text-green-400 ml-2"></i>
                                        <div>
                                            <div class="text-sm font-medium text-green-800 dark:text-green-200">{{ $cartOffer->offer->title }}</div>
                                            <div class="text-xs text-green-600 dark:text-green-400">
                                                @if($cartOffer->offer->type === 'حجز')
                                                    عرض حجز - {{ number_format($cartOffer->discount_amount, 2) }} د.ل
                                                @elseif($cartOffer->offer->offer_type === 'buy_get')
                                                    🎁 اشتري {{ $cartOffer->offer->buy_quantity }} واحصل على {{ $cartOffer->offer->get_quantity }} مجاناً
                                                    <br><span class="text-green-700 dark:text-green-300 font-medium">توفير: {{ number_format($cartOffer->discount_amount, 2) }} د.ل</span>
                                                @else
                                                    خصم {{ number_format($cartOffer->discount_amount, 2) }} د.ل
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    <button onclick="removeOffer({{ $cartOffer->cart_offer_id }})"
                                            class="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 transition-colors">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            @endforeach
                            <div class="flex justify-between text-sm font-medium text-green-600 dark:text-green-400 border-t border-green-200 dark:border-green-700 pt-2">
                                <span>إجمالي الخصم:</span>
                                <span id="total-discount">-{{ number_format($offersDiscount ?? 0, 2) }} د.ل</span>
                            </div>
                        </div>
                        @endif

                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">الضريبة ({{ $taxRate }}%):</span>
                            <span class="font-medium text-gray-800 dark:text-white">{{ $settingsHelper->formatCurrency($settingsHelper->calculateTax($finalTotal ?? $total)) }}</span>
                        </div>

                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                            <div class="flex justify-between">
                                <span class="text-lg font-semibold text-gray-800 dark:text-white">المجموع الكلي:</span>
                                <span id="final-total" class="text-lg font-bold text-primary">{{ $settingsHelper->formatCurrency($settingsHelper->calculateTotal($finalTotal ?? $total)) }}</span>
                            </div>
                        </div>
                    </div>



                    <div class="space-y-3">
                        <button onclick="proceedToCheckout()"
                                class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition">
                            <i class="fas fa-credit-card ml-2"></i>
                            متابعة للدفع
                        </button>
                        <a href="{{ route('customer.menu') }}"
                           class="w-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-3 px-6 rounded-lg transition text-center block">
                            <i class="fas fa-arrow-right ml-2"></i>
                            متابعة التسوق
                        </a>
                    </div>
                </div>
            </div>
        </div>

        @else
        <!-- حالة السلة الفارغة -->
        <div class="max-w-2xl mx-auto">
            <!-- العروض المطبقة (إن وجدت) -->
            @if(isset($cartOffers) && $cartOffers->count() > 0)
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-6">
                <h4 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-tag text-green-600 dark:text-green-400 ml-2"></i>
                    العروض المطبقة
                </h4>
                @foreach($cartOffers as $cartOffer)
                <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-3">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-gift text-green-600 dark:text-green-400 ml-3"></i>
                            <div>
                                <div class="font-medium text-green-800 dark:text-green-200">{{ $cartOffer->offer->title }}</div>
                                <div class="text-sm text-green-600 dark:text-green-400">
                                    @if($cartOffer->offer->type === 'حجز')
                                        عرض حجز - {{ number_format($cartOffer->discount_amount, 2) }} د.ل
                                    @else
                                        خصم {{ $cartOffer->offer->discount_percentage ?? 0 }}% - سيتم تطبيقه عند إضافة عناصر
                                    @endif
                                </div>
                            </div>
                        </div>
                        <button onclick="removeOffer({{ $cartOffer->cart_offer_id }})"
                                class="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 transition-colors">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                @endforeach
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3 mt-4">
                    <div class="flex items-center">
                        <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 ml-2"></i>
                        <span class="text-sm text-blue-800 dark:text-blue-200">
                            تم تطبيق العروض بنجاح! أضف عناصر للسلة للاستفادة من الخصم
                        </span>
                    </div>
                </div>
            </div>
            @endif

            <!-- رسالة السلة الفارغة -->
            <div class="text-center py-12">
                <div class="max-w-md mx-auto">
                    <i class="fas fa-shopping-cart text-6xl text-gray-300 dark:text-gray-600 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">السلة فارغة</h3>
                    <p class="text-gray-500 dark:text-gray-500 mb-6">
                        @if(isset($cartOffers) && $cartOffers->count() > 0)
                            لديك عروض مطبقة! أضف عناصر للسلة للاستفادة من الخصم
                        @else
                            لم تقم بإضافة أي عناصر للسلة بعد
                        @endif
                    </p>
                    <a href="{{ route('customer.menu') }}"
                       class="inline-block bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition">
                        <i class="fas fa-utensils ml-2"></i>
                        تصفح القائمة
                    </a>
                </div>
            </div>
        </div>
        @endif
    </div>
</main>

@include('customer.partials.footer')

<!-- سكريبت إدارة السلة -->
<script>
// إظهار رسالة نجاح تطبيق العرض إذا كانت موجودة في URL
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('offer_applied') === '1') {
        const message = urlParams.get('message') || 'تم تطبيق العرض بنجاح';
        showSimpleNotification(message, 'success');

        // إزالة المعاملات من URL
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
    }
});

// دالة إظهار الإشعارات البسيطة
function showSimpleNotification(message, type = 'success') {
    const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
    const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';

    const alertDiv = document.createElement('div');
    alertDiv.className = `fixed top-20 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300`;
    alertDiv.innerHTML = `
        <div class="flex items-center">
            <i class="${icon} ml-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(alertDiv);

    // إظهار الرسالة
    setTimeout(() => {
        alertDiv.classList.remove('translate-x-full');
    }, 100);

    // إخفاء الرسالة بعد 3 ثوان
    setTimeout(() => {
        alertDiv.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(alertDiv);
        }, 300);
    }, 3000);
}
// تغيير الكمية (زيادة أو نقصان)
function changeQuantity(cartId, change) {
    const quantityElement = document.querySelector(`[data-cart-id="${cartId}"].quantity-display`);
    const currentQuantity = parseInt(quantityElement.textContent);
    const newQuantity = currentQuantity + change;

    // منع النقصان تحت 1 أو الزيادة فوق 10
    if (newQuantity < 1) {
        showSimpleNotification('لا يمكن أن تكون الكمية أقل من 1', 'warning');
        return;
    }

    if (newQuantity > 10) {
        showSimpleNotification('الحد الأقصى للكمية هو 10', 'warning');
        return;
    }

    updateQuantity(cartId, newQuantity);
}

// تحديث حالة الأزرار حسب الكمية
function updateButtonStates(cartId, quantity) {
    const cartItem = document.querySelector(`[data-cart-id="${cartId}"]`);
    const decreaseBtn = cartItem.querySelector('.decrease-btn');
    const increaseBtn = cartItem.querySelector('.increase-btn');

    // تحديث زر النقصان
    if (quantity <= 1) {
        decreaseBtn.disabled = true;
        decreaseBtn.className = 'decrease-btn w-8 h-8 rounded-full flex items-center justify-center transition bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed';
    } else {
        decreaseBtn.disabled = false;
        decreaseBtn.className = 'decrease-btn w-8 h-8 rounded-full flex items-center justify-center transition bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300';
    }

    // تحديث زر الزيادة
    if (quantity >= 10) {
        increaseBtn.disabled = true;
        increaseBtn.className = 'increase-btn w-8 h-8 rounded-full flex items-center justify-center transition bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed';
    } else {
        increaseBtn.disabled = false;
        increaseBtn.className = 'increase-btn w-8 h-8 rounded-full flex items-center justify-center transition bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300';
    }
}

// تحديث الكمية
function updateQuantity(cartId, newQuantity) {
    if (newQuantity < 1 || newQuantity > 10) return;

    fetch(`/customer/cart/update/${cartId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ quantity: newQuantity })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('تحديث الكمية - البيانات المستلمة:', data);

            // تحديث العرض
            const cartItem = document.querySelector(`[data-cart-id="${cartId}"]`);
            cartItem.querySelector('.quantity-display').textContent = newQuantity;
            cartItem.querySelector('.item-subtotal').textContent = data.item_subtotal + ' د.ل';

            // تحديث جميع المجاميع
            const cartSubtotal = parseFloat(data.cart_subtotal.replace(' د.ل', ''));
            const offersDiscount = parseFloat(data.offers_discount || 0);
            const finalTotal = parseFloat(data.cart_total.replace(' د.ل', ''));

            updateAllTotals(cartSubtotal, offersDiscount, finalTotal);

            // تحديث قسم العروض المطبقة
            updateAppliedOffersSection();

            // تحديث حالة الأزرار
            updateButtonStates(cartId, newQuantity);

            // إشعار بسيط بدون نافذة منبثقة
            showSimpleNotification('تم تحديث الكمية بنجاح', 'success');
        } else {
            console.error('خطأ في تحديث الكمية:', data.message);
            showSimpleNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showSimpleNotification('حدث خطأ أثناء تحديث الكمية', 'error');
    });
}

// متغيرات للحذف
let currentDeleteCartId = null;

// حذف عنصر - إظهار النافذة المخصصة
function removeItem(cartId) {
    currentDeleteCartId = cartId;

    // الحصول على معلومات العنصر
    const cartItem = document.querySelector(`[data-cart-id="${cartId}"]`);
    const itemName = cartItem.querySelector('.font-semibold').textContent;
    const itemPrice = cartItem.querySelector('.text-gray-600').textContent;
    const itemImage = cartItem.querySelector('img').src;

    // تحديث محتوى النافذة
    document.getElementById('deleteItemName').textContent = itemName;
    document.getElementById('deleteItemPrice').textContent = itemPrice;
    document.getElementById('deleteItemImg').src = itemImage;
    document.getElementById('deleteItemImg').alt = itemName;

    // إظهار النافذة
    showDeleteModal();
}

// إظهار نافذة الحذف
function showDeleteModal() {
    const modal = document.getElementById('deleteModal');
    const content = document.getElementById('deleteModalContent');

    modal.classList.remove('hidden');

    // تأثير الظهور
    setTimeout(() => {
        content.classList.remove('scale-95', 'opacity-0');
        content.classList.add('scale-100', 'opacity-100');
    }, 10);
}

// إغلاق نافذة الحذف
function closeDeleteModal() {
    const modal = document.getElementById('deleteModal');
    const content = document.getElementById('deleteModalContent');

    content.classList.remove('scale-100', 'opacity-100');
    content.classList.add('scale-95', 'opacity-0');

    setTimeout(() => {
        modal.classList.add('hidden');
        currentDeleteCartId = null;
    }, 300);
}

// تأكيد الحذف
function confirmDelete() {
    if (!currentDeleteCartId) return;

    const confirmBtn = document.getElementById('confirmDeleteBtn');
    const originalText = confirmBtn.innerHTML;

    // تغيير النص أثناء التحميل
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري الحذف...';
    confirmBtn.disabled = true;

    fetch(`/customer/cart/remove/${currentDeleteCartId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إغلاق النافذة
            closeDeleteModal();

            // حذف العنصر من الصفحة
            document.querySelector(`[data-cart-id="${currentDeleteCartId}"]`).remove();

            if (data.cart_count === 0) {
                location.reload();
            } else {
                console.log('حذف عنصر - البيانات المستلمة:', data);

                // تحديث جميع المجاميع
                const cartSubtotal = parseFloat(data.cart_subtotal ? data.cart_subtotal.replace(' د.ل', '') : data.cart_total.replace(' د.ل', ''));
                const offersDiscount = parseFloat(data.offers_discount || 0);
                const finalTotal = parseFloat(data.cart_total.replace(' د.ل', ''));

                updateAllTotals(cartSubtotal, offersDiscount, finalTotal);
            }

            showNotification(data.message, 'success');
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('حدث خطأ أثناء حذف العنصر', 'error');
    })
    .finally(() => {
        // إعادة النص الأصلي
        confirmBtn.innerHTML = originalText;
        confirmBtn.disabled = false;
    });
}

// مسح السلة - إظهار النافذة المخصصة
function clearCart() {
    showClearCartModal();
}

// إظهار نافذة مسح السلة
function showClearCartModal() {
    const modal = document.getElementById('clearCartModal');
    const content = document.getElementById('clearCartModalContent');

    modal.classList.remove('hidden');

    // تأثير الظهور
    setTimeout(() => {
        content.classList.remove('scale-95', 'opacity-0');
        content.classList.add('scale-100', 'opacity-100');
    }, 10);
}

// إغلاق نافذة مسح السلة
function closeClearCartModal() {
    const modal = document.getElementById('clearCartModal');
    const content = document.getElementById('clearCartModalContent');

    content.classList.remove('scale-100', 'opacity-100');
    content.classList.add('scale-95', 'opacity-0');

    setTimeout(() => {
        modal.classList.add('hidden');
    }, 300);
}

// تأكيد مسح السلة
function confirmClearCart() {
    const confirmBtn = document.getElementById('confirmClearCartBtn');
    const originalText = confirmBtn.innerHTML;

    // تغيير النص أثناء التحميل
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري المسح...';
    confirmBtn.disabled = true;

    fetch('/customer/cart/clear', {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إغلاق النافذة
            closeClearCartModal();

            // إعادة تحميل الصفحة
            setTimeout(() => {
                location.reload();
            }, 500);
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('حدث خطأ أثناء مسح السلة', 'error');
    })
    .finally(() => {
        // إعادة النص الأصلي
        confirmBtn.innerHTML = originalText;
        confirmBtn.disabled = false;
    });
}

// عرض الإشعارات البسيطة
function showSimpleNotification(message, type) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white text-sm transition-all duration-300 transform translate-x-full ${
        type === 'success' ? 'bg-green-500' :
        type === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
    }`;
    notification.textContent = message;

    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);

    // إظهار الإشعار
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // إخفاء الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// عرض الإشعارات (للحالات الخاصة)
function showNotification(message, type) {
    showSimpleNotification(message, type);
}

// دالة تحديث جميع المجاميع في الصفحة
function updateAllTotals(cartSubtotal, offersDiscount = 0, finalTotal = null) {
    console.log('تحديث جميع المجاميع:', { cartSubtotal, offersDiscount, finalTotal });

    // تحديث المجموع الفرعي (قبل الخصم)
    const cartTotalElements = document.querySelectorAll('#cart-total, .cart-total');
    cartTotalElements.forEach(element => {
        if (element) {
            element.textContent = cartSubtotal + ' د.ل';
            console.log('تم تحديث عنصر المجموع الفرعي');
        }
    });

    // تحديث خصم العروض
    const offersDiscountElements = document.querySelectorAll('#offers-discount, .offers-discount');
    offersDiscountElements.forEach(element => {
        if (element) {
            element.textContent = offersDiscount + ' د.ل';
            console.log('تم تحديث عنصر خصم العروض');

            // إظهار/إخفاء قسم خصم العروض
            const offersSection = element.closest('.offers-section, .discount-section');
            if (offersSection) {
                if (offersDiscount > 0) {
                    offersSection.style.display = 'block';
                } else {
                    offersSection.style.display = 'none';
                }
            }
        }
    });

    // تحديث المجموع النهائي
    const calculatedFinalTotal = finalTotal !== null ? finalTotal : (cartSubtotal - offersDiscount);
    const finalTotalElements = document.querySelectorAll('#final-total, .final-total');
    finalTotalElements.forEach(element => {
        if (element) {
            element.textContent = calculatedFinalTotal + ' د.ل';
            console.log('تم تحديث عنصر المجموع النهائي:', calculatedFinalTotal);
        }
    });
}

// تحديث قسم العروض المطبقة
function updateAppliedOffersSection() {
    fetch('/customer/cart/offers')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const appliedOffersContainer = document.getElementById('applied-offers');
                if (appliedOffersContainer) {
                    if (data.offers && data.offers.length > 0) {
                        // إظهار قسم العروض
                        appliedOffersContainer.closest('.border-t').style.display = 'block';

                        // تحديث محتوى العروض
                        let offersHtml = '<h4 class="text-sm font-medium text-gray-800 dark:text-white mb-3">العروض المطبقة:</h4>';
                        data.offers.forEach(offer => {
                            offersHtml += `
                                <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3 mb-2">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <i class="fas fa-tag text-green-600 dark:text-green-400 ml-2"></i>
                                            <span class="text-sm font-medium text-gray-800 dark:text-white">${offer.title}</span>
                                        </div>
                                        <div class="flex items-center">
                                            <span class="text-sm font-bold text-green-600 dark:text-green-400">-${offer.discount_amount} د.ل</span>
                                            <button onclick="removeOffer(${offer.offer_id})"
                                                    class="mr-2 text-red-500 hover:text-red-700 transition-colors">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            `;
                        });
                        appliedOffersContainer.innerHTML = offersHtml;
                    } else {
                        // إخفاء قسم العروض إذا لم تعد هناك عروض
                        appliedOffersContainer.closest('.border-t').style.display = 'none';
                    }
                }
            }
        })
        .catch(error => {
            console.error('خطأ في تحديث العروض:', error);
        });
}

// الانتقال لصفحة الدفع
function proceedToCheckout() {
    window.location.href = `{{ route('customer.cart.checkout') }}`;
}



// تهيئة حالة الأزرار عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحديث حالة جميع الأزرار
    document.querySelectorAll('.quantity-display').forEach(function(quantityElement) {
        const cartId = quantityElement.getAttribute('data-cart-id');
        const quantity = parseInt(quantityElement.textContent);
        updateButtonStates(cartId, quantity);
    });

    // إضافة مستمعات الأحداث للنوافذ
    setupModalEventListeners();
});

// إعداد مستمعات الأحداث للنوافذ
function setupModalEventListeners() {
    // إغلاق النوافذ بالضغط خارجها
    document.getElementById('deleteModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeDeleteModal();
        }
    });

    document.getElementById('clearCartModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeClearCartModal();
        }
    });

    document.getElementById('deleteOfferModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeDeleteOfferModal();
        }
    });

    // إغلاق النوافذ بمفتاح Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            if (!document.getElementById('deleteModal').classList.contains('hidden')) {
                closeDeleteModal();
            }
            if (!document.getElementById('clearCartModal').classList.contains('hidden')) {
                closeClearCartModal();
            }
            if (!document.getElementById('deleteOfferModal').classList.contains('hidden')) {
                closeDeleteOfferModal();
            }
        }
    });
}

// متغيرات لحذف العروض
let currentDeleteOfferId = null;

// دالة حذف عرض من السلة - إظهار النافذة المخصصة
function removeOffer(cartOfferId) {
    currentDeleteOfferId = cartOfferId;

    // الحصول على معلومات العرض
    const offerButton = document.querySelector(`[onclick="removeOffer(${cartOfferId})"]`);
    const offerElement = offerButton.closest('.bg-green-50, .dark\\:bg-green-900\\/20');

    if (offerElement) {
        const titleElement = offerElement.querySelector('.font-medium, .font-semibold');
        const descriptionElement = offerElement.querySelector('.text-sm');

        // استخراج معلومات العرض
        const title = titleElement ? titleElement.textContent.trim() : 'عرض خاص';
        let description = '';
        let savings = '';

        if (descriptionElement) {
            const fullText = descriptionElement.textContent.trim();
            if (fullText.includes('توفير:')) {
                const parts = fullText.split('توفير:');
                description = parts[0].trim();
                savings = 'توفير: ' + parts[1].trim();
            } else {
                description = fullText;
            }
        }

        // تحديث محتوى النافذة
        document.getElementById('deleteOfferTitle').textContent = title;
        document.getElementById('deleteOfferDescription').textContent = description;
        document.getElementById('deleteOfferSavings').textContent = savings;
    }

    // إظهار النافذة
    showDeleteOfferModal();
}

// إظهار نافذة حذف العرض
function showDeleteOfferModal() {
    const modal = document.getElementById('deleteOfferModal');
    const content = document.getElementById('deleteOfferModalContent');

    modal.classList.remove('hidden');

    // تأثير الظهور
    setTimeout(() => {
        content.classList.remove('scale-95', 'opacity-0');
        content.classList.add('scale-100', 'opacity-100');
    }, 10);
}

// إغلاق نافذة حذف العرض
function closeDeleteOfferModal() {
    const modal = document.getElementById('deleteOfferModal');
    const content = document.getElementById('deleteOfferModalContent');

    content.classList.remove('scale-100', 'opacity-100');
    content.classList.add('scale-95', 'opacity-0');

    setTimeout(() => {
        modal.classList.add('hidden');
        currentDeleteOfferId = null;
    }, 300);
}

// تأكيد حذف العرض
function confirmDeleteOffer() {
    if (!currentDeleteOfferId) return;

    const confirmBtn = document.getElementById('confirmDeleteOfferBtn');
    const originalText = confirmBtn.innerHTML;

    // إظهار حالة التحميل
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري الحذف...';
    confirmBtn.disabled = true;

    fetch(`{{ route('customer.cart.remove-offer', '') }}/${currentDeleteOfferId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إغلاق النافذة
            closeDeleteOfferModal();

            // إزالة العرض من الواجهة
            const offerElement = document.querySelector(`[onclick="removeOffer(${currentDeleteOfferId})"]`).closest('.bg-green-50, .dark\\:bg-green-900\\/20');
            if (offerElement) {
                // تأثير الاختفاء المتقدم
                offerElement.style.transform = 'scale(0.9) translateX(20px)';
                offerElement.style.opacity = '0';
                offerElement.style.filter = 'blur(2px)';
                offerElement.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';

                // إضافة تأثير الهز قبل الحذف
                offerElement.animate([
                    { transform: 'translateX(0)' },
                    { transform: 'translateX(-5px)' },
                    { transform: 'translateX(5px)' },
                    { transform: 'translateX(0)' }
                ], {
                    duration: 200,
                    easing: 'ease-in-out'
                });

                setTimeout(() => {
                    offerElement.remove();
                }, 400);
            }

            // تحديث المجاميع
            document.getElementById('total-discount').textContent = `-${data.offers_discount} د.ل`;
            document.getElementById('final-total').textContent = `${data.cart_total} د.ل`;

            // إظهار رسالة نجاح
            showSimpleNotification(data.message, 'success');

            // إخفاء قسم العروض إذا لم تعد هناك عروض
            const appliedOffersContainer = document.getElementById('applied-offers');
            if (appliedOffersContainer && appliedOffersContainer.children.length === 0) {
                appliedOffersContainer.closest('.border-t').style.display = 'none';
            }
        } else {
            showSimpleNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showSimpleNotification('حدث خطأ أثناء إلغاء العرض', 'error');
    })
    .finally(() => {
        // إعادة النص الأصلي
        confirmBtn.innerHTML = originalText;
        confirmBtn.disabled = false;
    });
}
</script>

<!-- نافذة تأكيد الحذف -->
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4 modal-backdrop">
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300 scale-95 opacity-0 modal-content" id="deleteModalContent">
        <!-- رأس النافذة -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center ml-4">
                    <i class="fas fa-trash-alt text-red-600 dark:text-red-400 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">حذف العنصر</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">تأكيد عملية الحذف</p>
                </div>
            </div>
            <button onclick="closeDeleteModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- محتوى النافذة -->
        <div class="p-6">
            <div class="flex items-start mb-6">
                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-lg ml-4 flex-shrink-0" id="deleteItemImage">
                    <img src="" alt="" class="w-full h-full object-cover rounded-lg" id="deleteItemImg">
                </div>
                <div class="flex-1">
                    <h4 class="font-semibold text-gray-900 dark:text-white mb-1" id="deleteItemName">اسم المنتج</h4>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-2" id="deleteItemPrice">السعر</p>
                    <p class="text-sm text-gray-600 dark:text-gray-300">هل أنت متأكد من حذف هذا العنصر من السلة؟</p>
                </div>
            </div>

            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400 ml-2"></i>
                    <p class="text-sm text-yellow-800 dark:text-yellow-200">
                        سيتم حذف هذا العنصر نهائياً من سلة التسوق
                    </p>
                </div>
            </div>
        </div>

        <!-- أزرار التحكم -->
        <div class="flex items-center justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50 rounded-b-2xl">
            <button onclick="closeDeleteModal()"
                    class="px-6 py-2.5 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-500 transition-colors font-medium">
                <i class="fas fa-times ml-2"></i>إلغاء
            </button>
            <button onclick="confirmDelete()"
                    id="confirmDeleteBtn"
                    class="px-6 py-2.5 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-all duration-200 font-medium delete-btn focus-ring transform hover:scale-105">
                <i class="fas fa-trash ml-2"></i>حذف العنصر
            </button>
        </div>
    </div>
</div>

<!-- نافذة تأكيد مسح السلة -->
<div id="clearCartModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4 modal-backdrop">
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300 scale-95 opacity-0 modal-content" id="clearCartModalContent">
        <!-- رأس النافذة -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center ml-4">
                    <i class="fas fa-trash text-red-600 dark:text-red-400 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">مسح السلة</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">حذف جميع العناصر</p>
                </div>
            </div>
            <button onclick="closeClearCartModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- محتوى النافذة -->
        <div class="p-6">
            <div class="text-center mb-6">
                <div class="w-20 h-20 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-shopping-cart text-red-600 dark:text-red-400 text-2xl"></i>
                </div>
                <h4 class="font-semibold text-gray-900 dark:text-white mb-2">مسح السلة بالكامل؟</h4>
                <p class="text-sm text-gray-600 dark:text-gray-300">
                    هل أنت متأكد من حذف جميع العناصر من السلة؟
                </p>
            </div>

            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle text-red-600 dark:text-red-400 ml-2"></i>
                    <p class="text-sm text-red-800 dark:text-red-200">
                        سيتم حذف جميع العناصر نهائياً ولا يمكن التراجع عن هذا الإجراء
                    </p>
                </div>
            </div>
        </div>

        <!-- أزرار التحكم -->
        <div class="flex items-center justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50 rounded-b-2xl">
            <button onclick="closeClearCartModal()"
                    class="px-6 py-2.5 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-500 transition-colors font-medium">
                <i class="fas fa-times ml-2"></i>إلغاء
            </button>
            <button onclick="confirmClearCart()"
                    id="confirmClearCartBtn"
                    class="px-6 py-2.5 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-all duration-200 font-medium delete-btn focus-ring transform hover:scale-105">
                <i class="fas fa-trash ml-2"></i>مسح السلة
            </button>
        </div>
    </div>
</div>

<!-- نافذة تأكيد حذف العروض -->
<div id="deleteOfferModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4 modal-backdrop">
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300 scale-95 opacity-0 modal-content" id="deleteOfferModalContent">
        <!-- رأس النافذة -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900/30 dark:to-orange-800/30 rounded-full flex items-center justify-center ml-4 shadow-sm">
                    <i class="fas fa-gift text-orange-600 dark:text-orange-400 text-xl animate-pulse"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">إلغاء العرض</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">تأكيد إلغاء العرض من السلة</p>
                </div>
            </div>
            <button onclick="closeDeleteOfferModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors hover:rotate-90 transform duration-200">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- محتوى النافذة -->
        <div class="p-6">
            <div class="text-center mb-6">
                <p class="text-gray-600 dark:text-gray-400 mb-4">هل أنت متأكد من رغبتك في إلغاء هذا العرض من السلة؟</p>

                <!-- معاينة العرض -->
                <div class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-4 shadow-sm">
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-100 to-green-200 dark:from-green-800 dark:to-green-700 rounded-full flex items-center justify-center shadow-sm">
                            <i class="fas fa-percentage text-green-600 dark:text-green-400 text-lg"></i>
                        </div>
                        <div class="flex-1 text-right">
                            <h4 id="deleteOfferTitle" class="font-semibold text-green-800 dark:text-green-200 mb-1"></h4>
                            <p id="deleteOfferDescription" class="text-sm text-green-600 dark:text-green-400 mb-1"></p>
                            <p id="deleteOfferSavings" class="text-xs text-green-700 dark:text-green-300 font-medium bg-green-100 dark:bg-green-800/50 px-2 py-1 rounded-full inline-block"></p>
                        </div>
                    </div>
                </div>

                <!-- تحذير -->
                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3 mb-4">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400"></i>
                        <p class="text-sm text-yellow-700 dark:text-yellow-300">
                            سيتم إعادة حساب إجمالي السلة بدون هذا العرض
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار التحكم -->
        <div class="flex items-center justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50 rounded-b-2xl">
            <button onclick="closeDeleteOfferModal()"
                    class="px-6 py-2.5 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-500 transition-all duration-200 font-medium hover:scale-105 hover:shadow-md">
                <i class="fas fa-times ml-2"></i>إلغاء
            </button>
            <button onclick="confirmDeleteOffer()"
                    id="confirmDeleteOfferBtn"
                    class="px-6 py-2.5 bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 text-white rounded-lg transition-all duration-200 font-medium hover:scale-105 hover:shadow-lg transform">
                <i class="fas fa-gift ml-2"></i>إلغاء العرض
            </button>
        </div>
    </div>
</div>

@include('customer.partials.scripts')

</body>
</html>
