@extends('layouts.admin')

@section('title', 'الملف الشخصي - لوحة تحكم Eat Hub')

@section('page-title', 'الملف الشخصي')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-gray-50 via-orange-50 to-red-50 dark:from-gray-900 dark:via-orange-900/20 dark:to-red-900/30 py-8">
    <div class="container mx-auto px-4">
        <!-- Header Section -->
        <div class="text-center mb-12">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-orange-500 to-red-600 rounded-full mb-6 shadow-2xl">
                <i class="fas fa-user-crown text-white text-3xl"></i>
            </div>
            <h1 class="text-4xl font-black text-gray-900 dark:text-white mb-3">الملف الشخصي</h1>
            <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">إدارة معلوماتك الشخصية وإعدادات حسابك كمدير للنظام</p>
        </div>

        <!-- Alert Messages -->
        @if(session('success'))
        <div class="mb-8 max-w-4xl mx-auto">
            <div class="bg-gradient-to-r from-green-500 to-emerald-600 text-white p-4 rounded-xl shadow-lg flex items-center">
                <i class="fas fa-check-circle text-2xl ml-3"></i>
                <span class="font-semibold">{{ session('success') }}</span>
            </div>
        </div>
        @endif

        @if(session('error'))
        <div class="mb-8 max-w-4xl mx-auto">
            <div class="bg-gradient-to-r from-red-500 to-pink-600 text-white p-4 rounded-xl shadow-lg flex items-center">
                <i class="fas fa-exclamation-circle text-2xl ml-3"></i>
                <span class="font-semibold">{{ session('error') }}</span>
            </div>
        </div>
        @endif

        @if($errors->any())
        <div class="mb-8 max-w-4xl mx-auto">
            <div class="bg-gradient-to-r from-red-500 to-pink-600 text-white p-4 rounded-xl shadow-lg">
                <div class="flex items-center mb-2">
                    <i class="fas fa-exclamation-triangle text-2xl ml-3"></i>
                    <span class="font-semibold">يرجى تصحيح الأخطاء التالية:</span>
                </div>
                <ul class="list-disc list-inside mr-8">
                    @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        </div>
        @endif

        <!-- Main Content -->
        <div class="max-w-6xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Profile Card -->
                <div class="lg:col-span-1">
                    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 text-center">
                        <div class="relative group mb-6">
                            <div class="w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-orange-500 to-red-600 flex items-center justify-center text-white text-4xl font-bold overflow-hidden shadow-2xl">
                                @if($user->profile_image)
                                    <img src="{{ asset('storage/' . $user->profile_image) }}" alt="{{ $user->first_name }}" class="w-full h-full object-cover">
                                @else
                                    {{ substr($user->first_name, 0, 1) }}
                                @endif
                            </div>
                            <div class="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 cursor-pointer">
                                <label for="profile_image" class="text-white text-sm cursor-pointer flex items-center">
                                    <i class="fas fa-camera ml-2"></i>
                                    تغيير الصورة
                                </label>
                            </div>
                            <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center shadow-lg">
                                <i class="fas fa-crown text-white text-sm"></i>
                            </div>
                        </div>

                        <h3 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">{{ $user->first_name }} {{ $user->last_name }}</h3>
                        <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 text-orange-700 dark:text-orange-300 rounded-full text-sm font-semibold mb-6">
                            <i class="fas fa-shield-alt ml-2"></i>
                            {{ $user->user_type == 'admin' ? 'مدير النظام' : ($user->user_type == 'employee' ? 'موظف' : 'عميل') }}
                        </div>

                        <!-- Contact Info -->
                        <div class="space-y-4">
                            <div class="flex items-center justify-center text-gray-600 dark:text-gray-400">
                                <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center ml-3">
                                    <i class="fas fa-envelope text-blue-600 dark:text-blue-400"></i>
                                </div>
                                <span class="text-sm">{{ $user->email }}</span>
                            </div>

                            @if($user->phone)
                            <div class="flex items-center justify-center text-gray-600 dark:text-gray-400">
                                <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center ml-3">
                                    <i class="fas fa-phone text-green-600 dark:text-green-400"></i>
                                </div>
                                <span class="text-sm">{{ $user->phone }}</span>
                            </div>
                            @endif

                            <div class="flex items-center justify-center text-gray-600 dark:text-gray-400">
                                <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center ml-3">
                                    <i class="fas fa-calendar text-purple-600 dark:text-purple-400"></i>
                                </div>
                                <span class="text-sm">عضو منذ {{ \Carbon\Carbon::parse($user->created_at)->format('Y/m/d') }}</span>
                            </div>
                        </div>

                        <!-- Stats -->
                        <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                            <div class="grid grid-cols-2 gap-4">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">{{ \App\Models\User::count() }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">إجمالي المستخدمين</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ \App\Models\User::where('is_active', true)->count() }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">المستخدمين النشطين</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Section -->
                <div class="lg:col-span-2 space-y-8">
                    <form action="{{ route('admin.profile.update') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        <input type="file" id="profile_image" name="profile_image" class="hidden" accept="image/*" onchange="document.getElementById('submit-form').click()">

                        <!-- Personal Information -->
                        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 mb-8">
                            <div class="flex items-center mb-6">
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center ml-4">
                                    <i class="fas fa-user text-white text-xl"></i>
                                </div>
                                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">المعلومات الشخصية</h3>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="space-y-2">
                                    <label for="first_name" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">الاسم الأول</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <i class="fas fa-user text-gray-400"></i>
                                        </div>
                                        <input type="text" id="first_name" name="first_name" value="{{ $user->first_name }}"
                                               class="w-full pl-4 pr-10 py-3 rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-300">
                                    </div>
                                </div>

                                <div class="space-y-2">
                                    <label for="last_name" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">الاسم الأخير</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <i class="fas fa-user text-gray-400"></i>
                                        </div>
                                        <input type="text" id="last_name" name="last_name" value="{{ $user->last_name }}"
                                               class="w-full pl-4 pr-10 py-3 rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-300">
                                    </div>
                                </div>

                                <div class="space-y-2">
                                    <label for="email" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">البريد الإلكتروني</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <i class="fas fa-envelope text-gray-400"></i>
                                        </div>
                                        <input type="email" id="email" name="email" value="{{ $user->email }}"
                                               class="w-full pl-4 pr-10 py-3 rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-300">
                                    </div>
                                </div>

                                <div class="space-y-2">
                                    <label for="phone" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">رقم الهاتف</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <i class="fas fa-phone text-gray-400"></i>
                                        </div>
                                        <input type="text" id="phone" name="phone" value="{{ $user->phone }}"
                                               class="w-full pl-4 pr-10 py-3 rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-300">
                                    </div>
                                </div>

                                <div class="md:col-span-2 space-y-2">
                                    <label for="address" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">العنوان</label>
                                    <div class="relative">
                                        <div class="absolute top-3 right-3 pointer-events-none">
                                            <i class="fas fa-map-marker-alt text-gray-400"></i>
                                        </div>
                                        <textarea id="address" name="address" rows="3"
                                                  class="w-full pl-4 pr-10 py-3 rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-300 resize-none">{{ $user->address ?? '' }}</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- App Settings -->
                        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 mb-8">
                            <div class="flex items-center mb-6">
                                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-xl flex items-center justify-center ml-4">
                                    <i class="fas fa-cogs text-white text-xl"></i>
                                </div>
                                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">إعدادات التطبيق</h3>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="space-y-2">
                                    <label for="language" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">اللغة المفضلة</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <i class="fas fa-language text-gray-400"></i>
                                        </div>
                                        <select id="language" name="language"
                                                class="w-full pl-4 pr-10 py-3 rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 appearance-none">
                                            <option value="ar" {{ ($user->language ?? 'ar') == 'ar' ? 'selected' : '' }}>العربية</option>
                                            <option value="en" {{ ($user->language ?? '') == 'en' ? 'selected' : '' }}>الإنجليزية</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="space-y-2">
                                    <label for="theme" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">المظهر المفضل</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <i class="fas fa-palette text-gray-400"></i>
                                        </div>
                                        <select id="theme" name="theme"
                                                class="w-full pl-4 pr-10 py-3 rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 appearance-none">
                                            <option value="light" {{ ($user->theme ?? '') == 'light' ? 'selected' : '' }}>فاتح</option>
                                            <option value="dark" {{ ($user->theme ?? '') == 'dark' ? 'selected' : '' }}>داكن</option>
                                            <option value="system" {{ ($user->theme ?? 'system') == 'system' ? 'selected' : '' }}>حسب النظام</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-6">
                                <label class="flex items-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300">
                                    <input type="checkbox" name="notifications_enabled" value="1"
                                           class="w-5 h-5 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 dark:focus:ring-purple-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 ml-3"
                                           {{ ($user->notifications_enabled ?? 1) ? 'checked' : '' }}>
                                    <div class="flex items-center">
                                        <i class="fas fa-bell text-purple-600 dark:text-purple-400 ml-3"></i>
                                        <div>
                                            <span class="text-gray-800 dark:text-white font-semibold">تفعيل الإشعارات</span>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">استقبال إشعارات النظام والتحديثات المهمة</p>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <!-- Password Change -->
                        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 mb-8">
                            <div class="flex items-center mb-6">
                                <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-500 rounded-xl flex items-center justify-center ml-4">
                                    <i class="fas fa-lock text-white text-xl"></i>
                                </div>
                                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">تغيير كلمة المرور</h3>
                            </div>

                            <div class="space-y-6">
                                <div class="space-y-2">
                                    <label for="current_password" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">كلمة المرور الحالية</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <i class="fas fa-key text-gray-400"></i>
                                        </div>
                                        <input type="password" id="current_password" name="current_password"
                                               class="w-full pl-4 pr-10 py-3 rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-300"
                                               placeholder="أدخل كلمة المرور الحالية">
                                    </div>
                                </div>

                                <div class="space-y-2">
                                    <label for="password" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">كلمة المرور الجديدة</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <i class="fas fa-lock text-gray-400"></i>
                                        </div>
                                        <input type="password" id="password" name="password"
                                               class="w-full pl-4 pr-10 py-3 rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-300"
                                               placeholder="أدخل كلمة المرور الجديدة">
                                    </div>
                                </div>

                                <div class="space-y-2">
                                    <label for="password_confirmation" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">تأكيد كلمة المرور الجديدة</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <i class="fas fa-check-circle text-gray-400"></i>
                                        </div>
                                        <input type="password" id="password_confirmation" name="password_confirmation"
                                               class="w-full pl-4 pr-10 py-3 rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-300"
                                               placeholder="أعد إدخال كلمة المرور الجديدة">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8">
                            <div class="flex flex-col sm:flex-row gap-4 justify-between">
                                <button type="button" id="deleteAccountBtn"
                                        class="flex items-center justify-center px-6 py-3 bg-gradient-to-r from-red-500 to-pink-600 text-white font-bold rounded-xl hover:from-red-600 hover:to-pink-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                                    <i class="fas fa-trash-alt ml-2"></i>
                                    حذف الحساب
                                </button>

                                <button id="submit-form" type="submit"
                                        class="flex items-center justify-center px-8 py-3 bg-gradient-to-r from-orange-500 to-red-600 text-white font-bold rounded-xl hover:from-orange-600 hover:to-red-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                                    <i class="fas fa-save ml-2"></i>
                                    حفظ التغييرات
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- نموذج حذف الحساب -->
                    <form id="delete-account-form" action="{{ route('admin.profile.delete') }}" method="POST" class="hidden">
                        @csrf
                        @method('DELETE')
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom Delete Account Confirmation Modal -->
<div id="deleteAccountModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-lg w-full mx-4 transform transition-all duration-300 scale-95" id="accountModalContent">
        <div class="p-8">
            <!-- Header -->
            <div class="flex items-center justify-center mb-6">
                <div class="w-20 h-20 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
                    <i class="fas fa-user-times text-red-600 dark:text-red-400 text-3xl"></i>
                </div>
            </div>

            <!-- Content -->
            <div class="text-center mb-8">
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">تأكيد حذف الحساب</h3>
                <div class="space-y-4">
                    <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
                        هل أنت متأكد من رغبتك في حذف حسابك نهائياً؟
                    </p>
                    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                        <div class="flex items-start">
                            <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400 mt-1 ml-3"></i>
                            <div class="text-left">
                                <p class="text-red-800 dark:text-red-300 font-semibold text-sm mb-2">تحذير مهم:</p>
                                <ul class="text-red-700 dark:text-red-400 text-sm space-y-1">
                                    <li>• سيتم حذف جميع بياناتك نهائياً</li>
                                    <li>• لن تتمكن من استرداد حسابك</li>
                                    <li>• سيتم تسجيل خروجك فوراً</li>
                                    <li>• هذا الإجراء لا يمكن التراجع عنه</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Buttons -->
            <div class="flex flex-col sm:flex-row gap-4">
                <button type="button" id="cancelAccountDelete"
                        class="flex-1 px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 font-semibold rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-300 flex items-center justify-center">
                    <i class="fas fa-arrow-left ml-2"></i>
                    إلغاء والعودة
                </button>
                <button type="button" id="confirmAccountDelete"
                        class="flex-1 px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white font-semibold rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:scale-105">
                    <i class="fas fa-trash-alt ml-2"></i>
                    حذف الحساب نهائياً
                </button>
            </div>
        </div>
    </div>
</div>

@section('styles')
<style>
    /* Custom styles for better appearance */
    .appearance-none::-ms-expand {
        display: none;
    }

    /* Gradient text effect */
    .gradient-text {
        background: linear-gradient(135deg, #f97316, #dc2626);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Hover effects */
    .hover-lift:hover {
        transform: translateY(-2px);
    }

    /* Custom scrollbar */
    .custom-scrollbar::-webkit-scrollbar {
        width: 6px;
    }

    .custom-scrollbar::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #f97316, #dc2626);
        border-radius: 10px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #ea580c, #b91c1c);
    }
</style>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Delete Account Modal Elements
        const deleteAccountModal = document.getElementById('deleteAccountModal');
        const accountModalContent = document.getElementById('accountModalContent');
        const deleteAccountBtn = document.getElementById('deleteAccountBtn');
        const cancelAccountDeleteBtn = document.getElementById('cancelAccountDelete');
        const confirmAccountDeleteBtn = document.getElementById('confirmAccountDelete');

        // Show delete account modal
        deleteAccountBtn.addEventListener('click', function() {
            deleteAccountModal.classList.remove('hidden');
            deleteAccountModal.classList.add('flex');
            setTimeout(() => {
                accountModalContent.classList.remove('scale-95');
                accountModalContent.classList.add('scale-100');
            }, 10);
        });

        // Cancel account delete
        cancelAccountDeleteBtn.addEventListener('click', closeAccountModal);

        // Close modal when clicking outside
        deleteAccountModal.addEventListener('click', function(e) {
            if (e.target === deleteAccountModal) {
                closeAccountModal();
            }
        });

        // Confirm account delete
        confirmAccountDeleteBtn.addEventListener('click', function() {
            // Add loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري الحذف...';
            this.disabled = true;

            // Submit form after short delay for better UX
            setTimeout(() => {
                document.getElementById('delete-account-form').submit();
            }, 1000);
        });

        // Close account modal function
        function closeAccountModal() {
            accountModalContent.classList.remove('scale-100');
            accountModalContent.classList.add('scale-95');
            setTimeout(() => {
                deleteAccountModal.classList.add('hidden');
                deleteAccountModal.classList.remove('flex');
                // Reset confirm button
                confirmAccountDeleteBtn.innerHTML = '<i class="fas fa-trash-alt ml-2"></i>حذف الحساب نهائياً';
                confirmAccountDeleteBtn.disabled = false;
            }, 200);
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !deleteAccountModal.classList.contains('hidden')) {
                closeAccountModal();
            }
        });
    });

    // Auto-submit form when profile image is selected
    document.getElementById('profile_image').addEventListener('change', function() {
        if (this.files && this.files[0]) {
            // Show loading indicator
            const submitBtn = document.getElementById('submit-form');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري الحفظ...';
            submitBtn.disabled = true;

            // Submit form
            setTimeout(() => {
                document.querySelector('form').submit();
            }, 500);
        }
    });

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const password = document.getElementById('password').value;
        const passwordConfirmation = document.getElementById('password_confirmation').value;

        if (password && password !== passwordConfirmation) {
            e.preventDefault();
            alert('كلمة المرور الجديدة وتأكيدها غير متطابقتين');
            return false;
        }

        // Show loading state
        const submitBtn = document.getElementById('submit-form');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري الحفظ...';
        submitBtn.disabled = true;
    });

    // Password strength indicator
    document.getElementById('password').addEventListener('input', function() {
        const password = this.value;
        const strengthIndicator = document.getElementById('password-strength');

        if (password.length === 0) {
            if (strengthIndicator) strengthIndicator.remove();
            return;
        }

        let strength = 0;
        let strengthText = '';
        let strengthColor = '';

        if (password.length >= 8) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;

        switch (strength) {
            case 0:
            case 1:
                strengthText = 'ضعيفة جداً';
                strengthColor = 'text-red-500';
                break;
            case 2:
                strengthText = 'ضعيفة';
                strengthColor = 'text-orange-500';
                break;
            case 3:
                strengthText = 'متوسطة';
                strengthColor = 'text-yellow-500';
                break;
            case 4:
                strengthText = 'قوية';
                strengthColor = 'text-green-500';
                break;
            case 5:
                strengthText = 'قوية جداً';
                strengthColor = 'text-green-600';
                break;
        }

        let indicator = document.getElementById('password-strength');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'password-strength';
            indicator.className = 'text-sm mt-1';
            this.parentNode.appendChild(indicator);
        }

        indicator.innerHTML = `<span class="${strengthColor}">قوة كلمة المرور: ${strengthText}</span>`;
    });
</script>
@endsection
@endsection
