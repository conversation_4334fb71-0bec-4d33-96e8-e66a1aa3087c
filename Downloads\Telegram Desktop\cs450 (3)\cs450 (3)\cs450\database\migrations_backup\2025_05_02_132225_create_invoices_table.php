<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
return new class extends Migration
{
    public function up()
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id('invoice_id');
            $table->foreignId('order_id')->constrained('orders')->onDelete('cascade');
            $table->decimal('total_amount', 10, 2);
            $table->decimal('tax_amount', 10, 2);
            $table->decimal('discount_amount', 10, 2)->default(0.00);
            $table->enum('payment_status', ['paid', 'unpaid', 'partial'])->default('unpaid');
            $table->date('due_date');
            $table->timestamps();
            
        
        });  
            DB::statement('ALTER TABLE invoices ADD CONSTRAINT invoices_total_amount_positive CHECK (total_amount > 0)');
            DB::statement('ALTER TABLE invoices ADD CONSTRAINT invoices_tax_non_negative CHECK (tax_amount >= 0)');
            DB::statement('ALTER TABLE invoices ADD CONSTRAINT invoices_discount_non_negative CHECK (discount_amount >= 0)');
    }

    public function down()
    {
        Schema::dropIfExists('invoices');
    }
};