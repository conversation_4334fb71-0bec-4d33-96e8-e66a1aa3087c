<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title', 'Eat Hub - تجربة طعام لا تنسى'); ?></title>

    <!-- النظام الموحد للوضع المظلم -->
    <script src="<?php echo e(asset('js/unified-dark-mode.js')); ?>"></script>

    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#FF6B35',      // برتقالي محمر
                        secondary: '#4CAF50',    // أخضر للدلالة على الطازج
                        accent: '#FFEB3B',       // أصفر للتنبيهات والعروض
                        warmBrown: '#C8A080',    // بني دافئ للراحة
                        darkText: '#333333',     // للنصوص الداكنة
                    },
                    fontFamily: {
                        sans: ['Cairo', 'sans-serif'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.7s ease-in-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'bounce-light': 'bounceLight 3s infinite',
                        'float': 'float 3s ease-in-out infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(50px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        bounceLight: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-10px)' }
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' }
                        }
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <?php echo $__env->make('customer.partials.styles', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>

<body class="bg-gray-50 dark:bg-gray-900">
    <?php echo $__env->make('customer.partials.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- المحتوى الرئيسي -->
    <main class="min-h-screen">
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <!-- أزرار عائمة للأدمن والموظف -->
    <?php if(auth()->guard()->check()): ?>
    <?php if(auth()->user()->user_type == 'admin' || auth()->user()->user_type == 'employee'): ?>
    <div class="fixed bottom-6 left-6 z-50 flex flex-col space-y-3">
        <?php if(auth()->user()->user_type == 'admin' || auth()->user()->can('dashboard.admin')): ?>
        <a href="<?php echo e(route('admin.dashboard')); ?>"
           class="w-14 h-14 bg-gradient-to-r from-orange-500 to-red-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group hover:scale-110"
           title="العودة للوحة الإدارة">
            <i class="fas fa-user-shield text-lg group-hover:scale-110 transition-transform"></i>
        </a>
        <?php endif; ?>
        <a href="<?php echo e(route('employee.dashboard')); ?>"
           class="w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group hover:scale-110"
           title="العودة للوحة الموظف">
            <i class="fas fa-user-tie text-lg group-hover:scale-110 transition-transform"></i>
        </a>
    </div>
    <?php endif; ?>
    <?php endif; ?>

    <?php echo $__env->make('customer.partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Scripts -->
    <script>
        // Dark Mode Toggle
        document.addEventListener('DOMContentLoaded', function() {
            const darkModeToggle = document.getElementById('darkModeToggle');
            const darkModeIcon = document.getElementById('darkModeIcon');

            if (darkModeToggle && darkModeIcon) {
                // تحديث الأيقونة عند التحميل
                const savedDarkMode = localStorage.getItem('darkMode');
                if (savedDarkMode === 'true') {
                    document.documentElement.classList.add('dark');
                    darkModeIcon.classList.remove('fa-moon');
                    darkModeIcon.classList.add('fa-sun');
                } else if (savedDarkMode === 'false') {
                    document.documentElement.classList.remove('dark');
                    darkModeIcon.classList.remove('fa-sun');
                    darkModeIcon.classList.add('fa-moon');
                }

                // إضافة مستمع الحدث
                darkModeToggle.addEventListener('click', function() {
                    if (document.documentElement.classList.contains('dark')) {
                        document.documentElement.classList.remove('dark');
                        darkModeIcon.classList.remove('fa-sun');
                        darkModeIcon.classList.add('fa-moon');
                        localStorage.setItem('darkMode', 'false');
                    } else {
                        document.documentElement.classList.add('dark');
                        darkModeIcon.classList.remove('fa-moon');
                        darkModeIcon.classList.add('fa-sun');
                        localStorage.setItem('darkMode', 'true');
                    }
                });
            }

            // Mobile Menu Toggle
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const mobileMenu = document.getElementById('mobileMenu');

            if (mobileMenuToggle && mobileMenu) {
                mobileMenuToggle.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }
        });
    </script>

    <?php echo $__env->make('customer.partials.scripts', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Downloads\Telegram Desktop\cs450 (3)\cs450 (3)\cs450\resources\views/customer/layouts/simple.blade.php ENDPATH**/ ?>