<?php $__env->startSection('content'); ?>

<style>
.attendance-modal {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.attendance-modal-backdrop {
    backdrop-filter: blur(8px);
}

.pulse-ring {
    animation: pulse-ring 2s infinite;
}

@keyframes pulse-ring {
    0% {
        transform: scale(0.8);
        opacity: 1;
    }
    100% {
        transform: scale(1.2);
        opacity: 0;
    }
}
</style>
<!-- Hero Section -->
<div class="relative mb-8 overflow-hidden">
    <div class="bg-white dark:bg-gray-800 rounded-3xl p-8 border border-gray-200 dark:border-gray-700 shadow-lg">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-4xl font-black text-gray-800 dark:text-white mb-3">
                    مرحباً، <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"><?php echo e(Auth::user()->first_name ?? 'الموظف'); ?></span>!
                    <span class="inline-block animate-bounce">👋</span>
                </h1>
                <p class="text-gray-600 dark:text-gray-400 text-lg font-medium">
                    إليك نظرة عامة على أنشطة المطعم اليوم
                </p>
            </div>
            <div class="hidden md:flex items-center space-x-4 space-x-reverse">
                <?php if(Auth::user()->user_type == 'admin' || Auth::user()->can('dashboard.admin')): ?>
                <a href="<?php echo e(route('admin.dashboard')); ?>"
                   class="flex items-center px-4 py-2 bg-orange-50 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 rounded-lg hover:bg-orange-100 dark:hover:bg-orange-900/30 transition-colors">
                    <i class="fas fa-user-shield ml-2"></i>
                    <span class="text-sm font-medium">لوحة الإدارة</span>
                </a>
                <?php endif; ?>
                <a href="<?php echo e(route('customer.index')); ?>"
                   class="flex items-center px-4 py-2 bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors">
                    <i class="fas fa-home ml-2"></i>
                    <span class="text-sm font-medium">الصفحة الرئيسية</span>
                </a>
                <div class="w-24 h-24 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-3xl font-bold">
                    <?php echo e(substr(Auth::user()->first_name ?? 'M', 0, 1)); ?>

                </div>
            </div>
        </div>
    </div>
</div>

<div id="dashboard-page" class="page">
    <div class="mb-8">
        <h2 class="text-3xl font-bold text-gray-800 dark:text-white mb-6">نظرة عامة</h2>

        <!-- بطاقات الإحصائيات -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-12">
            <!-- بطاقة إحصائية - الطلبات اليوم -->
            <div class="relative bg-gradient-to-br from-cyan-50 via-blue-50 to-indigo-100 dark:from-cyan-900/30 dark:via-blue-900/30 dark:to-indigo-900/40 rounded-3xl shadow-2xl hover:shadow-3xl p-8 card-hover overflow-hidden border-2 border-cyan-200/60 dark:border-cyan-700/60 group transform hover:-translate-y-2 transition-all duration-500">
                <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-cyan-400 via-blue-500 to-indigo-600 rounded-t-3xl"></div>
                <div class="absolute -top-16 -right-16 w-32 h-32 bg-gradient-to-br from-cyan-400/20 to-blue-500/20 rounded-full blur-xl"></div>
                <div class="absolute -bottom-8 -left-8 w-24 h-24 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-full blur-lg"></div>
                <div class="absolute top-4 right-4 w-3 h-3 bg-cyan-400 rounded-full animate-ping"></div>

                <div class="flex justify-between items-start relative z-10">
                    <div class="flex-1">
                        <p class="text-gray-800 dark:text-white text-lg font-bold mb-3 tracking-wide">الطلبات اليوم</p>
                        <h3 class="text-5xl font-black text-gray-900 dark:text-white mb-4 leading-tight"><?php echo e($todayStats['ordersCount'] ?? 0); ?></h3>
                        <div class="flex items-center space-x-3 space-x-reverse bg-white/80 dark:bg-gray-800/80 rounded-2xl p-3 backdrop-blur-sm border border-gray-200 dark:border-gray-600">
                            <div class="w-3 h-3 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse shadow-lg"></div>
                            <p class="text-gray-800 dark:text-white text-base font-bold">
                                <?php echo e($todayStats['totalSales'] ?? 0); ?> د.ل
                            </p>
                        </div>
                    </div>
                    <div class="rounded-3xl bg-gradient-to-br from-cyan-500 via-blue-600 to-indigo-700 p-6 shadow-2xl group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 border-4 border-white/30">
                        <i class="fas fa-shopping-cart text-white text-3xl drop-shadow-lg"></i>
                    </div>
                </div>
            </div>

            <!-- بطاقة إحصائية - الحجوزات اليوم -->
            <div class="relative bg-gradient-to-br from-emerald-50 via-teal-50 to-green-100 dark:from-emerald-900/30 dark:via-teal-900/30 dark:to-green-900/40 rounded-3xl shadow-2xl hover:shadow-3xl p-8 card-hover overflow-hidden border-2 border-emerald-200/60 dark:border-emerald-700/60 group transform hover:-translate-y-2 transition-all duration-500">
                <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-emerald-400 via-teal-500 to-green-600 rounded-t-3xl"></div>
                <div class="absolute -top-16 -right-16 w-32 h-32 bg-gradient-to-br from-emerald-400/20 to-teal-500/20 rounded-full blur-xl"></div>
                <div class="absolute -bottom-8 -left-8 w-24 h-24 bg-gradient-to-br from-green-500/20 to-lime-500/20 rounded-full blur-lg"></div>
                <div class="absolute top-4 right-4 w-3 h-3 bg-emerald-400 rounded-full animate-ping"></div>

                <div class="flex justify-between items-start relative z-10">
                    <div class="flex-1">
                        <p class="text-gray-800 dark:text-white text-lg font-bold mb-3 tracking-wide">الحجوزات اليوم</p>
                        <h3 class="text-5xl font-black text-gray-900 dark:text-white mb-4 leading-tight"><?php echo e($todayStats['reservationsCount'] ?? 0); ?></h3>
                        <div class="flex items-center space-x-3 space-x-reverse bg-white/80 dark:bg-gray-800/80 rounded-2xl p-3 backdrop-blur-sm border border-gray-200 dark:border-gray-600">
                            <div class="w-3 h-3 bg-gradient-to-r from-emerald-400 to-teal-500 rounded-full animate-pulse shadow-lg"></div>
                            <p class="text-gray-800 dark:text-white text-base font-bold">
                                نشطة
                            </p>
                        </div>
                    </div>
                    <div class="rounded-3xl bg-gradient-to-br from-emerald-500 via-teal-600 to-green-700 p-6 shadow-2xl group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 border-4 border-white/30">
                        <i class="fas fa-calendar-check text-white text-3xl drop-shadow-lg"></i>
                    </div>
                </div>
            </div>

            <!-- بطاقة إحصائية - الطلبات المعلقة -->
            <div class="relative bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-100 dark:from-orange-900/30 dark:via-amber-900/30 dark:to-yellow-900/40 rounded-3xl shadow-2xl hover:shadow-3xl p-8 card-hover overflow-hidden border-2 border-orange-200/60 dark:border-orange-700/60 group transform hover:-translate-y-2 transition-all duration-500">
                <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-orange-400 via-amber-500 to-yellow-600 rounded-t-3xl"></div>
                <div class="absolute -top-16 -right-16 w-32 h-32 bg-gradient-to-br from-orange-400/20 to-amber-500/20 rounded-full blur-xl"></div>
                <div class="absolute -bottom-8 -left-8 w-24 h-24 bg-gradient-to-br from-yellow-500/20 to-orange-500/20 rounded-full blur-lg"></div>
                <div class="absolute top-4 right-4 w-3 h-3 bg-orange-400 rounded-full animate-ping"></div>

                <div class="flex justify-between items-start relative z-10">
                    <div class="flex-1">
                        <p class="text-gray-800 dark:text-white text-lg font-bold mb-3 tracking-wide">الطلبات المعلقة</p>
                        <h3 class="text-5xl font-black text-gray-900 dark:text-white mb-4 leading-tight"><?php echo e($todayStats['pendingOrdersCount'] ?? 0); ?></h3>
                        <div class="flex items-center space-x-3 space-x-reverse bg-white/80 dark:bg-gray-800/80 rounded-2xl p-3 backdrop-blur-sm border border-gray-200 dark:border-gray-600">
                            <div class="w-3 h-3 bg-gradient-to-r from-orange-400 to-amber-500 rounded-full animate-pulse shadow-lg"></div>
                            <p class="text-gray-800 dark:text-white text-base font-bold">
                                في الانتظار
                            </p>
                        </div>
                    </div>
                    <div class="rounded-3xl bg-gradient-to-br from-orange-500 via-amber-600 to-yellow-700 p-6 shadow-2xl group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 border-4 border-white/30">
                        <i class="fas fa-clock text-white text-3xl drop-shadow-lg"></i>
                    </div>
                </div>
            </div>

            <!-- بطاقة إحصائية - الطاولات المتاحة -->
            <div class="relative bg-gradient-to-br from-purple-50 via-violet-50 to-indigo-100 dark:from-purple-900/30 dark:via-violet-900/30 dark:to-indigo-900/40 rounded-3xl shadow-2xl hover:shadow-3xl p-8 card-hover overflow-hidden border-2 border-purple-200/60 dark:border-purple-700/60 group transform hover:-translate-y-2 transition-all duration-500">
                <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-purple-400 via-violet-500 to-indigo-600 rounded-t-3xl"></div>
                <div class="absolute -top-16 -right-16 w-32 h-32 bg-gradient-to-br from-purple-400/20 to-violet-500/20 rounded-full blur-xl"></div>
                <div class="absolute -bottom-8 -left-8 w-24 h-24 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-full blur-lg"></div>
                <div class="absolute top-4 right-4 w-3 h-3 bg-purple-400 rounded-full animate-ping"></div>

                <div class="flex justify-between items-start relative z-10">
                    <div class="flex-1">
                        <p class="text-gray-800 dark:text-white text-lg font-bold mb-3 tracking-wide">الطاولات المتاحة</p>
                        <h3 class="text-5xl font-black text-gray-900 dark:text-white mb-4 leading-tight"><?php echo e($tableStats['available'] ?? 0); ?></h3>
                        <div class="flex items-center space-x-3 space-x-reverse bg-white/80 dark:bg-gray-800/80 rounded-2xl p-3 backdrop-blur-sm border border-gray-200 dark:border-gray-600">
                            <div class="w-3 h-3 bg-gradient-to-r from-purple-400 to-violet-500 rounded-full animate-pulse shadow-lg"></div>
                            <p class="text-gray-800 dark:text-white text-base font-bold">
                                من <?php echo e($tableStats['total'] ?? 0); ?> طاولة
                            </p>
                        </div>
                    </div>
                    <div class="rounded-3xl bg-gradient-to-br from-purple-500 via-violet-600 to-indigo-700 p-6 shadow-2xl group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 border-4 border-white/30">
                        <i class="fas fa-chair text-white text-3xl drop-shadow-lg"></i>
                    </div>
                </div>
            </div>

            <!-- بطاقة إحصائية - العروض النشطة -->
            <?php if(isset($offerStats)): ?>
            <div class="relative bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-100 dark:from-orange-900/30 dark:via-amber-900/30 dark:to-yellow-900/40 rounded-3xl shadow-2xl hover:shadow-3xl p-8 card-hover overflow-hidden border-2 border-orange-200/60 dark:border-orange-700/60 group transform hover:-translate-y-2 transition-all duration-500">
                <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-orange-400 via-amber-500 to-yellow-600 rounded-t-3xl"></div>
                <div class="absolute -top-16 -right-16 w-32 h-32 bg-gradient-to-br from-orange-400/20 to-amber-500/20 rounded-full blur-xl"></div>
                <div class="absolute -bottom-8 -left-8 w-24 h-24 bg-gradient-to-br from-yellow-500/20 to-orange-500/20 rounded-full blur-lg"></div>
                <div class="absolute top-4 right-4 w-3 h-3 bg-orange-400 rounded-full animate-ping"></div>

                <div class="flex justify-between items-start relative z-10">
                    <div class="flex-1">
                        <p class="text-gray-800 dark:text-white text-lg font-bold mb-3 tracking-wide">العروض النشطة</p>
                        <h3 class="text-5xl font-black text-gray-900 dark:text-white mb-4 leading-tight"><?php echo e($offerStats['active'] ?? 0); ?></h3>
                        <div class="flex items-center space-x-3 space-x-reverse bg-white/80 dark:bg-gray-800/80 rounded-2xl p-3 backdrop-blur-sm border border-gray-200 dark:border-gray-600">
                            <div class="w-3 h-3 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse shadow-lg"></div>
                            <p class="text-gray-800 dark:text-white text-base font-bold">
                                <?php echo e($offerStats['total'] ?? 0); ?> إجمالي
                            </p>
                        </div>
                    </div>
                    <div class="rounded-3xl bg-gradient-to-br from-orange-500 via-amber-600 to-yellow-700 p-6 shadow-2xl group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 border-4 border-white/30">
                        <i class="fas fa-tags text-white text-3xl drop-shadow-lg"></i>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        <!-- الطلبات النشطة -->
        <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-3xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h3 class="text-2xl font-bold text-gray-800 dark:text-white mb-2 flex items-center">
                        <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center mr-3">
                            <i class="fas fa-shopping-cart text-white text-sm"></i>
                        </div>
                        الطلبات النشطة
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400">طلبات تحتاج إلى معالجة</p>
                </div>
                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="relative">
                        <select id="orderStatusFilter" class="appearance-none bg-gray-100 border border-gray-300 text-gray-800 py-2 px-4 pr-10 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300">
                            <option value="all">جميع الحالات</option>
                            <option value="pending">قيد الانتظار</option>
                            <option value="preparing">قيد التحضير</option>
                            <option value="completed">مكتمل</option>
                            <option value="canceled">ملغي</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center px-3 text-gray-600">
                            <i class="fas fa-chevron-down text-xs"></i>
                        </div>
                    </div>
                    <a href="<?php echo e(route('employee.orders')); ?>" class="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl text-sm font-semibold hover:scale-105 transition-transform duration-300">عرض الكل</a>
                </div>
            </div>

            <!-- شريط البحث السريع -->
            <div class="mb-6">
                <div class="relative">
                    <input type="text" id="quickOrderSearch" placeholder="بحث سريع في الطلبات..." class="w-full px-6 py-4 pr-14 rounded-2xl bg-gray-100 border border-gray-300 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 text-sm">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                        <i class="fas fa-search text-gray-500"></i>
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                <table class="min-w-full text-sm">
                    <thead>
                        <tr class="bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-200 border-b border-gray-200 dark:border-gray-600">
                            <th class="py-4 px-6 text-right font-semibold">#</th>
                            <th class="py-4 px-6 text-right font-semibold">العميل</th>
                            <th class="py-4 px-6 text-right font-semibold">المنتجات</th>
                            <th class="py-4 px-6 text-right font-semibold">المبلغ</th>
                            <th class="py-4 px-6 text-right font-semibold">الحالة</th>
                            <th class="py-4 px-6 text-right font-semibold">الوقت</th>
                            <th class="py-4 px-6 text-right font-semibold">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-white/10" id="activeOrdersTable">
                        <?php $__empty_1 = true; $__currentLoopData = $pendingOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-white/5 order-row transition-all duration-300 modern-card" data-status="<?php echo e($order->status); ?>">
                            <td class="py-4 px-6">
                                <span class="font-bold text-white">#<?php echo e($order->order_id); ?></span>
                            </td>
                            <td class="py-4 px-6">
                                <div class="flex items-center">
                                    <?php if($order->user && !empty($order->user->first_name) && !empty($order->user->last_name)): ?>
                                        <div class="w-10 h-10 rounded-full bg-gradient-to-br from-primary to-secondary flex items-center justify-center text-white text-sm font-bold mr-3">
                                            <?php echo e(substr($order->user->first_name, 0, 1)); ?>

                                        </div>
                                        <span class="text-white/90 font-medium"><?php echo e($order->user->first_name); ?> <?php echo e($order->user->last_name); ?></span>
                                    <?php elseif($order->user && !empty($order->user->name)): ?>
                                        <div class="w-10 h-10 rounded-full bg-gradient-to-br from-primary to-secondary flex items-center justify-center text-white text-sm font-bold mr-3">
                                            <?php echo e(substr($order->user->name, 0, 1)); ?>

                                        </div>
                                        <span class="text-white/90 font-medium"><?php echo e($order->user->name); ?></span>
                                    <?php else: ?>
                                        <div class="w-10 h-10 rounded-full bg-gray-500 flex items-center justify-center text-white text-sm font-bold mr-3">
                                            ?
                                        </div>
                                        <span class="text-white/90 font-medium">غير محدد</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="py-4 px-6">
                                <?php if($order->items->count() > 0): ?>
                                    <div class="flex flex-wrap gap-2">
                                    <?php $__currentLoopData = $order->items->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="inline-block px-3 py-1 bg-gray-100 border border-gray-300 rounded-xl text-xs text-gray-800">
                                            <?php echo e($item->menuItem->name ?? 'غير معروف'); ?>

                                            <?php if($item->quantity > 1): ?>
                                                <span class="text-blue-600 font-bold">×<?php echo e($item->quantity); ?></span>
                                            <?php endif; ?>
                                        </span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($order->items->count() > 2): ?>
                                        <span class="inline-block px-3 py-1 bg-gray-100 border border-gray-300 rounded-xl text-xs text-gray-600">+<?php echo e($order->items->count() - 2); ?></span>
                                    <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <span class="text-gray-500">لا توجد عناصر</span>
                                <?php endif; ?>
                            </td>
                            <td class="py-4 px-6 font-bold text-gray-800"><?php echo e($order->total_amount); ?> د.ل</td>
                            <td class="py-4 px-6">
                                <?php if($order->status == 'pending'): ?>
                                    <span class="px-3 py-2 bg-yellow-100 text-yellow-800 border border-yellow-300 rounded-xl text-xs font-bold flex items-center w-fit">
                                        <span class="w-2 h-2 bg-yellow-500 rounded-full inline-block mr-2"></span>
                                        قيد الانتظار
                                    </span>
                                <?php elseif($order->status == 'preparing'): ?>
                                    <span class="px-3 py-2 bg-blue-100 text-blue-800 border border-blue-300 rounded-xl text-xs font-bold flex items-center w-fit">
                                        <span class="w-2 h-2 bg-blue-500 rounded-full inline-block mr-2"></span>
                                        قيد التحضير
                                    </span>
                                <?php elseif($order->status == 'completed'): ?>
                                    <span class="px-3 py-2 bg-green-100 text-green-800 border border-green-300 rounded-xl text-xs font-bold flex items-center w-fit">
                                        <span class="w-2 h-2 bg-green-500 rounded-full inline-block mr-2"></span>
                                        مكتمل
                                    </span>
                                <?php elseif($order->status == 'canceled'): ?>
                                    <span class="px-3 py-2 bg-red-100 text-red-800 border border-red-300 rounded-xl text-xs font-bold flex items-center w-fit">
                                        <span class="w-2 h-2 bg-red-500 rounded-full inline-block mr-2"></span>
                                        ملغي
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="py-4 px-6">
                                <div class="flex flex-col">
                                    <span class="text-white font-semibold"><?php echo e($order->created_at->format('H:i')); ?></span>
                                    <span class="text-xs text-white/60"><?php echo e($order->created_at->diffForHumans()); ?></span>
                                </div>
                            </td>
                            <td class="py-4 px-6">
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <?php if($order->status == 'pending'): ?>
                                        <a href="<?php echo e(route('employee.orders.update-status', ['id' => $order->order_id, 'status' => 'preparing'])); ?>" class="p-2 rounded-xl bg-gradient-to-r from-indigo-500/20 to-purple-500/20 hover:from-indigo-500/30 hover:to-purple-500/30 text-indigo-300 hover:text-indigo-200 transition-all duration-300 group border border-indigo-500/30" title="بدء التحضير">
                                            <i class="fas fa-utensils group-hover:scale-110 transition-transform"></i>
                                        </a>
                                    <?php elseif($order->status == 'preparing'): ?>
                                        <a href="<?php echo e(route('employee.orders.update-status', ['id' => $order->order_id, 'status' => 'completed'])); ?>" class="p-2 rounded-xl bg-gradient-to-r from-green-500/20 to-emerald-500/20 hover:from-green-500/30 hover:to-emerald-500/30 text-green-300 hover:text-green-200 transition-all duration-300 group border border-green-500/30" title="إكمال الطلب">
                                            <i class="fas fa-check-circle group-hover:scale-110 transition-transform"></i>
                                        </a>
                                    <?php endif; ?>
                                    <a href="<?php echo e(route('employee.orders.show', $order->order_id)); ?>" class="p-2 rounded-xl bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 transition-all duration-300 group" title="عرض التفاصيل">
                                        <i class="fas fa-eye group-hover:scale-110 transition-transform"></i>
                                    </a>
                                    <button type="button" class="p-2 rounded-xl bg-gradient-to-r from-gray-500/20 to-slate-500/20 hover:from-gray-500/30 hover:to-slate-500/30 text-gray-300 hover:text-gray-200 transition-all duration-300 group border border-gray-500/30 print-receipt" data-order-id="<?php echo e($order->order_id); ?>" title="طباعة الإيصال">
                                        <i class="fas fa-print group-hover:scale-110 transition-transform"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="py-12 text-center">
                                <div class="flex flex-col items-center justify-center">
                                    <div class="w-20 h-20 rounded-full bg-gray-100 dark:bg-gray-600 flex items-center justify-center mb-4">
                                        <i class="fas fa-clipboard-list text-3xl text-gray-400 dark:text-gray-300"></i>
                                    </div>
                                    <p class="text-xl font-bold text-gray-800 dark:text-white mb-2">لا توجد طلبات نشطة حالياً</p>
                                    <a href="<?php echo e(route('employee.orders.create')); ?>" class="mt-3 px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-semibold hover:scale-105 transition-transform duration-300">إنشاء طلب جديد</a>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- سكريبت البحث السريع وتصفية الطلبات -->
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const quickSearch = document.getElementById('quickOrderSearch');
                    const statusFilter = document.getElementById('orderStatusFilter');
                    const orderRows = document.querySelectorAll('.order-row');

                    // البحث السريع
                    quickSearch.addEventListener('input', filterOrders);

                    // تصفية حسب الحالة
                    statusFilter.addEventListener('change', filterOrders);

                    function filterOrders() {
                        const searchTerm = quickSearch.value.toLowerCase();
                        const statusValue = statusFilter.value;

                        orderRows.forEach(row => {
                            const orderStatus = row.getAttribute('data-status');
                            const orderText = row.textContent.toLowerCase();

                            const matchesSearch = searchTerm === '' || orderText.includes(searchTerm);
                            const matchesStatus = statusValue === 'all' || orderStatus === statusValue;

                            row.style.display = (matchesSearch && matchesStatus) ? '' : 'none';
                        });

                        // عرض رسالة إذا لم يتم العثور على نتائج
                        const visibleRows = document.querySelectorAll('.order-row[style=""]').length;
                        const noResultsRow = document.getElementById('noResultsRow');

                        if (visibleRows === 0) {
                            if (!noResultsRow) {
                                const tbody = document.getElementById('activeOrdersTable');
                                const newRow = document.createElement('tr');
                                newRow.id = 'noResultsRow';
                                newRow.innerHTML = `
                                    <td colspan="7" class="py-6 text-center text-gray-500 dark:text-gray-400">
                                        <div class="flex flex-col items-center justify-center">
                                            <i class="fas fa-search text-4xl mb-3 text-gray-300 dark:text-gray-600"></i>
                                            <p>لم يتم العثور على نتائج</p>
                                        </div>
                                    </td>
                                `;
                                tbody.appendChild(newRow);
                            }
                        } else if (noResultsRow) {
                            noResultsRow.remove();
                        }
                    }

                    // طباعة الإيصال
                    const printButtons = document.querySelectorAll('.print-receipt');
                    printButtons.forEach(button => {
                        button.addEventListener('click', function() {
                            const orderId = this.getAttribute('data-order-id');
                            // هنا يمكن إضافة كود لطباعة الإيصال
                            alert('جاري طباعة إيصال للطلب رقم ' + orderId);
                        });
                    });
                });
            </script>
        </div>

        <!-- حجوزات اليوم -->
        <div class="bg-white dark:bg-gray-800 rounded-3xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h3 class="text-2xl font-bold text-gray-800 dark:text-white mb-2 flex items-center">
                        <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center mr-3">
                            <i class="fas fa-calendar-check text-white text-sm"></i>
                        </div>
                        حجوزات اليوم
                    </h3>
                    <p class="text-white/70"><?php echo e($todayReservations->count()); ?> حجز</p>
                </div>
                <div class="flex items-center space-x-3 space-x-reverse">
                    <a href="<?php echo e(route('employee.reservations.create')); ?>" class="p-3 rounded-xl bg-gradient-to-r from-green-500/20 to-emerald-500/20 hover:from-green-500/30 hover:to-emerald-500/30 text-green-300 hover:text-green-200 transition-all duration-300 group border border-green-500/30" title="إضافة حجز جديد">
                        <i class="fas fa-plus group-hover:scale-110 transition-transform"></i>
                    </a>
                    <a href="<?php echo e(route('employee.reservations')); ?>" class="px-4 py-2 bg-gradient-to-r from-primary to-secondary text-white rounded-xl text-sm font-semibold hover:scale-105 transition-transform duration-300 neon-glow">عرض الكل</a>
                </div>
            </div>

            <!-- الجدول الزمني للحجوزات -->
            <div class="mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-lg font-bold text-white">الجدول الزمني</h4>
                    <div class="flex text-sm space-x-4 space-x-reverse">
                        <span class="flex items-center">
                            <span class="w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full inline-block mr-2 animate-pulse"></span>
                            <span class="text-green-300">قادم</span>
                        </span>
                        <span class="flex items-center">
                            <span class="w-3 h-3 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full inline-block mr-2 animate-pulse"></span>
                            <span class="text-blue-300">حالي</span>
                        </span>
                        <span class="flex items-center">
                            <span class="w-3 h-3 bg-gradient-to-r from-gray-400 to-slate-400 rounded-full inline-block mr-2"></span>
                            <span class="text-gray-300">مضى</span>
                        </span>
                    </div>
                </div>
                <div class="relative h-12 bg-gray-100 rounded-2xl overflow-hidden border border-gray-300">
                    <div class="absolute inset-0 flex">
                        <?php
                            $now = \Carbon\Carbon::now();
                            $startOfDay = $now->copy()->startOfDay();
                            $endOfDay = $now->copy()->endOfDay();
                            $totalMinutes = $startOfDay->diffInMinutes($endOfDay);
                            $minutesSinceMidnight = $startOfDay->diffInMinutes($now);
                            $currentTimePosition = ($minutesSinceMidnight / $totalMinutes) * 100;
                        ?>

                        <!-- مؤشر الوقت الحالي -->
                        <div class="absolute h-full w-1 bg-gradient-to-b from-red-400 to-pink-400 z-10 rounded-full animate-pulse" style="left: <?php echo e($currentTimePosition); ?>%"></div>

                        <!-- الحجوزات -->
                        <?php $__currentLoopData = $todayReservations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reservation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $reservationTime = $reservation->reservation_time;
                                $minutesSinceMidnightForReservation = $startOfDay->diffInMinutes($reservationTime);
                                $position = ($minutesSinceMidnightForReservation / $totalMinutes) * 100;
                                $isPast = $reservationTime->isPast();
                                $isCurrent = $reservationTime->isPast() && $reservationTime->addHours(1)->isFuture();
                                $bgColor = $isPast ? ($isCurrent ? 'from-blue-400 to-cyan-400' : 'from-gray-400 to-slate-400') : 'from-green-400 to-emerald-400';
                            ?>
                            <div class="absolute h-8 w-8 bg-gradient-to-br <?php echo e($bgColor); ?> rounded-full top-2 -ml-4 flex items-center justify-center text-white text-xs font-bold cursor-pointer reservation-marker hover:scale-110 transition-transform duration-300 shadow-lg border-2 border-white/30" style="left: <?php echo e($position); ?>%" data-reservation-id="<?php echo e($reservation->reservation_id); ?>" title="<?php echo e(($reservation->user && !empty($reservation->user->first_name) && !empty($reservation->user->last_name)) ? $reservation->user->first_name . ' ' . $reservation->user->last_name : (($reservation->user && !empty($reservation->user->name)) ? $reservation->user->name : 'غير محدد')); ?> - <?php echo e($reservationTime->format('H:i')); ?>">
                                <?php echo e($reservation->table->table_number); ?>

                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <!-- مقياس الساعات -->
                    <div class="absolute inset-x-0 -bottom-6 h-4 flex">
                        <?php for($hour = 0; $hour < 24; $hour += 3): ?>
                            <div class="flex-1 border-r border-white/20 relative">
                                <span class="absolute -bottom-5 right-0 text-xs text-white/60"><?php echo e(sprintf('%02d:00', $hour)); ?></span>
                            </div>
                        <?php endfor; ?>
                    </div>
                </div>
            </div>

            <!-- قائمة الحجوزات -->
            <div class="space-y-4 max-h-80 overflow-y-auto custom-scrollbar">
                <?php $__empty_1 = true; $__currentLoopData = $todayReservations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reservation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <?php
                        $isPast = $reservation->reservation_time->isPast();
                        $isCurrent = $reservation->reservation_time->isPast() && $reservation->reservation_time->addHours(1)->isFuture();
                        $borderColor = $isPast ? ($isCurrent ? 'from-blue-400 to-cyan-400' : 'from-gray-400 to-slate-400') : 'from-green-400 to-emerald-400';
                        $bgColor = $isPast ? ($isCurrent ? 'bg-gradient-to-r from-blue-500/10 to-cyan-500/10' : 'bg-gradient-to-r from-gray-500/10 to-slate-500/10') : 'bg-gradient-to-r from-green-500/10 to-emerald-500/10';
                    ?>
                    <div class="p-4 rounded-2xl bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 shadow-sm reservation-item card-hover" id="reservation-<?php echo e($reservation->reservation_id); ?>">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center mb-3">
                                    <?php if($reservation->user && !empty($reservation->user->first_name) && !empty($reservation->user->last_name)): ?>
                                        <div class="w-10 h-10 rounded-full bg-gradient-to-br <?php echo e($borderColor); ?> flex items-center justify-center text-white text-sm font-bold mr-3">
                                            <?php echo e(substr($reservation->user->first_name, 0, 1)); ?>

                                        </div>
                                        <div>
                                            <p class="font-bold text-white text-lg"><?php echo e($reservation->user->first_name); ?> <?php echo e($reservation->user->last_name); ?></p>
                                    <?php elseif($reservation->user && !empty($reservation->user->name)): ?>
                                        <div class="w-10 h-10 rounded-full bg-gradient-to-br <?php echo e($borderColor); ?> flex items-center justify-center text-white text-sm font-bold mr-3">
                                            <?php echo e(substr($reservation->user->name, 0, 1)); ?>

                                        </div>
                                        <div>
                                            <p class="font-bold text-white text-lg"><?php echo e($reservation->user->name); ?></p>
                                    <?php else: ?>
                                        <div class="w-10 h-10 rounded-full bg-gray-500 flex items-center justify-center text-white text-sm font-bold mr-3">
                                            ?
                                        </div>
                                        <div>
                                            <p class="font-bold text-white text-lg">غير محدد</p>
                                    <?php endif; ?>
                                        <div class="flex items-center mt-1 text-sm text-white/70 space-x-4 space-x-reverse">
                                            <span class="flex items-center">
                                                <i class="fas fa-chair mr-2 text-white/60"></i>
                                                طاولة #<?php echo e($reservation->table->table_number); ?>

                                            </span>
                                            <span class="flex items-center">
                                                <i class="fas fa-users mr-2 text-white/60"></i>
                                                <?php echo e($reservation->guests_count); ?> أشخاص
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-left">
                                <p class="font-bold text-white text-xl mb-1"><?php echo e($reservation->reservation_time->format('H:i')); ?></p>
                                <p class="text-sm">
                                    <?php if($isPast): ?>
                                        <?php if($isCurrent): ?>
                                            <span class="text-blue-300 flex items-center">
                                                <i class="fas fa-clock mr-2 animate-pulse"></i>حالياً
                                            </span>
                                        <?php else: ?>
                                            <span class="text-gray-300 flex items-center">
                                                <i class="fas fa-history mr-2"></i>مضى <?php echo e($reservation->reservation_time->diffForHumans()); ?>

                                            </span>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="text-green-300 flex items-center">
                                            <i class="fas fa-hourglass-start mr-2 animate-pulse"></i>قادم <?php echo e($reservation->reservation_time->diffForHumans()); ?>

                                        </span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                        <div class="flex justify-end mt-4 space-x-2 space-x-reverse">
                            <button type="button" class="px-3 py-2 rounded-xl bg-gray-100 hover:bg-gray-200 text-gray-700 hover:text-gray-900 transition-all duration-300 group text-sm" onclick="window.location.href='<?php echo e(route('employee.reservations.show', $reservation->reservation_id)); ?>'">
                                <i class="fas fa-eye mr-2 group-hover:scale-110 transition-transform"></i>عرض
                            </button>
                            <?php if(!$isPast || $isCurrent): ?>
                                <button type="button" class="px-3 py-2 rounded-xl bg-gradient-to-r from-blue-500/20 to-cyan-500/20 hover:from-blue-500/30 hover:to-cyan-500/30 text-blue-300 hover:text-blue-200 transition-all duration-300 group border border-blue-500/30 text-sm" onclick="window.location.href='<?php echo e(route('employee.reservations.edit', $reservation->reservation_id)); ?>'">
                                    <i class="fas fa-edit mr-2 group-hover:scale-110 transition-transform"></i>تعديل
                                </button>
                                <button type="button" class="px-3 py-2 rounded-xl bg-gradient-to-r from-green-500/20 to-emerald-500/20 hover:from-green-500/30 hover:to-emerald-500/30 text-green-300 hover:text-green-200 transition-all duration-300 group border border-green-500/30 text-sm" onclick="showAttendanceConfirmModal(<?php echo e($reservation->reservation_id); ?>, '<?php echo e($reservation->user->first_name ?? ''); ?> <?php echo e($reservation->user->last_name ?? ''); ?>', '<?php echo e($reservation->table->table_number ?? ''); ?>', '<?php echo e($reservation->reservation_time->format('H:i')); ?>')">
                                    <i class="fas fa-check-circle mr-2 group-hover:scale-110 transition-transform"></i>تأكيد الحضور
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="bg-white dark:bg-gray-700 rounded-2xl p-8 text-center border border-gray-200 dark:border-gray-600 shadow-sm">
                        <div class="w-20 h-20 rounded-full bg-gray-100 dark:bg-gray-600 flex items-center justify-center mb-4 mx-auto">
                            <i class="fas fa-calendar-day text-3xl text-gray-400 dark:text-gray-300"></i>
                        </div>
                        <p class="text-gray-800 dark:text-white text-xl font-bold mb-2">لا توجد حجوزات لهذا اليوم</p>
                        <a href="<?php echo e(route('employee.reservations.create')); ?>" class="mt-4 inline-block px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-semibold hover:scale-105 transition-transform duration-300">
                            <i class="fas fa-plus mr-2"></i>إضافة حجز جديد
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- سكريبت التفاعل مع الحجوزات -->
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // التفاعل مع مؤشرات الحجوزات في الجدول الزمني
                    const reservationMarkers = document.querySelectorAll('.reservation-marker');

                    reservationMarkers.forEach(marker => {
                        marker.addEventListener('click', function() {
                            const reservationId = this.getAttribute('data-reservation-id');
                            const reservationItem = document.getElementById('reservation-' + reservationId);

                            // تمرير إلى عنصر الحجز في القائمة
                            if (reservationItem) {
                                reservationItem.scrollIntoView({ behavior: 'smooth', block: 'center' });

                                // إضافة تأثير وميض للعنصر
                                reservationItem.classList.add('bg-primary/20');
                                setTimeout(() => {
                                    reservationItem.classList.remove('bg-primary/20');
                                }, 1000);
                            }
                        });
                    });
                });
            </script>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- حالة الطاولات -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <div>
                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">حالة الطاولات</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        <span class="text-green-500"><?php echo e($tableStats['available'] ?? 0); ?></span> متاحة،
                        <span class="text-red-500"><?php echo e($tableStats['occupied'] ?? 0); ?></span> مشغولة،
                        <span class="text-yellow-500"><?php echo e($tableStats['reserved'] ?? 0); ?></span> محجوزة
                    </p>
                </div>
                <div class="flex items-center">
                    <div class="relative ml-2">
                        <select id="tableAreaFilter" class="appearance-none bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 py-1 px-3 pr-8 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary">
                            <option value="all">جميع المناطق</option>
                            <option value="window">قرب النافذة</option>
                            <option value="family">منطقة عائلية</option>
                            <option value="main">المنطقة الرئيسية</option>
                            <option value="vip">VIP</option>
                            <option value="outdoor">الشرفة الخارجية</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
                            <i class="fas fa-chevron-down text-xs"></i>
                        </div>
                    </div>
                    <a href="<?php echo e(route('employee.tables')); ?>" class="text-primary text-sm hover:underline">عرض الكل</a>
                </div>
            </div>

            <!-- مخطط المطعم -->
            <div class="restaurant-layout mb-4 p-4 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
                <div class="flex justify-between items-center mb-2">
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">مخطط المطعم</h4>
                    <div class="flex text-xs">
                        <span class="ml-2 flex items-center">
                            <span class="w-3 h-3 bg-green-500/20 border border-green-500 rounded-sm inline-block ml-1"></span>
                            متاح
                        </span>
                        <span class="ml-2 flex items-center">
                            <span class="w-3 h-3 bg-red-500/20 border border-red-500 rounded-sm inline-block ml-1"></span>
                            مشغول
                        </span>
                        <span class="flex items-center">
                            <span class="w-3 h-3 bg-yellow-500/20 border border-yellow-500 rounded-sm inline-block ml-1"></span>
                            محجوز
                        </span>
                    </div>
                </div>

                <div class="restaurant-map relative h-80 border-2 border-gray-300 dark:border-gray-600 rounded-xl overflow-hidden bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900">
                    <!-- مناطق المطعم -->
                    <div class="absolute inset-0 p-3">

                        <!-- منطقة قرب النافذة - أعلى يمين -->
                        <div class="absolute top-3 right-3 w-36 h-16 border-2 border-blue-400 dark:border-blue-500 rounded-lg bg-blue-100/70 dark:bg-blue-900/30 p-1 shadow-md">
                            <div class="flex items-center text-xs text-blue-700 dark:text-blue-300 font-semibold mb-1">
                                <i class="fas fa-window-maximize ml-1"></i>
                                قرب النافذة
                            </div>
                            <div class="flex flex-wrap gap-1 justify-start">
                                <?php $__currentLoopData = $tables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $table): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($table->area == 'window'): ?>
                                        <div class="restaurant-table table-<?php echo e($table->status); ?> cursor-pointer window-table-mini" data-table-id="<?php echo e($table->table_id); ?>" data-area="<?php echo e($table->area); ?>">
                                            <span class="table-number text-xs"><?php echo e($table->table_number); ?></span>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>

                        <!-- المنطقة العائلية - وسط يمين -->
                        <div class="absolute top-20 right-3 w-40 h-20 border-2 border-green-400 dark:border-green-500 rounded-lg bg-green-100/70 dark:bg-green-900/30 p-1 shadow-md">
                            <div class="flex items-center text-xs text-green-700 dark:text-green-300 font-semibold mb-1">
                                <i class="fas fa-users ml-1"></i>
                                منطقة عائلية
                            </div>
                            <div class="flex flex-wrap gap-1 justify-start">
                                <?php $__currentLoopData = $tables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $table): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($table->area == 'family'): ?>
                                        <div class="restaurant-table table-<?php echo e($table->status); ?> cursor-pointer family-table-mini" data-table-id="<?php echo e($table->table_id); ?>" data-area="<?php echo e($table->area); ?>">
                                            <span class="table-number text-xs"><?php echo e($table->table_number); ?></span>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>

                        <!-- المنطقة الرئيسية - وسط -->
                        <div class="absolute top-3 left-3 right-48 bottom-16 border-2 border-gray-400 dark:border-gray-500 rounded-lg bg-gray-100/70 dark:bg-gray-800/50 p-1 shadow-md">
                            <div class="flex items-center text-xs text-gray-700 dark:text-gray-300 font-semibold mb-1">
                                <i class="fas fa-utensils ml-1"></i>
                                المنطقة الرئيسية
                            </div>
                            <div class="grid grid-cols-3 gap-1 h-full pt-1">
                                <?php $__currentLoopData = $tables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $table): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($table->area == 'main'): ?>
                                        <div class="restaurant-table table-<?php echo e($table->status); ?> cursor-pointer main-table-mini" data-table-id="<?php echo e($table->table_id); ?>" data-area="<?php echo e($table->area); ?>">
                                            <span class="table-number text-xs"><?php echo e($table->table_number); ?></span>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>

                        <!-- منطقة VIP - أسفل يمين -->
                        <div class="absolute bottom-3 right-3 w-40 h-12 border-2 border-yellow-400 dark:border-yellow-500 rounded-lg bg-gradient-to-r from-yellow-100 to-orange-100 dark:from-yellow-900/30 dark:to-orange-900/30 p-1 shadow-md">
                            <div class="flex items-center text-xs text-yellow-700 dark:text-yellow-300 font-semibold mb-1">
                                <i class="fas fa-crown ml-1"></i>
                                VIP
                            </div>
                            <div class="flex justify-start items-center gap-1">
                                <?php $__currentLoopData = $tables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $table): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($table->area == 'vip'): ?>
                                        <div class="restaurant-table table-<?php echo e($table->status); ?> cursor-pointer vip-table-mini" data-table-id="<?php echo e($table->table_id); ?>" data-area="<?php echo e($table->area); ?>">
                                            <span class="table-number text-xs"><?php echo e($table->table_number); ?></span>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>

                        <!-- الشرفة الخارجية - أسفل يسار -->
                        <div class="absolute bottom-3 left-3 w-36 h-12 border-2 border-purple-400 dark:border-purple-500 rounded-lg bg-purple-100/70 dark:bg-purple-900/30 p-1 shadow-md">
                            <div class="flex items-center text-xs text-purple-700 dark:text-purple-300 font-semibold mb-1">
                                <i class="fas fa-tree ml-1"></i>
                                الشرفة الخارجية
                            </div>
                            <div class="flex justify-start items-center gap-1">
                                <?php $__currentLoopData = $tables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $table): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($table->area == 'outdoor'): ?>
                                        <div class="restaurant-table table-<?php echo e($table->status); ?> cursor-pointer outdoor-table-mini" data-table-id="<?php echo e($table->table_id); ?>" data-area="<?php echo e($table->area); ?>">
                                            <span class="table-number text-xs"><?php echo e($table->table_number); ?></span>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>

                        <!-- عناصر ديكورية صغيرة -->
                        <div class="absolute top-5 left-5 w-4 h-4 bg-green-300 dark:bg-green-600 rounded-full opacity-60 flex items-center justify-center">
                            <i class="fas fa-leaf text-green-700 dark:text-green-200 text-xs"></i>
                        </div>
                        <div class="absolute bottom-5 left-12 w-5 h-5 bg-brown-300 dark:bg-yellow-600 rounded-full opacity-50 flex items-center justify-center">
                            <i class="fas fa-coffee text-yellow-800 dark:text-yellow-200 text-xs"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قائمة الطاولات -->
            <div class="table-grid" id="tablesGrid">
                <?php $__empty_1 = true; $__currentLoopData = $tables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $table): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="restaurant-table table-<?php echo e($table->status); ?> cursor-pointer" data-table-id="<?php echo e($table->table_id); ?>" data-area="<?php echo e($table->area); ?>">
                    <span class="table-number"><?php echo e($table->table_number); ?></span>
                    <div class="flex flex-col items-center">
                        <span class="table-capacity mb-1"><?php echo e($table->capacity); ?> <i class="fas fa-user-friends text-xs"></i></span>
                        <span class="table-status
                            <?php if($table->status == 'available'): ?>
                                bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300
                            <?php elseif($table->status == 'occupied'): ?>
                                bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300
                            <?php elseif($table->status == 'reserved'): ?>
                                bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300
                            <?php endif; ?>
                        ">
                            <?php if($table->status == 'available'): ?>
                                متاح
                            <?php elseif($table->status == 'occupied'): ?>
                                مشغول
                            <?php elseif($table->status == 'reserved'): ?>
                                محجوز
                            <?php endif; ?>
                        </span>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-span-full text-center py-4 text-gray-500 dark:text-gray-400">
                    <i class="fas fa-chair text-4xl mb-2 text-gray-300 dark:text-gray-600"></i>
                    <p>لا توجد طاولات متاحة</p>
                </div>
                <?php endif; ?>
            </div>

            <!-- سكريبت تصفية الطاولات -->
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const areaFilter = document.getElementById('tableAreaFilter');
                    const tables = document.querySelectorAll('.restaurant-table');

                    // تصفية حسب المنطقة
                    areaFilter.addEventListener('change', filterTables);

                    function filterTables() {
                        const areaValue = areaFilter.value;

                        tables.forEach(table => {
                            const tableArea = table.getAttribute('data-area');

                            if (areaValue === 'all' || tableArea === areaValue) {
                                table.style.display = '';
                            } else {
                                table.style.display = 'none';
                            }
                        });
                    }

                    // التفاعل مع الطاولات
                    tables.forEach(table => {
                        table.addEventListener('click', function() {
                            const tableId = this.getAttribute('data-table-id');
                            const tableStatus = this.classList.contains('table-available') ? 'available' :
                                              (this.classList.contains('table-occupied') ? 'occupied' : 'reserved');

                            // إظهار قائمة الإجراءات المتاحة للطاولة
                            showTableActions(tableId, tableStatus);
                        });
                    });

                    function showTableActions(tableId, status) {
                        let actions = '';

                        if (status === 'available') {
                            actions = `
                                <a href="<?php echo e(route('employee.reservations.create')); ?>?table_id=${tableId}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-calendar-plus ml-2"></i>إنشاء حجز
                                </a>
                                <a href="<?php echo e(route('employee.orders.create')); ?>?table_id=${tableId}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-utensils ml-2"></i>إنشاء طلب
                                </a>
                            `;
                        } else if (status === 'occupied') {
                            actions = `
                                <a href="<?php echo e(route('employee.tables.view-order')); ?>?table_id=${tableId}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-eye ml-2"></i>عرض الطلب
                                </a>
                                <a href="<?php echo e(route('employee.tables.free')); ?>?table_id=${tableId}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-check-circle ml-2"></i>تحرير الطاولة
                                </a>
                            `;
                        } else if (status === 'reserved') {
                            actions = `
                                <a href="<?php echo e(route('employee.reservations')); ?>?table_id=${tableId}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-calendar-check ml-2"></i>عرض الحجز
                                </a>
                                <a href="<?php echo e(route('employee.tables.cancel-reservation')); ?>?table_id=${tableId}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-times-circle ml-2"></i>إلغاء الحجز
                                </a>
                            `;
                        }

                        // إنشاء قائمة منبثقة
                        const popup = document.createElement('div');
                        popup.className = 'fixed inset-0 z-50 flex items-center justify-center';
                        popup.innerHTML = `
                            <div class="fixed inset-0 bg-black bg-opacity-50" id="tableActionsOverlay"></div>
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl z-10 w-72 overflow-hidden">
                                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                                    <h3 class="font-bold text-gray-800 dark:text-white">إجراءات الطاولة #${tableId}</h3>
                                    <button id="closeTableActions" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div class="py-2">
                                    ${actions}
                                </div>
                            </div>
                        `;

                        document.body.appendChild(popup);

                        // إغلاق القائمة المنبثقة
                        document.getElementById('closeTableActions').addEventListener('click', function() {
                            document.body.removeChild(popup);
                        });

                        document.getElementById('tableActionsOverlay').addEventListener('click', function() {
                            document.body.removeChild(popup);
                        });
                    }
                });
            </script>
        </div>

        <!-- أحدث الإشعارات -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <div>
                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">أحدث الإشعارات</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        <span class="text-primary"><?php echo e($notifications->where('is_read', 0)->count()); ?></span> غير مقروءة من أصل <?php echo e($notifications->count()); ?>

                    </p>
                </div>
                <div class="flex items-center">
                    <button id="markAllAsRead" class="p-1.5 bg-primary/10 text-primary rounded-md hover:bg-primary/20 ml-2" title="تعليم الكل كمقروء">
                        <i class="fas fa-check-double"></i>
                    </button>
                    <a href="<?php echo e(route('employee.notifications')); ?>" class="text-primary text-sm hover:underline">عرض الكل</a>
                </div>
            </div>

            <!-- تصفية الإشعارات -->
            <div class="mb-4 flex">
                <button class="notification-filter px-3 py-1 text-xs rounded-full mr-2 bg-primary text-white" data-filter="all">الكل</button>
                <button class="notification-filter px-3 py-1 text-xs rounded-full mr-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300" data-filter="unread">غير مقروءة</button>
                <button class="notification-filter px-3 py-1 text-xs rounded-full mr-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300" data-filter="orders">الطلبات</button>
                <button class="notification-filter px-3 py-1 text-xs rounded-full bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300" data-filter="system">النظام</button>
            </div>

            <div class="space-y-3 max-h-80 overflow-y-auto" id="notificationsContainer">
                <?php $__empty_1 = true; $__currentLoopData = $notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="p-3 rounded-lg border-r-4 <?php echo e($notification->is_read ? 'border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700/30' : 'border-primary bg-primary/5 dark:bg-primary/10'); ?> relative notification-item <?php echo e($notification->is_read ? 'read' : 'unread'); ?> <?php echo e($notification->type); ?>" data-notification-id="<?php echo e($notification->notification_id); ?>">
                    <div class="flex justify-between">
                        <div class="flex items-start">
                            <div class="mt-0.5 ml-3">
                                <?php if($notification->type == 'order'): ?>
                                    <span class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-500 flex items-center justify-center">
                                        <i class="fas fa-shopping-cart"></i>
                                    </span>
                                <?php elseif($notification->type == 'reservation'): ?>
                                    <span class="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-500 flex items-center justify-center">
                                        <i class="fas fa-calendar-check"></i>
                                    </span>
                                <?php elseif($notification->type == 'system'): ?>
                                    <span class="w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-900/30 text-gray-500 flex items-center justify-center">
                                        <i class="fas fa-cog"></i>
                                    </span>
                                <?php else: ?>
                                    <span class="w-8 h-8 rounded-full bg-primary/10 text-primary flex items-center justify-center">
                                        <i class="fas fa-bell"></i>
                                    </span>
                                <?php endif; ?>
                            </div>
                            <div>
                                <p class="text-gray-700 dark:text-gray-300 <?php echo e($notification->is_read ? '' : 'font-medium'); ?>"><?php echo e($notification->message); ?></p>
                                <div class="flex items-center mt-1">
                                    <p class="text-xs text-gray-500 dark:text-gray-400"><?php echo e($notification->created_at->diffForHumans()); ?></p>
                                    <?php if($notification->link): ?>
                                        <a href="<?php echo e($notification->link); ?>" class="text-xs text-primary hover:underline mr-3">عرض التفاصيل</a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <div>
                            <?php if(!$notification->is_read): ?>
                                <button class="mark-as-read p-1 text-gray-400 hover:text-primary" title="تعليم كمقروء" data-id="<?php echo e($notification->notification_id); ?>">
                                    <i class="fas fa-check"></i>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="p-4 rounded-lg border border-gray-200 dark:border-gray-700 text-center" id="emptyNotifications">
                    <i class="fas fa-bell-slash text-4xl text-gray-300 dark:text-gray-600 mb-2"></i>
                    <p class="text-gray-500 dark:text-gray-400">لا توجد إشعارات جديدة</p>
                </div>
                <?php endif; ?>
            </div>

            <?php if($notifications->count() > 0): ?>
            <div class="mt-4 text-center">
                <a href="<?php echo e(route('employee.notifications')); ?>" class="text-primary hover:underline text-sm">عرض كل الإشعارات</a>
            </div>
            <?php endif; ?>

            <!-- سكريبت التفاعل مع الإشعارات -->
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // تصفية الإشعارات
                    const filterButtons = document.querySelectorAll('.notification-filter');
                    const notifications = document.querySelectorAll('.notification-item');
                    const emptyNotifications = document.getElementById('emptyNotifications');

                    filterButtons.forEach(button => {
                        button.addEventListener('click', function() {
                            // تحديث حالة الأزرار
                            filterButtons.forEach(btn => {
                                btn.classList.remove('bg-primary', 'text-white');
                                btn.classList.add('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
                            });

                            this.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
                            this.classList.add('bg-primary', 'text-white');

                            const filter = this.getAttribute('data-filter');

                            // تصفية الإشعارات
                            let visibleCount = 0;

                            notifications.forEach(notification => {
                                if (filter === 'all' ||
                                    (filter === 'unread' && notification.classList.contains('unread')) ||
                                    (filter === 'orders' && notification.classList.contains('order')) ||
                                    (filter === 'system' && notification.classList.contains('system'))) {
                                    notification.style.display = '';
                                    visibleCount++;
                                } else {
                                    notification.style.display = 'none';
                                }
                            });

                            // إظهار رسالة إذا لم يتم العثور على إشعارات
                            if (visibleCount === 0 && !emptyNotifications) {
                                const container = document.getElementById('notificationsContainer');
                                const emptyMessage = document.createElement('div');
                                emptyMessage.id = 'emptyFilteredNotifications';
                                emptyMessage.className = 'p-4 rounded-lg border border-gray-200 dark:border-gray-700 text-center';
                                emptyMessage.innerHTML = `
                                    <i class="fas fa-filter text-4xl text-gray-300 dark:text-gray-600 mb-2"></i>
                                    <p class="text-gray-500 dark:text-gray-400">لا توجد إشعارات تطابق الفلتر المحدد</p>
                                `;
                                container.appendChild(emptyMessage);
                            } else if (visibleCount > 0) {
                                const emptyFiltered = document.getElementById('emptyFilteredNotifications');
                                if (emptyFiltered) {
                                    emptyFiltered.remove();
                                }
                            }
                        });
                    });

                    // تعليم الإشعار كمقروء
                    const markAsReadButtons = document.querySelectorAll('.mark-as-read');

                    markAsReadButtons.forEach(button => {
                        button.addEventListener('click', function() {
                            const notificationId = this.getAttribute('data-id');
                            const notification = document.querySelector(`.notification-item[data-notification-id="${notificationId}"]`);

                            // هنا يمكن إضافة طلب AJAX لتحديث حالة الإشعار في قاعدة البيانات

                            // تحديث واجهة المستخدم
                            notification.classList.remove('unread', 'border-primary', 'bg-primary/5', 'dark:bg-primary/10');
                            notification.classList.add('read', 'border-gray-300', 'dark:border-gray-600', 'bg-gray-50', 'dark:bg-gray-700/30');

                            // إزالة زر التعليم كمقروء
                            this.remove();

                            // تحديث عداد الإشعارات غير المقروءة
                            const unreadCount = document.querySelectorAll('.notification-item.unread').length;
                            const countElement = document.querySelector('.text-primary');
                            if (countElement) {
                                countElement.textContent = unreadCount;
                            }

                            // تحديث عداد الإشعارات في الهيدر
                            const headerCount = document.getElementById('notificationCount');
                            if (headerCount) {
                                headerCount.textContent = unreadCount;
                            }
                        });
                    });

                    // تعليم كل الإشعارات كمقروءة
                    const markAllAsReadButton = document.getElementById('markAllAsRead');

                    markAllAsReadButton.addEventListener('click', function() {
                        const unreadNotifications = document.querySelectorAll('.notification-item.unread');

                        unreadNotifications.forEach(notification => {
                            // هنا يمكن إضافة طلب AJAX لتحديث حالة الإشعار في قاعدة البيانات

                            // تحديث واجهة المستخدم
                            notification.classList.remove('unread', 'border-primary', 'bg-primary/5', 'dark:bg-primary/10');
                            notification.classList.add('read', 'border-gray-300', 'dark:border-gray-600', 'bg-gray-50', 'dark:bg-gray-700/30');

                            // إزالة زر التعليم كمقروء
                            const markButton = notification.querySelector('.mark-as-read');
                            if (markButton) {
                                markButton.remove();
                            }
                        });

                        // تحديث عداد الإشعارات غير المقروءة
                        const countElement = document.querySelector('.text-primary');
                        if (countElement) {
                            countElement.textContent = '0';
                        }

                        // تحديث عداد الإشعارات في الهيدر
                        const headerCount = document.getElementById('notificationCount');
                        if (headerCount) {
                            headerCount.textContent = '0';
                        }
                    });
                });
            </script>

            <!-- CSS للطاولات المصغرة في المخطط -->
            <style>
                .window-table-mini, .family-table-mini, .main-table-mini, .vip-table-mini, .outdoor-table-mini {
                    width: 20px;
                    height: 20px;
                    border-radius: 4px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 10px;
                    font-weight: bold;
                    transition: all 0.2s ease;
                    border: 1px solid;
                }

                .window-table-mini:hover, .family-table-mini:hover, .main-table-mini:hover,
                .vip-table-mini:hover, .outdoor-table-mini:hover {
                    transform: scale(1.1);
                    z-index: 10;
                }

                /* حالات الطاولات المصغرة */
                .window-table-mini.table-available, .family-table-mini.table-available,
                .main-table-mini.table-available, .vip-table-mini.table-available,
                .outdoor-table-mini.table-available {
                    background-color: rgba(34, 197, 94, 0.8);
                    border-color: rgb(34, 197, 94);
                    color: white;
                }

                .window-table-mini.table-occupied, .family-table-mini.table-occupied,
                .main-table-mini.table-occupied, .vip-table-mini.table-occupied,
                .outdoor-table-mini.table-occupied {
                    background-color: rgba(239, 68, 68, 0.8);
                    border-color: rgb(239, 68, 68);
                    color: white;
                }

                .window-table-mini.table-reserved, .family-table-mini.table-reserved,
                .main-table-mini.table-reserved, .vip-table-mini.table-reserved,
                .outdoor-table-mini.table-reserved {
                    background-color: rgba(245, 158, 11, 0.8);
                    border-color: rgb(245, 158, 11);
                    color: white;
                }

                /* تحسين شكل المخطط */
                .restaurant-map {
                    background-image:
                        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
                }

                .dark .restaurant-map {
                    background-image:
                        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.05) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.05) 0%, transparent 50%),
                        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.05) 0%, transparent 50%);
                }
            </style>
        </div>
    </div>

    <!-- قسم العروض -->
    <?php if(isset($offerStats)): ?>
    <div class="mb-8">
        <div class="bg-white dark:bg-gray-800 rounded-3xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h3 class="text-2xl font-bold text-gray-800 dark:text-white mb-2 flex items-center">
                        <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-orange-500 to-amber-500 flex items-center justify-center mr-3">
                            <i class="fas fa-tags text-white text-sm"></i>
                        </div>
                        إدارة العروض
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400">نظرة عامة على العروض والخصومات</p>
                </div>
                <a href="<?php echo e(route('employee.offers.index')); ?>"
                   class="px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white rounded-xl transition-all duration-300 font-semibold shadow-lg hover:shadow-xl">
                    <i class="fas fa-eye ml-2"></i>
                    عرض الكل
                </a>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- العروض النشطة -->
                <div class="bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/30 rounded-2xl p-6 border border-green-200 dark:border-green-700">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                            <i class="fas fa-check-circle text-white text-xl"></i>
                        </div>
                        <span class="text-3xl font-bold text-green-700 dark:text-green-400"><?php echo e($offerStats['active']); ?></span>
                    </div>
                    <h4 class="text-lg font-semibold text-green-800 dark:text-green-300 mb-1">العروض النشطة</h4>
                    <p class="text-green-600 dark:text-green-400 text-sm">العروض المتاحة حالياً</p>
                </div>

                <!-- العروض القادمة -->
                <div class="bg-gradient-to-br from-yellow-50 to-amber-100 dark:from-yellow-900/20 dark:to-amber-900/30 rounded-2xl p-6 border border-yellow-200 dark:border-yellow-700">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-amber-600 rounded-xl flex items-center justify-center">
                            <i class="fas fa-clock text-white text-xl"></i>
                        </div>
                        <span class="text-3xl font-bold text-yellow-700 dark:text-yellow-400"><?php echo e($offerStats['upcoming']); ?></span>
                    </div>
                    <h4 class="text-lg font-semibold text-yellow-800 dark:text-yellow-300 mb-1">العروض القادمة</h4>
                    <p class="text-yellow-600 dark:text-yellow-400 text-sm">ستبدأ قريباً</p>
                </div>

                <!-- العروض المنتهية -->
                <div class="bg-gradient-to-br from-red-50 to-rose-100 dark:from-red-900/20 dark:to-rose-900/30 rounded-2xl p-6 border border-red-200 dark:border-red-700">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-rose-600 rounded-xl flex items-center justify-center">
                            <i class="fas fa-times-circle text-white text-xl"></i>
                        </div>
                        <span class="text-3xl font-bold text-red-700 dark:text-red-400"><?php echo e($offerStats['expired']); ?></span>
                    </div>
                    <h4 class="text-lg font-semibold text-red-800 dark:text-red-300 mb-1">العروض المنتهية</h4>
                    <p class="text-red-600 dark:text-red-400 text-sm">انتهت صلاحيتها</p>
                </div>

                <!-- إجمالي العروض -->
                <div class="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/30 rounded-2xl p-6 border border-blue-200 dark:border-blue-700">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                            <i class="fas fa-tags text-white text-xl"></i>
                        </div>
                        <span class="text-3xl font-bold text-blue-700 dark:text-blue-400"><?php echo e($offerStats['total']); ?></span>
                    </div>
                    <h4 class="text-lg font-semibold text-blue-800 dark:text-blue-300 mb-1">إجمالي العروض</h4>
                    <p class="text-blue-600 dark:text-blue-400 text-sm">جميع العروض</p>
                </div>
            </div>

            <!-- إجراءات سريعة -->
            <div class="flex flex-wrap gap-3">
                <a href="<?php echo e(route('employee.offers.create')); ?>"
                   class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white rounded-lg transition-all duration-300 font-medium shadow-md hover:shadow-lg">
                    <i class="fas fa-plus ml-2"></i>
                    إضافة عرض جديد
                </a>
                <a href="<?php echo e(route('employee.offers.index', ['status' => 'active'])); ?>"
                   class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white rounded-lg transition-all duration-300 font-medium shadow-md hover:shadow-lg">
                    <i class="fas fa-eye ml-2"></i>
                    العروض النشطة
                </a>
                <a href="<?php echo e(route('employee.offers.index', ['status' => 'upcoming'])); ?>"
                   class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-yellow-500 to-amber-500 hover:from-yellow-600 hover:to-amber-600 text-white rounded-lg transition-all duration-300 font-medium shadow-md hover:shadow-lg">
                    <i class="fas fa-clock ml-2"></i>
                    العروض القادمة
                </a>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- نافذة تأكيد الحضور -->
<div id="attendanceConfirmModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center transition-all duration-300 opacity-0 attendance-modal-backdrop" onclick="hideAttendanceConfirmModal()">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300 scale-95 border border-gray-200 dark:border-gray-600 attendance-modal" onclick="event.stopPropagation()">
        <!-- Header منسق -->
        <div class="bg-gradient-to-r from-green-500 to-emerald-600 rounded-t-xl p-6 relative overflow-hidden">
            <!-- خلفية منسقة -->
            <div class="absolute inset-0 bg-white bg-opacity-10"></div>
            <div class="absolute -top-4 -right-4 w-24 h-24 bg-white bg-opacity-10 rounded-full"></div>
            <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-white bg-opacity-10 rounded-full"></div>

            <div class="relative z-10">
                <div class="flex items-center justify-center w-16 h-16 mx-auto bg-white bg-opacity-20 rounded-full mb-4 pulse-ring">
                    <i class="fas fa-user-check text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-white text-center mb-2">تأكيد حضور العميل</h3>
                <p class="text-green-100 text-sm text-center">تأكيد وصول العميل للمطعم</p>
            </div>
        </div>

        <!-- Content -->
        <div class="p-6">
            <div id="attendanceReservationInfo" class="text-center mb-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <!-- سيتم ملء المعلومات هنا -->
            </div>

            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
                <div class="flex items-center">
                    <i class="fas fa-info-circle text-blue-500 ml-3"></i>
                    <p class="text-sm text-blue-700 dark:text-blue-300">
                        سيتم تحديث حالة الحجز إلى "مكتمل" وإشعار العميل
                    </p>
                </div>
            </div>

            <div class="flex gap-3">
                <button onclick="hideAttendanceConfirmModal()"
                        class="flex-1 px-4 py-3 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-lg transition-all duration-200 font-medium">
                    <i class="fas fa-times ml-2"></i>إلغاء
                </button>
                <button onclick="confirmAttendance()"
                        class="flex-1 px-4 py-3 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white rounded-lg transition-all duration-200 font-medium shadow-lg hover:shadow-xl">
                    <i class="fas fa-check ml-2"></i>تأكيد الحضور
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// متغير لحفظ معرف الحجز المؤقت
let currentAttendanceReservationId = null;

// إظهار نافذة تأكيد الحضور
function showAttendanceConfirmModal(reservationId, customerName, tableNumber, reservationTime) {
    currentAttendanceReservationId = reservationId;
    document.getElementById('attendanceReservationInfo').innerHTML = `
        <div class="space-y-3">
            <div class="flex items-center justify-center">
                <div class="bg-green-100 dark:bg-green-900/30 rounded-full p-3 mb-2">
                    <i class="fas fa-user text-green-600 dark:text-green-400 text-lg"></i>
                </div>
            </div>
            <div class="space-y-2">
                <p class="font-bold text-lg text-gray-800 dark:text-white">${customerName}</p>
                <div class="flex items-center justify-center space-x-4 space-x-reverse text-sm">
                    <span class="flex items-center text-gray-600 dark:text-gray-300">
                        <i class="fas fa-chair ml-1 text-blue-500"></i>
                        طاولة ${tableNumber}
                    </span>
                    <span class="flex items-center text-gray-600 dark:text-gray-300">
                        <i class="fas fa-clock ml-1 text-purple-500"></i>
                        ${reservationTime}
                    </span>
                </div>
                <p class="text-xs text-gray-500 dark:text-gray-400">رقم الحجز: #${reservationId}</p>
            </div>
        </div>
    `;
    const modal = document.getElementById('attendanceConfirmModal');
    modal.classList.remove('hidden');
    setTimeout(() => {
        modal.classList.remove('opacity-0');
        modal.querySelector('div > div').classList.remove('scale-95');
        modal.querySelector('div > div').classList.add('scale-100');
    }, 10);
}

// إخفاء نافذة تأكيد الحضور
function hideAttendanceConfirmModal() {
    const modal = document.getElementById('attendanceConfirmModal');
    modal.classList.add('opacity-0');
    modal.querySelector('div > div').classList.remove('scale-100');
    modal.querySelector('div > div').classList.add('scale-95');
    setTimeout(() => {
        modal.classList.add('hidden');
        currentAttendanceReservationId = null;
    }, 300);
}

// تأكيد الحضور
async function confirmAttendance() {
    if (!currentAttendanceReservationId) return;

    const confirmBtn = document.querySelector('#attendanceConfirmModal button[onclick="confirmAttendance()"]');
    const originalText = confirmBtn.innerHTML;
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري التأكيد...';
    confirmBtn.disabled = true;

    try {
        console.log('Sending attendance confirmation for reservation:', currentAttendanceReservationId);

        const response = await fetch(`/employee/reservations/${currentAttendanceReservationId}/status`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json'
            },
            body: JSON.stringify({ status: 'completed' })
        });

        console.log('Response status:', response.status);
        console.log('Response ok:', response.ok);

        const data = await response.json();
        console.log('Response data:', data);

        if (response.ok && data.success) {
            hideAttendanceConfirmModal();
            showNotification('✅ تم تأكيد حضور العميل بنجاح', 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            const errorMessage = data.message || 'حدث خطأ في تأكيد الحضور';
            showNotification('❌ ' + errorMessage, 'error');
            confirmBtn.innerHTML = originalText;
            confirmBtn.disabled = false;
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('❌ حدث خطأ في الاتصال', 'error');
        confirmBtn.innerHTML = originalText;
        confirmBtn.disabled = false;
    }
}

// إظهار الإشعارات
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

    if (type === 'success') {
        notification.className += ' bg-green-500 text-white';
    } else if (type === 'error') {
        notification.className += ' bg-red-500 text-white';
    } else {
        notification.className += ' bg-blue-500 text-white';
    }

    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : (type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle')} ml-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // إظهار الإشعار
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // إخفاء الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// إغلاق النوافذ بمفتاح Escape
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        if (!document.getElementById('attendanceConfirmModal').classList.contains('hidden')) {
            hideAttendanceConfirmModal();
        }
    }
});
</script>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('employee.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\Telegram Desktop\cs450 (3)\cs450 (3)\cs450\resources\views/employee/dashboard.blade.php ENDPATH**/ ?>