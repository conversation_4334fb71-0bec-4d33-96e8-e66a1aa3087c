<?php

/**
 * سكريبت إعادة ترتيب الهجرات
 * يقوم بإعادة تسمية ملفات الهجرات حسب الترتيب الصحيح للتبعيات
 */

$migrationsPath = __DIR__ . '/database/migrations/';
$backupPath = __DIR__ . '/database/migrations_backup/';

// إنشاء نسخة احتياطية
if (!is_dir($backupPath)) {
    mkdir($backupPath, 0755, true);
    echo "✅ تم إنشاء مجلد النسخة الاحتياطية\n";
}

// نسخ الملفات للنسخة الاحتياطية
$files = glob($migrationsPath . '*.php');
foreach ($files as $file) {
    $filename = basename($file);
    copy($file, $backupPath . $filename);
}
echo "✅ تم إنشاء نسخة احتياطية من الهجرات\n";

// ترتيب الهجرات الجديد
$migrationOrder = [
    // الجداول الأساسية
    'create_users_table.php' => '2025_01_01_000001_create_users_table.php',
    'create_sessions_table.php' => '2025_01_01_000002_create_sessions_table.php',
    'create_cache_table.php' => '2025_01_01_000003_create_cache_table.php',
    'create_permission_tables.php' => '2025_01_01_000004_create_permission_tables.php',
    'create_settings_table.php' => '2025_01_01_000005_create_settings_table.php',
    
    // جداول المواد والمخزون
    'create_ingredients_table.php' => '2025_01_01_000010_create_ingredients_table.php',
    'create_inventory_table.php' => '2025_01_01_000011_create_inventory_table.php',
    'create_inventory_transactions_table.php' => '2025_01_01_000012_create_inventory_transactions_table.php',
    
    // جداول القائمة والوصفات
    'create_menu_items_table.php' => '2025_01_01_000020_create_menu_items_table.php',
    'create_recipes_table.php' => '2025_01_01_000021_create_recipes_table.php',
    
    // جداول الطاولات والحجوزات
    'create_tables_table.php' => '2025_01_01_000030_create_tables_table.php',
    'create_reservations_table.php' => '2025_01_01_000031_create_reservations_table.php',
    
    // جداول الطلبات والمدفوعات
    'create_orders_table.php' => '2025_01_01_000040_create_orders_table.php',
    'create_order_items_table.php' => '2025_01_01_000041_create_order_items_table.php',
    'create_payments_table.php' => '2025_01_01_000042_create_payments_table.php',
    'create_cart_table.php' => '2025_01_01_000043_create_cart_table.php',
    
    // جداول التقييمات والإشعارات
    'create_reviews_table.php' => '2025_01_01_000050_create_reviews_table.php',
    'create_notifications_table.php' => '2025_01_01_000051_create_notifications_table.php',
    
    // جداول التقارير المالية
    'create_expenses_table.php' => '2025_01_01_000060_create_expenses_table.php',
    'create_invoices_table.php' => '2025_01_01_000061_create_invoices_table.php',
    'create_financial_reports_table.php' => '2025_01_01_000062_create_financial_reports_table.php',
    'create_report_sources_table.php' => '2025_01_01_000063_create_report_sources_table.php',
    
    // تعديلات الجداول
    'add_image_path_to_menu_items_table.php' => '2025_01_01_000100_add_image_path_to_menu_items_table.php',
    'add_expiry_date_to_ingredients_table.php' => '2025_01_01_000101_add_expiry_date_to_ingredients_table.php',
    'add_password_hash_to_users_table.php' => '2025_01_01_000102_add_password_hash_to_users_table.php',
    'add_cost_per_unit_to_ingredients_table.php' => '2025_01_01_000103_add_cost_per_unit_to_ingredients_table.php',
    'add_description_to_menu_items_table.php' => '2025_01_01_000104_add_description_to_menu_items_table.php',
    'add_menu_item_id_to_reviews_table.php' => '2025_01_01_000105_add_menu_item_id_to_reviews_table.php',
    'update_expenses_table.php' => '2025_01_01_000106_update_expenses_table.php',
    'add_location_to_tables_table.php' => '2025_01_01_000107_add_location_to_tables_table.php',
    'make_user_id_nullable_in_orders_table.php' => '2025_01_01_000108_make_user_id_nullable_in_orders_table.php',
    'add_customer_info_to_orders_table.php' => '2025_01_01_000109_add_customer_info_to_orders_table.php',
    'add_is_featured_to_menu_items_table.php' => '2025_01_01_000110_add_is_featured_to_menu_items_table.php',
    'add_address_to_users_table.php' => '2025_01_01_000111_add_address_to_users_table.php',
    'add_social_login_fields_to_users_table.php' => '2025_01_01_000112_add_social_login_fields_to_users_table.php',
    'add_is_featured_column_to_menu_items_table.php' => '2025_01_01_000113_add_is_featured_column_to_menu_items_table.php',
    'add_delivery_fields_to_orders_table.php' => '2025_01_01_000114_add_delivery_fields_to_orders_table.php',
    'add_party_size_and_special_requests_to_reservations_table.php' => '2025_01_01_000115_add_party_size_and_special_requests_to_reservations_table.php',
    'make_title_nullable_in_notifications_table.php' => '2025_01_01_000116_make_title_nullable_in_notifications_table.php',
    'add_offer_fields_to_reservations_table.php' => '2025_01_01_000117_add_offer_fields_to_reservations_table.php',
    'add_columns_to_notifications_table.php' => '2025_01_01_000118_add_columns_to_notifications_table.php',
    'add_is_approved_to_reviews_table.php' => '2025_01_01_000119_add_is_approved_to_reviews_table.php',
    'ensure_notifications_table_structure.php' => '2025_01_01_000120_ensure_notifications_table_structure.php',
    'add_automatic_fields_to_expenses_table.php' => '2025_01_01_000121_add_automatic_fields_to_expenses_table.php',
    'add_theme_preference_to_users_table.php' => '2025_01_01_000122_add_theme_preference_to_users_table.php',
];

// البحث عن الملفات الحالية وإعادة تسميتها
$currentFiles = glob($migrationsPath . '*.php');
$renamedCount = 0;

foreach ($currentFiles as $currentFile) {
    $currentFilename = basename($currentFile);
    
    // البحث عن اسم الملف في قائمة الترتيب الجديد
    foreach ($migrationOrder as $oldPattern => $newName) {
        if (strpos($currentFilename, $oldPattern) !== false) {
            $newPath = $migrationsPath . $newName;
            
            // إعادة تسمية الملف
            if (rename($currentFile, $newPath)) {
                echo "✅ تم إعادة تسمية: $currentFilename → $newName\n";
                $renamedCount++;
            } else {
                echo "❌ فشل في إعادة تسمية: $currentFilename\n";
            }
            break;
        }
    }
}

echo "\n🎉 تم الانتهاء من إعادة ترتيب الهجرات!\n";
echo "📊 تم إعادة تسمية $renamedCount ملف\n";
echo "💾 النسخة الاحتياطية محفوظة في: database/migrations_backup/\n";
echo "\n📋 الخطوات التالية:\n";
echo "1. تشغيل: php artisan migrate:fresh\n";
echo "2. تشغيل: php artisan db:seed (إذا كان لديك seeders)\n";
echo "3. اختبار النظام للتأكد من عمل كل شيء بشكل صحيح\n";

?>
