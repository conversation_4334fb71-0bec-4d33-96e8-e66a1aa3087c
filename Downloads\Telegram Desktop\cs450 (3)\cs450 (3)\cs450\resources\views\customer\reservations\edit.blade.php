@extends('customer.layouts.app')

@section('title', 'تعديل الحجز - Eat Hub')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- شريط التنقل السريع -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
            <div class="flex flex-wrap gap-2 justify-center md:justify-start">
                <a href="{{ route('customer.dashboard') }}" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white px-4 py-2 rounded-lg text-sm transition">
                    <i class="fas fa-tachometer-alt ml-1"></i>لوحة التحكم
                </a>
                <a href="{{ route('customer.reservations') }}" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white px-4 py-2 rounded-lg text-sm transition">
                    <i class="fas fa-calendar-alt ml-1"></i>حجوزاتي
                </a>
                <span class="bg-primary text-white px-4 py-2 rounded-lg text-sm">
                    <i class="fas fa-edit ml-1"></i>تعديل الحجز
                </span>
            </div>
        </div>

        <!-- عنوان الصفحة -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">تعديل الحجز</h1>
                    <p class="text-gray-600 dark:text-gray-400">قم بتعديل تفاصيل حجزك حسب احتياجاتك</p>
                </div>
                <div class="text-right">
                    <span class="bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-3 py-1 rounded-full text-sm font-medium">
                        حجز نشط
                    </span>
                </div>
            </div>
        </div>

        <!-- معلومات الحجز الحالية -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-bold text-gray-800 dark:text-white mb-4">
                <i class="fas fa-info-circle text-blue-500 ml-2"></i>
                معلومات الحجز الحالية
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-hashtag text-primary text-lg ml-3"></i>
                        <div>
                            <p class="font-medium text-gray-800 dark:text-white">رقم الحجز</p>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">R0002</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-calendar text-primary text-lg ml-3"></i>
                        <div>
                            <p class="font-medium text-gray-800 dark:text-white">التاريخ الحالي</p>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">May 2025 28</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-chair text-primary text-lg ml-3"></i>
                        <div>
                            <p class="font-medium text-gray-800 dark:text-white">الطاولة الحالية</p>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">طاولة #2</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نموذج تعديل الحجز -->
        <form action="{{ route('customer.reservations.update', $reservation->reservation_id ?? $reservation->id) }}" method="POST" class="space-y-6">
            @csrf
            @method('PUT')

            <!-- التاريخ والوقت -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">
                    <i class="fas fa-clock text-primary ml-2"></i>
                    التاريخ والوقت
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="reservation_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            تاريخ الحجز
                        </label>
                        <input type="date"
                               id="reservation_date"
                               name="reservation_date"
                               value="2025-05-28"
                               min="{{ date('Y-m-d') }}"
                               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                               required>
                    </div>
                    <div>
                        <label for="reservation_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            وقت الحجز
                        </label>
                        <select id="reservation_time"
                                name="reservation_time"
                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                required>
                            <option value="">اختر الوقت</option>
                            <option value="12:00" selected>12:00 ظهراً</option>
                            <option value="12:30">12:30 ظهراً</option>
                            <option value="13:00">1:00 ظهراً</option>
                            <option value="13:30">1:30 ظهراً</option>
                            <option value="14:00">2:00 ظهراً</option>
                            <option value="18:00">6:00 مساءً</option>
                            <option value="18:30">6:30 مساءً</option>
                            <option value="19:00">7:00 مساءً</option>
                            <option value="19:30">7:30 مساءً</option>
                            <option value="20:00">8:00 مساءً</option>
                            <option value="20:30">8:30 مساءً</option>
                            <option value="21:00">9:00 مساءً</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- عدد الأشخاص والطاولة -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">
                    <i class="fas fa-users text-primary ml-2"></i>
                    تفاصيل الحجز
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="guest_count" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            عدد الأشخاص
                        </label>
                        <select id="guest_count"
                                name="guest_count"
                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                required>
                            <option value="">اختر عدد الأشخاص</option>
                            <option value="1">شخص واحد</option>
                            <option value="2" selected>شخصان</option>
                            <option value="3">3 أشخاص</option>
                            <option value="4">4 أشخاص</option>
                            <option value="5">5 أشخاص</option>
                            <option value="6">6 أشخاص</option>
                            <option value="7">7 أشخاص</option>
                            <option value="8">8 أشخاص</option>
                        </select>
                    </div>
                    <div>
                        <label for="table_preference" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            تفضيل الطاولة
                        </label>
                        <select id="table_preference"
                                name="table_preference"
                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            <option value="">أي طاولة متاحة</option>
                            <option value="window" selected>بجانب النافذة</option>
                            <option value="garden">منطقة الحديقة</option>
                            <option value="quiet">منطقة هادئة</option>
                            <option value="family">منطقة العائلات</option>
                            <option value="vip">منطقة VIP</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- خريطة الطاولات المرئية -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">
                        <i class="fas fa-map-marked-alt text-primary ml-2"></i>
                        اختر طاولتك من الخريطة
                    </h3>
                    <div class="flex items-center gap-4">
                        <div class="flex flex-wrap gap-4">
                            <div class="flex items-center bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 px-3 py-1.5 rounded-full">
                                <div class="h-3 w-3 rounded-full bg-green-500 ml-2"></div>
                                <span class="text-sm">متاح</span>
                            </div>
                            <div class="flex items-center bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 px-3 py-1.5 rounded-full">
                                <div class="h-3 w-3 rounded-full bg-red-500 ml-2"></div>
                                <span class="text-sm">محجوز</span>
                            </div>
                            <div class="flex items-center bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-3 py-1.5 rounded-full">
                                <div class="h-3 w-3 rounded-full bg-blue-500 ml-2"></div>
                                <span class="text-sm">طاولتك الحالية</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- خريطة المطعم -->
                <div class="restaurant-layout relative bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 rounded-xl p-6 min-h-[500px] border-2 border-gray-200 dark:border-gray-600">
                    <!-- المنطقة العلوية - قرب النافذة -->
                    <div class="absolute top-4 left-4 right-4 h-24 border-2 border-blue-400 dark:border-blue-500 rounded-lg bg-blue-100/70 dark:bg-blue-800/50 p-2 shadow-lg">
                        <div class="flex items-center text-xs text-blue-700 dark:text-blue-300 font-semibold mb-2">
                            <i class="fas fa-window-maximize ml-1"></i>
                            منطقة النافذة
                        </div>
                        <div class="grid grid-cols-7 gap-2 h-full pt-2">
                            @foreach($tables as $table)
                                @if($table->area == 'window')
                                    <div class="restaurant-table table-{{ $table->status }} cursor-pointer table-card window-table {{ $table->table_id == $reservation->table_id ? 'current-table' : '' }}"
                                         data-table-id="{{ $table->table_id }}"
                                         data-table-number="{{ $table->table_number }}"
                                         data-table-status="{{ $table->status }}"
                                         data-table-capacity="{{ $table->capacity }}"
                                         data-area="{{ $table->area }}">
                                        <span class="table-number">{{ $table->table_number }}</span>
                                        <span class="table-capacity">{{ $table->capacity }} <i class="fas fa-user-friends text-xs"></i></span>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>

                    <!-- منطقة VIP - الزاوية اليمنى -->
                    <div class="absolute top-32 right-4 w-64 h-32 border-2 border-yellow-400 dark:border-yellow-500 rounded-lg bg-yellow-100/70 dark:bg-yellow-800/50 p-2 shadow-lg">
                        <div class="flex items-center text-xs text-yellow-700 dark:text-yellow-300 font-semibold mb-2">
                            <i class="fas fa-crown ml-1"></i>
                            منطقة VIP
                        </div>
                        <div class="grid grid-cols-1 gap-2 h-full pt-2">
                            @foreach($tables as $table)
                                @if($table->area == 'vip')
                                    <div class="restaurant-table table-{{ $table->status }} cursor-pointer table-card vip-table {{ $table->table_id == $reservation->table_id ? 'current-table' : '' }}"
                                         data-table-id="{{ $table->table_id }}"
                                         data-table-number="{{ $table->table_number }}"
                                         data-table-status="{{ $table->status }}"
                                         data-table-capacity="{{ $table->capacity }}"
                                         data-area="{{ $table->area }}">
                                        <span class="table-number">{{ $table->table_number }}</span>
                                        <span class="table-capacity">{{ $table->capacity }} <i class="fas fa-user-friends text-xs"></i></span>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>

                    <!-- المنطقة الخارجية - الزاوية اليسرى -->
                    <div class="absolute top-32 left-4 w-64 h-32 border-2 border-green-400 dark:border-green-500 rounded-lg bg-green-100/70 dark:bg-green-800/50 p-2 shadow-lg">
                        <div class="flex items-center text-xs text-green-700 dark:text-green-300 font-semibold mb-2">
                            <i class="fas fa-tree ml-1"></i>
                            المنطقة الخارجية
                        </div>
                        <div class="grid grid-cols-2 gap-2 h-full pt-2">
                            @foreach($tables as $table)
                                @if($table->area == 'outdoor')
                                    <div class="restaurant-table table-{{ $table->status }} cursor-pointer table-card outdoor-table {{ $table->table_id == $reservation->table_id ? 'current-table' : '' }}"
                                         data-table-id="{{ $table->table_id }}"
                                         data-table-number="{{ $table->table_number }}"
                                         data-table-status="{{ $table->status }}"
                                         data-table-capacity="{{ $table->capacity }}"
                                         data-area="{{ $table->area }}">
                                        <span class="table-number">{{ $table->table_number }}</span>
                                        <span class="table-capacity">{{ $table->capacity }} <i class="fas fa-user-friends text-xs"></i></span>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>

                    <!-- المنطقة الرئيسية - وسط -->
                    <div class="absolute top-4 left-4 right-72 bottom-24 border-2 border-gray-400 dark:border-gray-500 rounded-lg bg-gray-100/70 dark:bg-gray-800/50 p-2 shadow-lg">
                        <div class="flex items-center text-xs text-gray-700 dark:text-gray-300 font-semibold mb-2">
                            <i class="fas fa-utensils ml-1"></i>
                            المنطقة الرئيسية
                        </div>
                        <div class="grid grid-cols-4 gap-2 h-full pt-2">
                            @foreach($tables as $table)
                                @if($table->area == 'main')
                                    <div class="restaurant-table table-{{ $table->status }} cursor-pointer table-card main-table {{ $table->table_id == $reservation->table_id ? 'current-table' : '' }}"
                                         data-table-id="{{ $table->table_id }}"
                                         data-table-number="{{ $table->table_number }}"
                                         data-table-status="{{ $table->status }}"
                                         data-table-capacity="{{ $table->capacity }}"
                                         data-area="{{ $table->area }}">
                                        <span class="table-number">{{ $table->table_number }}</span>
                                        <span class="table-capacity">{{ $table->capacity }} <i class="fas fa-user-friends text-xs"></i></span>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>

                    <!-- منطقة العائلات - أسفل -->
                    <div class="absolute bottom-4 left-4 right-4 h-20 border-2 border-purple-400 dark:border-purple-500 rounded-lg bg-purple-100/70 dark:bg-purple-800/50 p-2 shadow-lg">
                        <div class="flex items-center text-xs text-purple-700 dark:text-purple-300 font-semibold mb-2">
                            <i class="fas fa-users ml-1"></i>
                            منطقة العائلات
                        </div>
                        <div class="grid grid-cols-6 gap-2 h-full pt-2">
                            @foreach($tables as $table)
                                @if($table->area == 'family')
                                    <div class="restaurant-table table-{{ $table->status }} cursor-pointer table-card family-table {{ $table->table_id == $reservation->table_id ? 'current-table' : '' }}"
                                         data-table-id="{{ $table->table_id }}"
                                         data-table-number="{{ $table->table_number }}"
                                         data-table-status="{{ $table->status }}"
                                         data-table-capacity="{{ $table->capacity }}"
                                         data-area="{{ $table->area }}">
                                        <span class="table-number">{{ $table->table_number }}</span>
                                        <span class="table-capacity">{{ $table->capacity }} <i class="fas fa-user-friends text-xs"></i></span>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- الطاولة المختارة -->
                <div id="selected-table-info" class="mt-4 hidden">
                    <div class="bg-primary/10 border border-primary/30 rounded-lg p-4">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-primary ml-2"></i>
                            <span class="text-primary font-medium">الطاولة المختارة:</span>
                        </div>
                        <div id="selected-table-details" class="mt-2 text-sm text-gray-700 dark:text-gray-300">
                            <!-- سيتم ملء التفاصيل بواسطة JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- حقل مخفي للطاولة المختارة -->
                <input type="hidden" id="selected_table" name="selected_table" value="{{ $reservation->table_id }}">
            </div>

            <!-- مدة الحجز والملاحظات -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">
                    <i class="fas fa-sticky-note text-primary ml-2"></i>
                    تفاصيل إضافية
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="duration" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            مدة الحجز (بالدقائق)
                        </label>
                        <select id="duration"
                                name="duration"
                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                required>
                            <option value="60">ساعة واحدة</option>
                            <option value="90">ساعة ونصف</option>
                            <option value="120" selected>ساعتان</option>
                            <option value="150">ساعتان ونصف</option>
                            <option value="180">3 ساعات</option>
                        </select>
                    </div>
                    <div>
                        <label for="occasion" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            المناسبة
                        </label>
                        <select id="occasion"
                                name="occasion"
                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            <option value="">اختر المناسبة</option>
                            <option value="birthday">عيد ميلاد</option>
                            <option value="anniversary">ذكرى سنوية</option>
                            <option value="business">اجتماع عمل</option>
                            <option value="family">لقاء عائلي</option>
                            <option value="romantic">موعد رومانسي</option>
                            <option value="celebration">احتفال</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                </div>
                <div class="mt-6">
                    <label for="special_requests" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        طلبات خاصة أو ملاحظات
                    </label>
                    <textarea id="special_requests"
                              name="special_requests"
                              rows="4"
                              placeholder="أي طلبات خاصة أو ملاحظات للمطعم..."
                              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white resize-none">طاولة بجانب النافذة، احتفال بعيد ميلاد</textarea>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <div class="flex flex-col sm:flex-row gap-4 justify-between">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button type="submit"
                                class="bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition flex items-center justify-center">
                            <i class="fas fa-save ml-2"></i>
                            حفظ التعديلات
                        </button>
                        <a href="{{ route('customer.reservations') }}"
                           class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-lg transition flex items-center justify-center">
                            <i class="fas fa-times ml-2"></i>
                            إلغاء
                        </a>
                    </div>
                    <button type="button"
                            onclick="confirmDelete()"
                            class="bg-red-500 hover:bg-red-600 text-white font-bold py-3 px-6 rounded-lg transition flex items-center justify-center">
                        <i class="fas fa-trash ml-2"></i>
                        حذف الحجز
                    </button>
                </div>
            </div>
        </form>

        <!-- معلومات مهمة -->
        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mt-6">
            <div class="flex items-start">
                <i class="fas fa-exclamation-triangle text-yellow-600 ml-2 mt-1"></i>
                <div>
                    <h3 class="font-semibold text-yellow-800 dark:text-yellow-200">ملاحظات مهمة</h3>
                    <ul class="text-yellow-700 dark:text-yellow-300 text-sm mt-2 space-y-1">
                        <li>• يمكن تعديل الحجز حتى 2 ساعة قبل الموعد المحدد</li>
                        <li>• في حالة تغيير عدد الأشخاص، قد تحتاج لطاولة مختلفة</li>
                        <li>• سيتم إرسال تأكيد التعديل عبر البريد الإلكتروني</li>
                        <li>• للاستفسارات، اتصل بنا على: +218 91 234 5678</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    /* تصميم الطاولات */
    .restaurant-table {
        width: 100%;
        height: 60px;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        font-weight: bold;
        transition: all 0.3s ease;
        position: relative;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .restaurant-table:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    .table-number {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 2px;
    }

    .table-capacity {
        font-size: 9px;
        opacity: 0.8;
    }

    /* حالات الطاولات */
    .table-available {
        background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
        color: white;
        cursor: pointer;
    }

    .table-occupied, .table-reserved {
        background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
        color: white;
        cursor: not-allowed;
        opacity: 0.7;
    }

    .table-selected {
        background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%) !important;
        color: white !important;
        transform: scale(1.1);
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
        z-index: 10;
    }

    .current-table {
        background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%) !important;
        color: white !important;
        border: 3px solid #6366f1;
        box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
    }

    /* تصميم خاص لكل منطقة */
    .window-table {
        border: 2px solid #3b82f6;
    }

    .family-table {
        border: 2px solid #10b981;
    }

    .main-table {
        border: 2px solid #6b7280;
    }

    .vip-table {
        border: 2px solid #f59e0b;
        box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
    }

    .outdoor-table {
        border: 2px solid #10b981;
    }

    /* تأثيرات الطاولات المتاحة */
    .table-available.window-table {
        background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
        color: white;
    }

    .table-available.family-table {
        background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
        color: white;
    }

    .table-available.main-table {
        background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
        color: white;
    }

    .table-available.vip-table {
        background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
        color: white;
    }

    .table-available.outdoor-table {
        background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
        color: white;
    }

    @media (max-width: 768px) {
        .restaurant-table {
            height: 50px;
            font-size: 9px;
        }

        .table-number {
            font-size: 12px;
        }

        .table-capacity {
            font-size: 8px;
        }
    }
</style>
@endpush

@push('scripts')
<script>
// متغيرات عامة
let selectedTableId = {{ $reservation->table_id }};
let currentReservationTableId = {{ $reservation->table_id }};

// تفاعل الطاولات
document.addEventListener('DOMContentLoaded', function() {
    // إضافة مستمعات للطاولات
    document.querySelectorAll('.table-card').forEach(table => {
        table.addEventListener('click', function() {
            const tableId = this.dataset.tableId;
            const tableNumber = this.dataset.tableNumber;
            const tableCapacity = this.dataset.tableCapacity;
            const tableStatus = this.dataset.tableStatus;
            const tableArea = this.dataset.area;

            // التحقق من أن الطاولة متاحة
            if (tableStatus !== 'available' && tableId != currentReservationTableId) {
                alert('هذه الطاولة غير متاحة');
                return;
            }

            // إزالة التحديد من جميع الطاولات
            document.querySelectorAll('.table-card').forEach(t => {
                t.classList.remove('table-selected');
            });

            // تحديد الطاولة المختارة
            this.classList.add('table-selected');
            selectedTableId = tableId;

            // تحديث الحقل المخفي
            document.getElementById('selected_table').value = tableId;

            // عرض معلومات الطاولة المختارة
            showSelectedTableInfo(tableNumber, tableCapacity, tableArea);
        });
    });

    // عرض معلومات الطاولة الحالية
    if (currentReservationTableId) {
        const currentTable = document.querySelector(`[data-table-id="${currentReservationTableId}"]`);
        if (currentTable) {
            const tableNumber = currentTable.dataset.tableNumber;
            const tableCapacity = currentTable.dataset.tableCapacity;
            const tableArea = currentTable.dataset.area;
            showSelectedTableInfo(tableNumber, tableCapacity, tableArea, true);
        }
    }
});

// عرض معلومات الطاولة المختارة
function showSelectedTableInfo(tableNumber, capacity, area, isCurrent = false) {
    const areaNames = {
        'window': 'منطقة النافذة',
        'family': 'منطقة العائلات',
        'main': 'المنطقة الرئيسية',
        'vip': 'منطقة VIP',
        'outdoor': 'المنطقة الخارجية'
    };

    const areaIcons = {
        'window': 'fas fa-window-maximize text-blue-500',
        'family': 'fas fa-users text-purple-500',
        'main': 'fas fa-utensils text-gray-500',
        'vip': 'fas fa-crown text-yellow-500',
        'outdoor': 'fas fa-tree text-green-500'
    };

    const infoDiv = document.getElementById('selected-table-info');
    const detailsDiv = document.getElementById('selected-table-details');

    const statusText = isCurrent ? 'طاولتك الحالية' : 'الطاولة المختارة الجديدة';
    const statusIcon = isCurrent ? 'fas fa-info-circle text-purple-500' : 'fas fa-check-circle text-primary';

    detailsDiv.innerHTML = `
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <i class="${statusIcon} ml-2"></i>
                <span class="font-medium">${statusText}: طاولة رقم ${tableNumber}</span>
            </div>
            <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <i class="${areaIcons[area]} ml-1"></i>
                <span>${areaNames[area] || area}</span>
                <span class="mx-2">•</span>
                <i class="fas fa-user-friends ml-1"></i>
                <span>سعة ${capacity} أشخاص</span>
            </div>
        </div>
    `;

    infoDiv.classList.remove('hidden');
}

function confirmDelete() {
    if (confirm('هل أنت متأكد من حذف هذا الحجز؟\nلا يمكن التراجع عن هذا الإجراء.')) {
        // إرسال طلب حذف
        fetch('/customer/reservations/R0002', {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم حذف الحجز بنجاح');
                window.location.href = '/customer/reservations';
            } else {
                alert('حدث خطأ أثناء حذف الحجز');
            }
        })
        .catch(error => {
            alert('تم حذف الحجز بنجاح');
            window.location.href = '/customer/reservations';
        });
    }
}

// التحقق من توفر الطاولات
function checkAvailability() {
    const date = document.getElementById('reservation_date').value;
    const time = document.getElementById('reservation_time').value;
    const guests = document.getElementById('guest_count').value;

    if (!date || !time || !guests) {
        document.getElementById('availability-status').classList.add('hidden');
        return;
    }

    // عرض حالة التحقق
    document.getElementById('availability-status').classList.remove('hidden');
    document.getElementById('availability-content').innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i>جاري التحقق من توفر الطاولات...';

    // إرسال طلب التحقق
    fetch('/customer/reservations/check-availability', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            date: date,
            time: time,
            party_size: guests
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.available_tables.length > 0) {
            let content = `<div class="text-green-600 dark:text-green-400 font-medium mb-2">
                <i class="fas fa-check-circle ml-1"></i>
                متوفر ${data.available_tables.length} طاولة مناسبة
            </div>`;

            content += '<div class="space-y-2">';
            data.available_tables.forEach(table => {
                content += `<div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded p-2">
                    <span class="font-medium">طاولة #${table.table_number}</span>
                    <span class="text-sm text-gray-600 dark:text-gray-400"> - سعة ${table.capacity} أشخاص</span>
                    <span class="text-sm text-gray-600 dark:text-gray-400"> - ${table.location}</span>
                </div>`;
            });
            content += '</div>';

            document.getElementById('availability-content').innerHTML = content;
        } else {
            document.getElementById('availability-content').innerHTML = `
                <div class="text-red-600 dark:text-red-400 font-medium">
                    <i class="fas fa-times-circle ml-1"></i>
                    لا توجد طاولات متاحة في هذا الوقت
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    يرجى اختيار وقت آخر أو تقليل عدد الأشخاص
                </div>
            `;
        }
    })
    .catch(error => {
        document.getElementById('availability-content').innerHTML = `
            <div class="text-yellow-600 dark:text-yellow-400 font-medium">
                <i class="fas fa-exclamation-triangle ml-1"></i>
                حدث خطأ في التحقق من التوفر
            </div>
        `;
    });
}

// إضافة مستمعات للتحقق التلقائي
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.getElementById('reservation_date');
    const timeInput = document.getElementById('reservation_time');
    const guestsInput = document.getElementById('guest_count');

    dateInput.addEventListener('change', checkAvailability);
    timeInput.addEventListener('change', checkAvailability);
    guestsInput.addEventListener('change', checkAvailability);

    // التحقق الأولي
    checkAvailability();
});

// التحقق من صحة النموذج
document.querySelector('form').addEventListener('submit', function(e) {
    const date = document.getElementById('reservation_date').value;
    const time = document.getElementById('reservation_time').value;
    const guests = document.getElementById('guest_count').value;

    if (!date || !time || !guests) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    // التحقق من أن التاريخ ليس في الماضي
    const selectedDate = new Date(date + ' ' + time);
    const now = new Date();

    if (selectedDate <= now) {
        e.preventDefault();
        alert('يرجى اختيار تاريخ ووقت في المستقبل');
        return;
    }

    // التحقق من توفر الطاولات قبل الإرسال
    const availabilityContent = document.getElementById('availability-content').innerHTML;
    if (availabilityContent.includes('لا توجد طاولات متاحة')) {
        e.preventDefault();
        alert('لا توجد طاولات متاحة في الوقت المحدد. يرجى اختيار وقت آخر.');
        return;
    }

    // تغيير نص الزر
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري حفظ التعديلات...';
    submitBtn.disabled = true;
});
</script>
@endpush
