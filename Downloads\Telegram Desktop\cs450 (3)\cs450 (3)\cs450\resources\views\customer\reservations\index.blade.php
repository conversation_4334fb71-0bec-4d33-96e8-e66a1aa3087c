@extends('customer.layouts.simple')

@section('title', 'حجوزاتي - Eat Hub')

@section('content')

<!-- المحتوى الرئيسي -->
<main class="min-h-screen pt-20">
    <div class="container mx-auto px-4 py-8">
        <!-- عنوان الصفحة -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2">حجوزاتي</h1>
                <p class="text-gray-600 dark:text-gray-400">إدارة جميع حجوزاتك في مكان واحد</p>
            </div>
            <a href="{{ route('customer.reservations.create') }}"
               class="bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition">
                <i class="fas fa-plus ml-2"></i>
                حجز جديد
            </a>
        </div>

        <!-- قائمة الحجوزات -->
        @if($reservations->count() > 0)
        <div class="grid gap-6">
            @foreach($reservations as $reservation)
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white ml-3">
                                حجز رقم #{{ $reservation->reservation_id }}
                            </h3>
                            <span class="px-3 py-1 rounded-full text-sm font-medium
                                @if($reservation->status == 'confirmed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                @elseif($reservation->status == 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                @elseif($reservation->status == 'canceled') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                                @else bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 @endif">
                                @if($reservation->status == 'confirmed') مؤكد
                                @elseif($reservation->status == 'pending') في الانتظار
                                @elseif($reservation->status == 'canceled') ملغي
                                @else {{ $reservation->status }} @endif
                            </span>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 dark:text-gray-400">
                            <div class="flex items-center">
                                <i class="fas fa-calendar-alt text-primary ml-2"></i>
                                <span>{{ \Carbon\Carbon::parse($reservation->reservation_time)->format('Y-m-d') }}</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-clock text-primary ml-2"></i>
                                <span>{{ \Carbon\Carbon::parse($reservation->reservation_time)->format('H:i') }}</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-users text-primary ml-2"></i>
                                <span>{{ $reservation->party_size ?? 'غير محدد' }} أشخاص</span>
                            </div>
                        </div>

                        @if($reservation->table)
                        <div class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                            <i class="fas fa-chair text-primary ml-2"></i>
                            طاولة رقم {{ $reservation->table->table_number }}
                        </div>
                        @endif

                        @if($reservation->offer_title)
                        <div class="mt-3 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-tag text-green-600 ml-2"></i>
                                <span class="text-sm font-medium text-green-800 dark:text-green-200">
                                    حجز من عرض: {{ $reservation->offer_title }}
                                </span>
                            </div>
                            @if($reservation->contact_phone)
                            <div class="mt-1 text-xs text-green-700 dark:text-green-300">
                                <i class="fas fa-phone ml-1"></i>
                                رقم التواصل: {{ $reservation->contact_phone }}
                            </div>
                            @endif
                        </div>
                        @endif

                        @if($reservation->preOrder && $reservation->preOrder->orderItems->count() > 0)
                        <div class="mt-3 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center">
                                    <i class="fas fa-utensils text-orange-600 ml-2"></i>
                                    <span class="text-sm font-medium text-orange-800 dark:text-orange-200">
                                        طلب طعام مسبق
                                    </span>
                                </div>
                                <span class="px-2 py-1 rounded-full text-xs font-medium
                                    {{ $reservation->preOrder->status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                       ($reservation->preOrder->status === 'preparing' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' :
                                        'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300') }}">
                                    {{ $reservation->preOrder->status === 'pending' ? 'في الانتظار' :
                                       ($reservation->preOrder->status === 'preparing' ? 'قيد التحضير' : 'جاهز') }}
                                </span>
                            </div>
                            <div class="text-xs text-orange-700 dark:text-orange-300">
                                @php
                                    $items = $reservation->preOrder->orderItems->take(3);
                                    $itemsList = $items->map(function($item) {
                                        return $item->menuItem->name . ' (×' . $item->quantity . ')';
                                    })->join(', ');
                                    if ($reservation->preOrder->orderItems->count() > 3) {
                                        $itemsList .= ' و ' . ($reservation->preOrder->orderItems->count() - 3) . ' عنصر آخر';
                                    }
                                @endphp
                                <i class="fas fa-list ml-1"></i>
                                {{ $itemsList }}
                            </div>
                            <div class="mt-1 text-xs text-orange-700 dark:text-orange-300">
                                <i class="fas fa-money-bill ml-1"></i>
                                إجمالي: {{ number_format($reservation->preOrder->total_amount, 2) }} د.ل
                            </div>
                        </div>
                        @endif

                        @if($reservation->special_requests)
                        <div class="mt-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                <strong>ملاحظات خاصة:</strong> {{ $reservation->special_requests }}
                            </p>
                        </div>
                        @endif
                    </div>

                    <div class="mt-4 md:mt-0 md:mr-6 flex flex-col sm:flex-row gap-2">
                        <!-- طباعة تفاصيل الحجز -->
                        <button onclick="printReservation({{ $reservation->reservation_id }})"
                                class="w-full sm:w-auto bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition shadow-md hover:shadow-lg"
                                title="طباعة تفاصيل الحجز">
                            <i class="fas fa-print ml-1"></i>
                            طباعة
                        </button>

                        <!-- إرسال تذكير -->
                        @if($reservation->status == 'confirmed' && \Carbon\Carbon::parse($reservation->reservation_time)->isFuture())
                        <button onclick="sendReminder({{ $reservation->reservation_id }})"
                                class="w-full sm:w-auto bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-lg transition shadow-md hover:shadow-lg"
                                title="إرسال تذكير بالحجز">
                            <i class="fas fa-bell ml-1"></i>
                            تذكير
                        </button>
                        @endif

                        @if($reservation->status == 'pending' || $reservation->status == 'confirmed')
                            @if(\Carbon\Carbon::parse($reservation->reservation_time)->isFuture())
                            <form action="{{ route('customer.reservations.cancel', $reservation->reservation_id) }}"
                                  method="POST"
                                  onsubmit="return confirm('هل أنت متأكد من إلغاء هذا الحجز؟')">
                                @csrf
                                @method('DELETE')
                                <button type="submit"
                                        class="w-full sm:w-auto bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg transition">
                                    <i class="fas fa-times ml-1"></i>
                                    إلغاء الحجز
                                </button>
                            </form>
                            @endif
                        @endif

                        <a href="{{ route('customer.reservations.show', $reservation->reservation_id) }}"
                           class="w-full sm:w-auto bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition text-center">
                            <i class="fas fa-eye ml-1"></i>
                            عرض التفاصيل
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="mt-8">
            {{ $reservations->links() }}
        </div>

        @else
        <!-- حالة عدم وجود حجوزات -->
        <div class="text-center py-12">
            <div class="max-w-md mx-auto">
                <i class="fas fa-calendar-times text-6xl text-gray-300 dark:text-gray-600 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">لا توجد حجوزات</h3>
                <p class="text-gray-500 dark:text-gray-500 mb-6">لم تقم بأي حجوزات بعد. احجز طاولتك الآن!</p>
                <a href="{{ route('customer.reservations.create') }}"
                   class="inline-block bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition">
                    <i class="fas fa-plus ml-2"></i>
                    احجز طاولة الآن
                </a>
            </div>
        </div>
        @endif
    </div>
@endsection

<!-- Modal لطباعة الحجز -->
<div id="printModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-bold text-gray-900 dark:text-white">تفاصيل الحجز للطباعة</h3>
                <button onclick="closePrintModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="printContent" class="mt-4">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
            <div class="flex justify-end space-x-3 space-x-reverse mt-6">
                <button onclick="closePrintModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500">
                    إغلاق
                </button>
                <button onclick="printReservationDetails()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    <i class="fas fa-print ml-2"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// طباعة تفاصيل الحجز
async function printReservation(reservationId) {
    try {
        // إظهار المودال
        document.getElementById('printModal').classList.remove('hidden');

        // إظهار رسالة تحميل
        document.getElementById('printContent').innerHTML = `
            <div class="text-center py-8">
                <i class="fas fa-spinner fa-spin text-blue-500 text-2xl mb-4"></i>
                <p class="text-gray-600 dark:text-gray-400">جاري تحميل تفاصيل الحجز...</p>
            </div>
        `;

        // تحميل تفاصيل الحجز
        const response = await fetch(`/customer/reservations/${reservationId}/print`);
        const data = await response.json();

        if (data.success) {
            document.getElementById('printContent').innerHTML = data.html;
        } else {
            document.getElementById('printContent').innerHTML = `
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-triangle text-red-500 text-2xl mb-4"></i>
                    <p class="text-red-600 dark:text-red-400">حدث خطأ في تحميل تفاصيل الحجز</p>
                    <button onclick="printReservation(${reservationId})" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition">
                        <i class="fas fa-redo ml-2"></i>
                        إعادة المحاولة
                    </button>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error:', error);
        document.getElementById('printContent').innerHTML = `
            <div class="text-center py-8">
                <i class="fas fa-exclamation-triangle text-red-500 text-2xl mb-4"></i>
                <p class="text-red-600 dark:text-red-400">حدث خطأ في الاتصال</p>
                <button onclick="printReservation(${reservationId})" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition">
                    <i class="fas fa-redo ml-2"></i>
                    إعادة المحاولة
                </button>
            </div>
        `;
    }
}

// طباعة المحتوى
function printReservationDetails() {
    const printContent = document.getElementById('printContent').innerHTML;
    const originalContent = document.body.innerHTML;

    document.body.innerHTML = printContent;
    window.print();
    document.body.innerHTML = originalContent;

    // إعادة تحميل الصفحة لاستعادة الوظائف
    location.reload();
}

// إغلاق مودال الطباعة
function closePrintModal() {
    document.getElementById('printModal').classList.add('hidden');
}

// إرسال تذكير
async function sendReminder(reservationId) {
    // تأكيد من المستخدم
    if (!confirm('هل تريد إرسال تذكير بهذا الحجز؟\nسيتم إرسال إشعار تذكير لك وللمطعم.')) {
        return;
    }

    try {
        // إظهار رسالة تحميل
        showNotification('جاري إرسال التذكير...', 'info');

        const response = await fetch(`/customer/reservations/${reservationId}/reminder`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            // إظهار رسالة نجاح
            showNotification('✅ تم إرسال التذكير بنجاح! ستتلقى إشعاراً في قائمة الإشعارات.', 'success');
        } else {
            showNotification(data.message || 'حدث خطأ في إرسال التذكير', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('حدث خطأ في الاتصال', 'error');
    }
}

// إظهار الإشعارات
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

    if (type === 'success') {
        notification.className += ' bg-green-500 text-white';
    } else if (type === 'error') {
        notification.className += ' bg-red-500 text-white';
    } else {
        notification.className += ' bg-blue-500 text-white';
    }

    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info'} ml-2"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="mr-4 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // إظهار الإشعار
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // إخفاء الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

// إغلاق المودال عند النقر خارجه
document.getElementById('printModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closePrintModal();
    }
});
</script>
