<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id('order_id');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('table_id')->nullable()->constrained('tables')->onDelete('set null');
            $table->decimal('total_amount', 10, 2)->default(0);
            $table->enum('status', ['pending', 'preparing', 'completed', 'canceled'])->default('pending');
            $table->timestamps();
        });

        // تأجيل إضافة الـ CONSTRAINT حتى يتم إنشاء الجدول
        DB::statement('ALTER TABLE orders ADD CONSTRAINT orders_total_amount_check CHECK (total_amount >= 0)');
    }

    public function down()
    {
        Schema::dropIfExists('orders');
    }
};