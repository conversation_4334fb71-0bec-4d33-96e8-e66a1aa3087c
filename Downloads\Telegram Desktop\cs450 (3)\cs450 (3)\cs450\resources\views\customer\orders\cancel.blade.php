@extends('customer.layouts.app')

@section('title', 'إلغاء الطلب - Eat Hub')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- شريط التنقل السريع -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
            <div class="flex flex-wrap gap-2 justify-center md:justify-start">
                <a href="{{ route('customer.dashboard') }}" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white px-4 py-2 rounded-lg text-sm transition">
                    <i class="fas fa-tachometer-alt ml-1"></i>لوحة التحكم
                </a>
                <a href="{{ route('customer.orders') }}" class="bg-primary text-white px-4 py-2 rounded-lg text-sm transition">
                    <i class="fas fa-shopping-bag ml-1"></i>طلباتي
                </a>
                <span class="bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 px-4 py-2 rounded-lg text-sm">
                    <i class="fas fa-times ml-1"></i>إلغاء الطلب
                </span>
            </div>
        </div>

        <!-- عنوان الصفحة -->
        <div class="text-center mb-8">
            <div class="w-20 h-20 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400 text-3xl"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2">إلغاء الطلب</h1>
            <p class="text-gray-600 dark:text-gray-400">هل أنت متأكد من إلغاء هذا الطلب؟</p>
        </div>

        <!-- معلومات الطلب -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">
                <i class="fas fa-info-circle text-blue-500 ml-2"></i>
                معلومات الطلب
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <p class="text-gray-600 dark:text-gray-400 text-sm">رقم الطلب</p>
                    <p class="font-bold text-gray-800 dark:text-white text-lg">{{ $order->order_number ?? 'ORD-0006' }}</p>
                </div>
                <div>
                    <p class="text-gray-600 dark:text-gray-400 text-sm">تاريخ الطلب</p>
                    <p class="font-bold text-gray-800 dark:text-white">{{ $order->created_at ?? 'May 2025 - 10:25 ص' }}</p>
                </div>
                <div>
                    <p class="text-gray-600 dark:text-gray-400 text-sm">إجمالي المبلغ</p>
                    <p class="font-bold text-gray-800 dark:text-white text-lg">{{ number_format($order->total_amount ?? 109.25, 2) }} د.ل</p>
                </div>
                <div>
                    <p class="text-gray-600 dark:text-gray-400 text-sm">حالة الطلب</p>
                    <span class="px-3 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 rounded-full text-sm">قيد التجهيز</span>
                </div>
            </div>
        </div>

        <!-- عناصر الطلب -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">
                <i class="fas fa-utensils text-green-500 ml-2"></i>
                عناصر الطلب
            </h3>
            <div class="space-y-3">
                @if(isset($order->items) && $order->items->count() > 0)
                    @foreach($order->items as $item)
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gray-200 dark:bg-gray-600 rounded-md flex items-center justify-center ml-3">
                                @if($item->menuItem && $item->menuItem->image_path)
                                    <img src="{{ asset('storage/' . $item->menuItem->image_path) }}"
                                         alt="{{ $item->menuItem->name }}"
                                         class="w-full h-full object-cover rounded-md">
                                @else
                                    <i class="fas fa-utensils text-gray-400"></i>
                                @endif
                            </div>
                            <div>
                                <p class="font-medium text-gray-800 dark:text-white">{{ $item->menuItem->name ?? 'بيتزا مارجريتا' }}</p>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">الكمية: {{ $item->quantity ?? 1 }} × {{ number_format($item->price ?? 80.00, 2) }} د.ل</p>
                            </div>
                        </div>
                        <span class="font-medium text-gray-800 dark:text-white">{{ number_format(($item->price ?? 80.00) * ($item->quantity ?? 1), 2) }} د.ل</span>
                    </div>
                    @endforeach
                @else
                    <!-- عناصر افتراضية للعرض -->
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gray-200 dark:bg-gray-600 rounded-md flex items-center justify-center ml-3">
                                <i class="fas fa-utensils text-gray-400"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-800 dark:text-white">بيتزا مارجريتا</p>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">الكمية: 1 × 80.00 د.ل</p>
                            </div>
                        </div>
                        <span class="font-medium text-gray-800 dark:text-white">80.00 د.ل</span>
                    </div>
                @endif
            </div>
        </div>

        <!-- سبب الإلغاء -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">
                <i class="fas fa-question-circle text-orange-500 ml-2"></i>
                سبب إلغاء الطلب (اختياري)
            </h3>
            <form id="cancelOrderForm" action="{{ route('customer.orders.cancel', $order->order_id ?? 1) }}" method="POST">
                @csrf
                @method('DELETE')

                <div class="space-y-3 mb-4">
                    <label class="flex items-center">
                        <input type="radio" name="cancellation_reason" value="changed_mind" class="ml-2">
                        <span class="text-gray-700 dark:text-gray-300">غيرت رأيي</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="cancellation_reason" value="wrong_order" class="ml-2">
                        <span class="text-gray-700 dark:text-gray-300">طلبت بالخطأ</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="cancellation_reason" value="too_long" class="ml-2">
                        <span class="text-gray-700 dark:text-gray-300">وقت التحضير طويل جداً</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="cancellation_reason" value="emergency" class="ml-2">
                        <span class="text-gray-700 dark:text-gray-300">ظرف طارئ</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="cancellation_reason" value="other" class="ml-2">
                        <span class="text-gray-700 dark:text-gray-300">سبب آخر</span>
                    </label>
                </div>

                <div class="mb-6">
                    <label for="cancellation_notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        ملاحظات إضافية
                    </label>
                    <textarea id="cancellation_notes"
                              name="cancellation_notes"
                              rows="3"
                              placeholder="أي ملاحظات إضافية حول سبب الإلغاء..."
                              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"></textarea>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="flex flex-col sm:flex-row gap-4 justify-between">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button type="submit"
                                class="bg-red-500 hover:bg-red-600 text-white font-bold py-3 px-6 rounded-lg transition flex items-center justify-center">
                            <i class="fas fa-times ml-2"></i>
                            تأكيد الإلغاء
                        </button>
                        <a href="{{ route('customer.orders') }}"
                           class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-lg transition flex items-center justify-center">
                            <i class="fas fa-arrow-right ml-2"></i>
                            العودة للطلبات
                        </a>
                    </div>
                    <a href="{{ route('customer.orders.show', $order->order_id ?? 1) }}"
                       class="bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition flex items-center justify-center">
                        <i class="fas fa-eye ml-2"></i>
                        عرض تفاصيل الطلب
                    </a>
                </div>
            </form>
        </div>

        <!-- تحذيرات مهمة -->
        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
            <div class="flex items-start">
                <i class="fas fa-exclamation-triangle text-red-600 ml-2 mt-1"></i>
                <div>
                    <h3 class="font-semibold text-red-800 dark:text-red-200">تحذيرات مهمة</h3>
                    <ul class="text-red-700 dark:text-red-300 text-sm mt-2 space-y-1">
                        <li>• لا يمكن التراجع عن إلغاء الطلب بعد التأكيد</li>
                        <li>• سيتم استرداد المبلغ خلال 3-5 أيام عمل</li>
                        <li>• قد تطبق رسوم إلغاء حسب سياسة المطعم</li>
                        <li>• للاستفسارات، اتصل بنا على: +218 91 234 5678</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- معلومات الاتصال -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div class="flex items-start">
                <i class="fas fa-phone text-blue-600 ml-2 mt-1"></i>
                <div>
                    <h3 class="font-semibold text-blue-800 dark:text-blue-200">هل تحتاج مساعدة؟</h3>
                    <p class="text-blue-700 dark:text-blue-300 text-sm mt-1">
                        يمكنك التواصل معنا قبل إلغاء الطلب. قد نتمكن من حل المشكلة أو تعديل الطلب بدلاً من إلغائه.
                    </p>
                    <div class="mt-2 space-x-4 space-x-reverse">
                        <a href="tel:+218912345678" class="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                            <i class="fas fa-phone ml-1"></i>+218 91 234 5678
                        </a>
                        <a href="mailto:<EMAIL>" class="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                            <i class="fas fa-envelope ml-1"></i><EMAIL>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// إرسال النموذج مباشرة بدون تأكيد
document.getElementById('cancelOrderForm').addEventListener('submit', function(e) {
    // تغيير نص الزر
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري الإلغاء...';
    submitBtn.disabled = true;

    // السماح بإرسال النموذج
    return true;
});
</script>
@endpush
