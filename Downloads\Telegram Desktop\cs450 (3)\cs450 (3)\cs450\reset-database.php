<?php

/**
 * إعادة تعيين قاعدة البيانات
 * يقوم بحذف جميع البيانات وإعادة تشغيل الهجرات والبذور
 */

echo "🔄 إعادة تعيين قاعدة البيانات...\n";
echo "================================\n\n";

// تأكيد من المستخدم
echo "⚠️  تحذير: هذا سيحذف جميع البيانات الموجودة!\n";
echo "هل تريد المتابعة؟ (y/N): ";
$handle = fopen("php://stdin", "r");
$line = fgets($handle);
fclose($handle);

if (trim(strtolower($line)) !== 'y') {
    echo "❌ تم إلغاء العملية\n";
    exit(0);
}

echo "\n🗑️  حذف جميع البيانات...\n";

// مسح الكاش أولاً
echo "🧹 مسح الكاش...\n";
shell_exec('php artisan cache:clear');
shell_exec('php artisan config:clear');
shell_exec('php artisan view:clear');

// إعادة تعيين قاعدة البيانات
echo "🔄 إعادة تعيين قاعدة البيانات...\n";
$output = shell_exec('php artisan migrate:fresh --force 2>&1');

if (strpos($output, 'Migrated:') !== false) {
    echo "✅ تم إعادة تعيين قاعدة البيانات بنجاح\n\n";
} else {
    echo "❌ فشل في إعادة تعيين قاعدة البيانات:\n";
    echo $output . "\n";
    exit(1);
}

// تشغيل البذور
echo "🌱 تشغيل بذور البيانات...\n";
$seedOutput = shell_exec('php artisan db:seed --force 2>&1');

if (strpos($seedOutput, 'Database seeding completed successfully') !== false || 
    strpos($seedOutput, 'Seeding:') !== false) {
    echo "✅ تم تشغيل البذور بنجاح\n\n";
} else {
    echo "⚠️  تحذير: قد تكون هناك مشاكل في البذور:\n";
    echo $seedOutput . "\n\n";
}

// إنشاء رابط التخزين
echo "🔗 إعادة إنشاء رابط التخزين...\n";
shell_exec('php artisan storage:link');
echo "✅ تم إنشاء رابط التخزين\n\n";

echo "🎉 تم إعادة تعيين قاعدة البيانات بنجاح!\n";
echo "================================\n";
echo "📋 معلومات تسجيل الدخول:\n";
echo "   البريد الإلكتروني: <EMAIL>\n";
echo "   كلمة المرور: A178a2002\n";
echo "   النوع: مدير\n\n";
