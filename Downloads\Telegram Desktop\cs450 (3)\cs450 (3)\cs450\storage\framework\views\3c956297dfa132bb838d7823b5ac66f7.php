<header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 z-40 relative">
    <div class="px-6 py-4 flex justify-between items-center">
        <!-- الجانب الأيسر -->
        <div class="flex items-center space-x-4 space-x-reverse">
            <!-- زر فتح/إغلاق القائمة الجانبية للجوال -->
            <button id="sidebarToggle" class="md:hidden p-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                <i class="fas fa-bars text-lg"></i>
            </button>

            <!-- عنوان الصفحة -->
            <div class="hidden md:block">
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                    <?php echo $__env->yieldContent('page-title', 'لوحة التحكم'); ?>
                </h1>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    مرحباً بك في نظام إدارة المطعم
                </p>
            </div>
        </div>

        <!-- الجانب الأيمن -->

        <div class="flex items-center space-x-4 space-x-reverse">
            <!-- زر البحث -->
            <div class="relative">
                <form action="<?php echo e(route('admin.search')); ?>" method="GET" class="hidden md:flex items-center">
                    <div class="relative">
                        <input type="text" name="query" placeholder="بحث في لوحة التحكم..." class="w-64 px-4 py-3 pr-12 rounded-xl bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-300">
                        <button type="submit" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <!-- إضافة حقول مخفية لتحديد نوع البحث -->
                    <input type="hidden" name="search_types[]" value="users">
                    <input type="hidden" name="search_types[]" value="orders">
                    <input type="hidden" name="search_types[]" value="menu">
                    <input type="hidden" name="search_types[]" value="reservations">
                    <input type="hidden" name="search_types[]" value="ingredients">
                    <input type="hidden" name="search_types[]" value="tables">
                    <?php if(Auth::user()->user_type == 'admin'): ?>
                    <input type="hidden" name="search_types[]" value="expenses">
                    <?php endif; ?>
                </form>
                <button id="mobileSearchBtn" class="md:hidden p-3 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group">
                    <i class="fas fa-search group-hover:scale-110 transition-transform"></i>
                </button>

                <!-- نافذة البحث للجوال -->
                <div id="mobileSearchModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50 md:hidden">
                    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-sm w-full mx-4 transform transition-all duration-300 scale-95" id="mobileSearchContent">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-bold text-gray-900 dark:text-white">البحث في لوحة التحكم</h3>
                                <button id="closeMobileSearch" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>
                            <form action="<?php echo e(route('admin.search')); ?>" method="GET">
                                <div class="relative mb-4">
                                    <input type="text" name="query" placeholder="ابحث عن المستخدمين، الطلبات، القائمة..."
                                           class="w-full px-4 py-3 pr-12 rounded-xl bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-300">
                                    <button type="submit" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <!-- إضافة حقول مخفية لتحديد نوع البحث -->
                                <input type="hidden" name="search_types[]" value="users">
                                <input type="hidden" name="search_types[]" value="orders">
                                <input type="hidden" name="search_types[]" value="menu">
                                <input type="hidden" name="search_types[]" value="reservations">
                                <input type="hidden" name="search_types[]" value="ingredients">
                                <input type="hidden" name="search_types[]" value="tables">
                                <?php if(Auth::user()->user_type == 'admin'): ?>
                                <input type="hidden" name="search_types[]" value="expenses">
                                <?php endif; ?>
                                <button type="submit" class="w-full px-4 py-3 bg-gradient-to-r from-orange-500 to-red-600 text-white font-semibold rounded-xl hover:from-orange-600 hover:to-red-700 transition-all duration-300 flex items-center justify-center">
                                    <i class="fas fa-search ml-2"></i>
                                    بحث
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- زر الإشعارات -->
            <div class="relative">
                <button id="notificationButton" class="header-notification-btn relative p-3 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group">
                    <i class="fas fa-bell text-lg header-bell-icon group-hover:scale-110 transition-transform"></i>
                    <span id="notificationCount" class="absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-full text-xs w-5 h-5 flex items-center justify-center font-medium shadow-lg">0</span>
                </button>
                <!-- قائمة الإشعارات - ستظهر عند النقر -->
                <div id="notificationsMenu" class="absolute left-0 top-full mt-3 w-80 bg-white dark:bg-gray-800 rounded-2xl shadow-lg py-2 z-50 hidden border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                        <h3 class="font-bold text-gray-800 dark:text-white">الإشعارات</h3>
                        <a href="<?php echo e(route('admin.notifications')); ?>" class="text-orange-600 dark:text-orange-400 text-sm hover:underline font-medium">عرض الكل</a>
                    </div>
                    <div id="notificationsList" class="max-h-64 overflow-y-auto">
                        <!-- ستتم تعبئة هذا القسم بالإشعارات من خلال JavaScript -->
                        <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                            <i class="fas fa-bell-slash text-3xl mb-3 opacity-50"></i>
                            <p>جاري تحميل الإشعارات...</p>
                        </div>
                        <a href="<?php echo e(route('admin.notifications')); ?>" class="block w-full text-center py-2 text-orange-600 dark:text-orange-400 hover:bg-orange-50 dark:hover:bg-orange-900/20 rounded-md transition-colors">
                            عرض جميع الإشعارات
                        </a>
                    </div>
                </div>
            </div>

            <!-- زر تبديل الثيم - حل نهائي وبسيط -->
            <button id="darkModeToggle" class="theme-toggle p-3 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group" title="تبديل الثيم" onclick="simpleDarkModeToggle()">
                <i id="darkModeIcon" class="theme-icon fas fa-moon group-hover:rotate-180 transition-transform duration-500"></i>
            </button>

            <script>
                // دالة بسيطة جداً لتبديل الوضع المظلم
                function simpleDarkModeToggle() {
                    console.log('🖱️ تم النقر على زر الوضع المظلم');

                    const html = document.documentElement;
                    const icon = document.getElementById('darkModeIcon');

                    if (html.classList.contains('dark')) {
                        // التبديل إلى الوضع الفاتح
                        html.classList.remove('dark');
                        localStorage.setItem('darkMode', 'false');
                        if (icon) {
                            icon.className = 'theme-icon fas fa-moon group-hover:rotate-180 transition-transform duration-500';
                        }
                        console.log('☀️ تم التبديل إلى الوضع الفاتح');
                    } else {
                        // التبديل إلى الوضع المظلم
                        html.classList.add('dark');
                        localStorage.setItem('darkMode', 'true');
                        if (icon) {
                            icon.className = 'theme-icon fas fa-sun group-hover:rotate-180 transition-transform duration-500';
                        }
                        console.log('🌙 تم التبديل إلى الوضع المظلم');
                    }

                    // تحديث المخططات إذا كانت موجودة
                    setTimeout(() => {
                        if (typeof forceUpdateCharts === 'function') {
                            forceUpdateCharts();
                        }
                        if (typeof updateAllCharts === 'function') {
                            updateAllCharts();
                        }
                    }, 100);
                }

                // تحديث الأيقونة عند تحميل الصفحة
                document.addEventListener('DOMContentLoaded', function() {
                    const icon = document.getElementById('darkModeIcon');
                    if (icon) {
                        const isDark = document.documentElement.classList.contains('dark');
                        icon.className = isDark ?
                            'theme-icon fas fa-sun group-hover:rotate-180 transition-transform duration-500' :
                            'theme-icon fas fa-moon group-hover:rotate-180 transition-transform duration-500';
                        console.log('🔄 تم تحديث أيقونة الزر:', isDark ? 'شمس' : 'قمر');
                    }
                });
            </script>

            <!-- صورة المستخدم -->
            <div class="relative">
                <button id="userMenuBtn" class="flex items-center p-2 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group">
                    <div class="w-10 h-10 rounded-full bg-gradient-to-br from-orange-500 to-red-600 flex items-center justify-center text-white font-bold overflow-hidden ring-2 ring-white/20 group-hover:ring-white/40 transition-all duration-300">
                        <?php if(Auth::user()->profile_image): ?>
                            <img src="<?php echo e(asset('storage/' . Auth::user()->profile_image)); ?>" alt="<?php echo e(Auth::user()->first_name); ?>" class="w-full h-full object-cover">
                        <?php else: ?>
                            <?php echo e(substr(Auth::user()->first_name, 0, 1)); ?>

                        <?php endif; ?>
                    </div>
                    <i class="fas fa-chevron-down text-sm mr-2 group-hover:rotate-180 transition-transform duration-300"></i>
                </button>

                <!-- قائمة المستخدم - مخفية افتراضياً -->
                <div id="userMenu" class="absolute left-0 mt-3 w-72 bg-white dark:bg-gray-800 rounded-2xl shadow-lg py-2 z-10 hidden border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full bg-gradient-to-br from-orange-500 to-red-600 flex items-center justify-center text-white font-bold overflow-hidden ring-2 ring-gray-200 dark:ring-gray-600">
                                <?php if(Auth::user()->profile_image): ?>
                                    <img src="<?php echo e(asset('storage/' . Auth::user()->profile_image)); ?>" alt="<?php echo e(Auth::user()->first_name); ?>" class="w-full h-full object-cover">
                                <?php else: ?>
                                    <?php echo e(substr(Auth::user()->first_name, 0, 1)); ?>

                                <?php endif; ?>
                            </div>
                            <div class="mr-3">
                                <p class="text-sm font-bold text-gray-800 dark:text-white"><?php echo e(Auth::user()->first_name); ?> <?php echo e(Auth::user()->last_name); ?></p>
                                <p class="text-xs text-gray-600 dark:text-gray-400 truncate"><?php echo e(Auth::user()->email); ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="py-2">
                        <a href="<?php echo e(route('admin.profile')); ?>" class="flex items-center px-6 py-3 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300 group">
                            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-orange-500 to-red-600 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                                <i class="fas fa-user-circle text-white text-sm"></i>
                            </div>
                            <span class="font-medium">الملف الشخصي</span>
                        </a>
                        <a href="<?php echo e(route('admin.settings')); ?>" class="flex items-center px-6 py-3 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300 group">
                            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-purple-500 to-indigo-600 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                                <i class="fas fa-cog text-white text-sm"></i>
                            </div>
                            <span class="font-medium">الإعدادات</span>
                        </a>

                        <?php if(Auth::user()->user_type == 'admin' || (Auth::user()->user_type == 'employee' && Auth::user()->can('dashboard.admin'))): ?>
                        <div class="border-t border-gray-200 dark:border-gray-700 my-2"></div>
                        <div class="px-6 py-2">
                            <span class="text-xs text-gray-500 dark:text-gray-400 font-medium">التنقل السريع</span>
                        </div>

                        <?php if(Auth::user()->user_type == 'admin'): ?>
                        <a href="<?php echo e(route('employee.dashboard')); ?>" class="flex items-center px-6 py-3 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 group">
                            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                                <i class="fas fa-user-tie text-white text-sm"></i>
                            </div>
                            <div>
                                <span class="font-medium block">واجهة الموظف</span>
                                <span class="text-xs text-gray-500 dark:text-gray-400">عرض النظام كموظف</span>
                            </div>
                        </a>
                        <?php elseif(Auth::user()->user_type == 'employee' && Auth::user()->can('dashboard.admin')): ?>
                        <a href="<?php echo e(route('employee.dashboard')); ?>" class="flex items-center px-6 py-3 text-gray-700 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-300 group">
                            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                                <i class="fas fa-arrow-left text-white text-sm"></i>
                            </div>
                            <div>
                                <span class="font-medium block">العودة لواجهة الموظف</span>
                                <span class="text-xs text-gray-500 dark:text-gray-400">العودة للواجهة الأساسية</span>
                            </div>
                        </a>
                        <?php endif; ?>

                        <a href="<?php echo e(route('customer.index')); ?>" class="flex items-center px-6 py-3 text-gray-700 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-300 group">
                            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                                <i class="fas fa-home text-white text-sm"></i>
                            </div>
                            <div>
                                <span class="font-medium block">الصفحة الرئيسية</span>
                                <span class="text-xs text-gray-500 dark:text-gray-400">عرض الموقع للعملاء</span>
                            </div>
                        </a>
                        <?php endif; ?>

                        <div class="border-t border-gray-200 dark:border-gray-700 my-2"></div>
                        <a href="<?php echo e(route('logout')); ?>" onclick="event.preventDefault(); document.getElementById('logout-form').submit();" class="flex items-center px-6 py-3 text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-300 group">
                            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-red-500 to-pink-500 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                                <i class="fas fa-sign-out-alt text-white text-sm"></i>
                            </div>
                            <span class="font-medium">تسجيل الخروج</span>
                        </a>
                        <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" style="display: none;">
                            <?php echo csrf_field(); ?>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>

<style>
    /* تحسين القوائم المنسدلة */
    .dropdown-menu {
        max-height: 400px;
        overflow-y: auto;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    /* التأكد من أن القوائم تظهر فوق كل شيء */
    #notificationsMenu, #userMenu {
        z-index: 9999 !important;
        position: absolute !important;
        top: 100% !important;
        transform: translateY(0) !important;
        min-width: 200px;
        max-width: 400px;
        backdrop-filter: blur(8px);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    /* تحسين الحاوي للقوائم */
    .relative .absolute {
        position: absolute !important;
    }

    /* إصلاح مشكلة الانقطاع */
    header {
        overflow: visible !important;
    }

    header .relative {
        overflow: visible !important;
    }

    /* تحسين الحاوي النسبي للقوائم */
    .relative {
        position: relative !important;
    }

    /* تأثيرات الهيدر المحسنة */
    .group:hover .group-hover\:scale-110 {
        transform: scale(1.1);
    }

    .group:hover .group-hover\:rotate-180 {
        transform: rotate(180deg);
    }

    /* تحسين الانتقالات */
    .transition-all {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* تحسين الظلال */
    .shadow-lg {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    /* تحسين التدرجات */
    .bg-gradient-to-br {
        background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
    }

    /* تحسين الحلقات */
    .ring-2 {
        box-shadow: 0 0 0 2px var(--tw-ring-color);
    }

    /* تحسين الأزرار */
    button:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
    }

    /* تحسين الإدخال */
    input:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mobile Search Modal
        const mobileSearchBtn = document.getElementById('mobileSearchBtn');
        const mobileSearchModal = document.getElementById('mobileSearchModal');
        const mobileSearchContent = document.getElementById('mobileSearchContent');
        const closeMobileSearch = document.getElementById('closeMobileSearch');

        // Show mobile search modal
        if (mobileSearchBtn) {
            mobileSearchBtn.addEventListener('click', function() {
                mobileSearchModal.classList.remove('hidden');
                mobileSearchModal.classList.add('flex');
                setTimeout(() => {
                    mobileSearchContent.classList.remove('scale-95');
                    mobileSearchContent.classList.add('scale-100');
                }, 10);
                // Focus on input
                const searchInput = mobileSearchModal.querySelector('input[name="query"]');
                if (searchInput) {
                    setTimeout(() => searchInput.focus(), 100);
                }
            });
        }

        // Close mobile search modal
        if (closeMobileSearch) {
            closeMobileSearch.addEventListener('click', closeMobileSearchModal);
        }

        // Close modal when clicking outside
        if (mobileSearchModal) {
            mobileSearchModal.addEventListener('click', function(e) {
                if (e.target === mobileSearchModal) {
                    closeMobileSearchModal();
                }
            });
        }

        // Close modal function
        function closeMobileSearchModal() {
            if (mobileSearchContent) {
                mobileSearchContent.classList.remove('scale-100');
                mobileSearchContent.classList.add('scale-95');
            }
            setTimeout(() => {
                if (mobileSearchModal) {
                    mobileSearchModal.classList.add('hidden');
                    mobileSearchModal.classList.remove('flex');
                }
            }, 200);
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && mobileSearchModal && !mobileSearchModal.classList.contains('hidden')) {
                closeMobileSearchModal();
            }
        });
    });
</script><?php /**PATH C:\Users\<USER>\Downloads\Telegram Desktop\cs450 (3)\cs450 (3)\cs450\resources\views/includes/header.blade.php ENDPATH**/ ?>