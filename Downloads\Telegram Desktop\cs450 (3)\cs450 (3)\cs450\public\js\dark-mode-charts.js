// إدارة الوضع المظلم للمخططات
class DarkModeChartManager {
    constructor() {
        this.currentTheme = this.getCurrentTheme();
        this.charts = new Map();
        this.init();
    }

    getCurrentTheme() {
        return document.documentElement.classList.contains('dark') ? 'dark' : 'light';
    }

    init() {
        // مراقبة تغيير الوضع المظلم
        this.observeThemeChanges();
        
        // مراقبة إضافية بالتحقق الدوري
        setInterval(() => {
            this.checkThemeChange();
        }, 500);
    }

    observeThemeChanges() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    setTimeout(() => {
                        this.checkThemeChange();
                    }, 100);
                }
            });
        });

        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['class']
        });
    }

    checkThemeChange() {
        const newTheme = this.getCurrentTheme();
        if (newTheme !== this.currentTheme) {
            console.log('تم تغيير الوضع من', this.currentTheme, 'إلى', newTheme);
            this.currentTheme = newTheme;
            this.updateAllCharts();
        }
    }

    registerChart(id, createFunction) {
        this.charts.set(id, createFunction);
    }

    updateAllCharts() {
        this.charts.forEach((createFunction, id) => {
            try {
                createFunction();
                console.log('تم تحديث المخطط:', id);
            } catch (error) {
                console.error('خطأ في تحديث المخطط:', id, error);
            }
        });
    }

    // دالة مساعدة للحصول على ألوان الوضع المظلم
    getThemeColors() {
        const isDark = this.currentTheme === 'dark';
        return {
            primary: isDark ? '#3b82f6' : '#f97316',
            success: isDark ? '#34d399' : '#10b981',
            danger: isDark ? '#f87171' : '#ef4444',
            warning: isDark ? '#fbbf24' : '#f59e0b',
            info: isDark ? '#60a5fa' : '#3b82f6',
            purple: isDark ? '#a78bfa' : '#8b5cf6',
            text: isDark ? '#e5e7eb' : '#4b5563',
            textSecondary: isDark ? '#9ca3af' : '#6b7280',
            border: isDark ? '#374151' : '#e5e7eb',
            background: isDark ? '#1f2937' : '#ffffff'
        };
    }

    // دالة مساعدة للحصول على إعدادات الوضع المظلم
    getThemeSettings() {
        const isDark = this.currentTheme === 'dark';
        const colors = this.getThemeColors();
        
        return {
            theme: {
                mode: isDark ? 'dark' : 'light'
            },
            grid: {
                borderColor: colors.border,
                strokeDashArray: 2
            },
            xaxis: {
                labels: {
                    style: {
                        colors: colors.textSecondary,
                        fontFamily: 'Cairo, sans-serif'
                    }
                },
                axisBorder: {
                    color: colors.border
                },
                axisTicks: {
                    color: colors.border
                }
            },
            yaxis: {
                labels: {
                    style: {
                        colors: colors.textSecondary,
                        fontFamily: 'Cairo, sans-serif'
                    }
                }
            },
            tooltip: {
                theme: isDark ? 'dark' : 'light',
                style: {
                    fontSize: '12px',
                    fontFamily: 'Cairo, sans-serif'
                }
            },
            legend: {
                labels: {
                    colors: colors.text
                }
            }
        };
    }
}

// إنشاء مدير الوضع المظلم العام
window.darkModeManager = new DarkModeChartManager();

// دالة مساعدة لتسجيل مخطط
window.registerChart = function(id, createFunction) {
    window.darkModeManager.registerChart(id, createFunction);
};

// دالة مساعدة للحصول على ألوان الوضع الحالي
window.getThemeColors = function() {
    return window.darkModeManager.getThemeColors();
};

// دالة مساعدة للحصول على إعدادات الوضع الحالي
window.getThemeSettings = function() {
    return window.darkModeManager.getThemeSettings();
};

// دالة لإجبار تحديث جميع المخططات
window.forceUpdateCharts = function() {
    window.darkModeManager.updateAllCharts();
};

console.log('✅ تم تحميل مدير الوضع المظلم للمخططات');
