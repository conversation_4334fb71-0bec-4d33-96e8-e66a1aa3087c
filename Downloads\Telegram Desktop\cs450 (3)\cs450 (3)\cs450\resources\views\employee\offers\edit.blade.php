@extends('employee.layouts.app')

@section('title', 'تعديل العرض - ' . $offer->title)

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">تعديل العرض</h1>
            <p class="text-gray-600 dark:text-gray-400">تحديث معلومات وتفاصيل العرض</p>
        </div>
        <div class="flex gap-2">
            <a href="{{ route('employee.offers.show', $offer) }}" 
               class="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors">
                <i class="fas fa-eye ml-2"></i>
                عرض التفاصيل
            </a>
            <a href="{{ route('employee.offers.index') }}" 
               class="inline-flex items-center px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <!-- نموذج تعديل العرض -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <form action="{{ route('employee.offers.update', $offer) }}" method="POST" enctype="multipart/form-data" class="p-6">
            @csrf
            @method('PUT')
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- العمود الأول: المعلومات الأساسية -->
                <div class="space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                        المعلومات الأساسية
                    </h3>

                    <!-- عنوان العرض -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">عنوان العرض *</label>
                        <input type="text" name="title" value="{{ old('title', $offer->title) }}" required
                               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                               placeholder="مثال: خصم 25% على الوجبات العائلية">
                        @error('title')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- وصف العرض -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">وصف العرض *</label>
                        <textarea name="description" rows="4" required
                                  class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                  placeholder="اكتب وصفاً مفصلاً للعرض...">{{ old('description', $offer->description) }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- نوع العرض -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع العرض *</label>
                        <select name="offer_type" required
                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            <option value="">اختر نوع العرض</option>
                            <option value="percentage" {{ old('offer_type', $offer->offer_type) == 'percentage' ? 'selected' : '' }}>نسبة مئوية</option>
                            <option value="fixed_amount" {{ old('offer_type', $offer->offer_type) == 'fixed_amount' ? 'selected' : '' }}>مبلغ ثابت</option>
                            <option value="buy_get" {{ old('offer_type', $offer->offer_type) == 'buy_get' ? 'selected' : '' }}>اشتري واحد واحصل على آخر</option>
                            <option value="combo" {{ old('offer_type', $offer->offer_type) == 'combo' ? 'selected' : '' }}>عرض مجموعة</option>
                        </select>
                        @error('offer_type')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- صورة العرض -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">صورة العرض</label>
                        
                        @if($offer->image_path)
                            <div class="mb-4">
                                <img src="{{ $offer->image_url }}" alt="{{ $offer->title }}" 
                                     class="w-full h-32 object-cover rounded-lg border border-gray-200 dark:border-gray-600">
                                <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">الصورة الحالية</p>
                            </div>
                        @endif
                        
                        <input type="file" name="image" accept="image/*"
                               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            اختر صورة جديدة لاستبدال الحالية. الحد الأقصى: 2MB، الصيغ المدعومة: JPG, PNG, GIF
                        </p>
                        @error('image')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- العمود الثاني: تفاصيل الخصم والتوقيت -->
                <div class="space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                        تفاصيل الخصم والتوقيت
                    </h3>

                    <!-- نطاق التخفيض -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نطاق التخفيض</label>
                        <select name="discount_scope" id="discount_scope"
                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            <option value="total_bill" {{ old('discount_scope', $offer->discount_scope) == 'total_bill' ? 'selected' : '' }}>
                                تخفيض على إجمالي الفاتورة
                            </option>
                            <option value="specific_item" {{ old('discount_scope', $offer->discount_scope) == 'specific_item' ? 'selected' : '' }}>
                                تخفيض على عنصر معين
                            </option>
                        </select>
                        @error('discount_scope')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- العنصر المستهدف -->
                    <div id="target_item_section" style="display: {{ old('discount_scope', $offer->discount_scope) == 'specific_item' ? 'block' : 'none' }};">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">العنصر المستهدف</label>
                        <select name="target_item_id" id="target_item_id"
                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            <option value="">اختر العنصر المستهدف</option>
                            @foreach($menuItems as $item)
                                <option value="{{ $item->item_id }}" {{ old('target_item_id', $offer->target_item_id) == $item->item_id ? 'selected' : '' }}>
                                    {{ $item->name }} - {{ $item->price }} د.ل
                                </option>
                            @endforeach
                        </select>
                        @error('target_item_id')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            سيتم تطبيق الخصم على هذا العنصر فقط عند وجوده في السلة
                        </p>
                    </div>

                    <!-- نسبة الخصم -->
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نسبة الخصم (%)</label>
                            <input type="number" name="discount_percentage" value="{{ old('discount_percentage', $offer->discount_percentage) }}" 
                                   min="0" max="100" step="0.01"
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            @error('discount_percentage')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">مبلغ الخصم (د.ل)</label>
                            <input type="number" name="discount_amount" value="{{ old('discount_amount', $offer->discount_amount) }}" 
                                   min="0" step="0.01"
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            @error('discount_amount')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- الحد الأدنى للطلب -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحد الأدنى للطلب (د.ل)</label>
                        <input type="number" name="min_order_amount" value="{{ old('min_order_amount', $offer->min_order_amount) }}"
                               min="0" step="0.01"
                               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                        @error('min_order_amount')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- تواريخ العرض -->
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ البداية *</label>
                            <input type="datetime-local" name="start_date" 
                                   value="{{ old('start_date', $offer->start_date->format('Y-m-d\TH:i')) }}" required
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            @error('start_date')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ النهاية</label>
                            <input type="datetime-local" name="end_date" 
                                   value="{{ old('end_date', $offer->end_date ? $offer->end_date->format('Y-m-d\TH:i') : '') }}"
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">اتركه فارغاً للعروض المفتوحة</p>
                            @error('end_date')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- حد الاستخدام -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحد الأقصى للاستخدام</label>
                        <input type="number" name="max_uses" value="{{ old('max_uses', $offer->max_uses) }}" min="1"
                               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                               placeholder="اتركه فارغاً للاستخدام غير المحدود">
                        @error('max_uses')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- حالة العرض -->
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="is_active" value="1" {{ old('is_active', $offer->is_active) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-primary focus:ring-primary">
                            <span class="mr-2 text-sm font-medium text-gray-700 dark:text-gray-300">العرض نشط</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- الشروط والأحكام -->
            <div class="mt-8">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2 mb-6">
                    الشروط والأحكام
                </h3>

                <div>
                    <textarea name="terms_conditions" rows="4"
                              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                              placeholder="اكتب الشروط والأحكام للعرض...">{{ old('terms_conditions', is_array($offer->terms_conditions) ? implode("\n", $offer->terms_conditions) : $offer->terms_conditions) }}</textarea>
                    @error('terms_conditions')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- عناصر القائمة المطبقة -->
            <div class="mt-8">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2 mb-6">
                    عناصر القائمة المطبقة (اختياري)
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-60 overflow-y-auto">
                    @foreach($menuItems as $item)
                        <label class="flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="checkbox" name="menu_items[]" value="{{ $item->item_id }}"
                                   {{ in_array($item->item_id, old('menu_items', $selectedMenuItems)) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-primary focus:ring-primary">
                            <div class="mr-3">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $item->name }}</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">{{ $item->price }} د.ل</p>
                            </div>
                        </label>
                    @endforeach
                </div>
                <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    اختر عناصر القائمة التي ينطبق عليها هذا العرض. اتركه فارغاً إذا كان العرض ينطبق على جميع العناصر.
                </p>
            </div>

            <!-- أزرار الحفظ -->
            <div class="mt-8 flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <button type="submit" 
                        class="flex-1 px-6 py-3 bg-primary hover:bg-primary/90 text-white font-semibold rounded-lg transition-colors">
                    <i class="fas fa-save ml-2"></i>
                    حفظ التغييرات
                </button>
                <a href="{{ route('employee.offers.show', $offer) }}" 
                   class="flex-1 text-center px-6 py-3 bg-gray-500 hover:bg-gray-600 text-white font-semibold rounded-lg transition-colors">
                    <i class="fas fa-times ml-2"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>


@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const discountScopeSelect = document.getElementById('discount_scope');
    const targetItemSection = document.getElementById('target_item_section');
    const targetItemSelect = document.getElementById('target_item_id');

    function toggleTargetItemSection() {
        if (discountScopeSelect.value === 'specific_item') {
            targetItemSection.style.display = 'block';
            targetItemSelect.required = true;
        } else {
            targetItemSection.style.display = 'none';
            targetItemSelect.required = false;
            targetItemSelect.value = '';
        }
    }

    // تشغيل الدالة عند تحميل الصفحة
    toggleTargetItemSection();

    // تشغيل الدالة عند تغيير الاختيار
    discountScopeSelect.addEventListener('change', toggleTargetItemSection);
});
</script>
@endpush
