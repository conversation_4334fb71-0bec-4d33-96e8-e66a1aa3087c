@extends('layouts.admin')

@section('title', 'إضافة طلب جديد')

@section('content')
<!-- Header Section -->
<div class="mb-8">
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-6 text-white shadow-xl">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex items-center space-x-4 space-x-reverse">
                <div class="bg-white/20 rounded-full p-3">
                    <i class="fas fa-plus-circle text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold">إضافة طلب جديد</h1>
                    <p class="text-blue-100 mt-1">إنشاء طلب جديد للعملاء بسهولة وسرعة</p>
                </div>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="{{ route('admin.orders') }}" class="bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white font-semibold py-3 px-6 rounded-xl flex items-center space-x-2 space-x-reverse transition-all duration-300 hover:scale-105">
                    <i class="fas fa-arrow-right"></i>
                    <span>العودة للطلبات</span>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Main Form Container -->
<div class="w-full">
    <!-- Customer Info and Order Summary Row -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 mb-6">
        <!-- Customer Information Card (3 columns) -->
        <div class="lg:col-span-3">
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden">
            <!-- Form Header -->
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 px-6 py-4 border-b border-gray-200 dark:border-gray-600">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                    <i class="fas fa-user-circle ml-2 text-blue-500"></i>
                    معلومات العميل
                </h3>
            </div>

            <div class="p-6">
                <form action="{{ route('admin.orders.store') }}" method="POST" id="orderForm">
                    @csrf

                    <!-- Hidden fields for order items -->
                    <div id="order-items-container"></div>

                    <!-- Customer Type Selection -->
                    <div class="mb-8">
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                            <div class="flex items-center justify-center space-x-8 space-x-reverse">
                                <label class="flex items-center cursor-pointer group">
                                    <input type="radio" id="registered_customer" name="customer_type" value="registered" class="customer-type-radio sr-only" checked>
                                    <div class="relative">
                                        <div class="w-6 h-6 bg-white border-2 border-gray-300 rounded-full group-hover:border-blue-400 transition-colors"></div>
                                        <div class="absolute inset-0 w-6 h-6 bg-blue-500 rounded-full scale-0 transition-transform duration-200 ease-out"></div>
                                        <div class="absolute inset-0 w-6 h-6 flex items-center justify-center">
                                            <div class="w-2 h-2 bg-white rounded-full opacity-0 transition-opacity duration-200"></div>
                                        </div>
                                    </div>
                                    <span class="mr-3 text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                                        <i class="fas fa-user-check ml-2"></i>
                                        عميل مسجل
                                    </span>
                                </label>

                                <label class="flex items-center cursor-pointer group">
                                    <input type="radio" id="guest_customer" name="customer_type" value="guest" class="customer-type-radio sr-only">
                                    <div class="relative">
                                        <div class="w-6 h-6 bg-white border-2 border-gray-300 rounded-full group-hover:border-blue-400 transition-colors"></div>
                                        <div class="absolute inset-0 w-6 h-6 bg-blue-500 rounded-full scale-0 transition-transform duration-200 ease-out"></div>
                                        <div class="absolute inset-0 w-6 h-6 flex items-center justify-center">
                                            <div class="w-2 h-2 bg-white rounded-full opacity-0 transition-opacity duration-200"></div>
                                        </div>
                                    </div>
                                    <span class="mr-3 text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                                        <i class="fas fa-user-plus ml-2"></i>
                                        زبون عادي (غير مسجل)
                                    </span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Registered Customer Fields -->
                    <div id="registered_customer_fields" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-2">
                                <label for="user_id" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                                    <i class="fas fa-user ml-2 text-blue-500"></i>
                                    العميل
                                </label>

                                <!-- Customer Search with Autocomplete -->
                                <div class="relative mb-3">
                                    <input type="text" id="customer_search" placeholder="ابحث عن العميل بالاسم أو الإيميل..."
                                           class="w-full px-4 py-3 pr-12 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                           autocomplete="off">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <i class="fas fa-search text-gray-400"></i>
                                    </div>

                                    <!-- Dropdown Results -->
                                    <div id="customer_dropdown" class="absolute top-full left-0 right-0 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl shadow-lg z-50 max-h-60 overflow-y-auto hidden">
                                        <!-- Results will be populated here -->
                                    </div>
                                </div>

                                <!-- Customer Select -->
                                <div class="relative">
                                    <select id="user_id" name="user_id" class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200">
                                        <option value="">اختر العميل</option>
                                        @foreach(\App\Models\User::where('user_type', 'customer')->orderBy('first_name')->get() as $user)
                                            <option value="{{ $user->user_id }}" data-search="{{ strtolower($user->first_name . ' ' . $user->last_name . ' ' . $user->email) }}" {{ old('user_id') == $user->user_id ? 'selected' : '' }}>
                                                {{ $user->first_name }} {{ $user->last_name }} ({{ $user->email }})
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-chevron-down text-gray-400"></i>
                                    </div>
                                </div>

                                <!-- Selected customer display -->
                                <div id="selected_customer" class="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800 hidden">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <i class="fas fa-user-check text-blue-500 ml-2"></i>
                                            <span id="selected_customer_name" class="text-blue-700 dark:text-blue-300 font-medium"></span>
                                        </div>
                                        <button type="button" id="clear_customer" class="text-red-500 hover:text-red-700 transition-colors">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Customer data for JavaScript -->
                                <script type="application/json" id="customers_data">
                                    [
                                        @foreach(\App\Models\User::where('user_type', 'customer')->orderBy('first_name')->get() as $user)
                                            {
                                                "id": "{{ $user->user_id }}",
                                                "name": "{{ $user->first_name }} {{ $user->last_name }}",
                                                "email": "{{ $user->email }}",
                                                "phone": "{{ $user->phone ?? '' }}",
                                                "search": "{{ strtolower($user->first_name . ' ' . $user->last_name . ' ' . $user->email) }}"
                                            }{{ !$loop->last ? ',' : '' }}
                                        @endforeach
                                    ]
                                </script>

                                <!-- Tables data for JavaScript -->
                                <script type="application/json" id="tables_data">
                                    [
                                        {
                                            "id": "",
                                            "name": "بدون طاولة (طلب خارجي)",
                                            "number": "",
                                            "capacity": "",
                                            "status": "available",
                                            "search": "بدون طاولة طلب خارجي"
                                        }{{ count($tables) > 0 ? ',' : '' }}
                                        @foreach($tables as $table)
                                            {
                                                "id": "{{ $table->table_id }}",
                                                "name": "طاولة {{ $table->table_number }} ({{ $table->capacity }} أشخاص){{ $table->status != 'available' ? ' - مشغولة' : '' }}",
                                                "number": "{{ $table->table_number }}",
                                                "capacity": "{{ $table->capacity }}",
                                                "status": "{{ $table->status }}",
                                                "search": "{{ strtolower('طاولة ' . $table->table_number . ' ' . $table->capacity) }}"
                                            }{{ !$loop->last ? ',' : '' }}
                                        @endforeach
                                    ]
                                </script>
                                @error('user_id')
                                    <p class="mt-1 text-sm text-red-500 flex items-center">
                                        <i class="fas fa-exclamation-circle ml-1"></i>
                                        {{ $message }}
                                    </p>
                                @enderror
                            </div>

                            <div class="space-y-2">
                                <label for="table_id" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                                    <i class="fas fa-chair ml-2 text-green-500"></i>
                                    الطاولة (اختياري)
                                </label>
                                <!-- Table Select -->
                                <div class="relative">
                                    <select id="table_id" name="table_id" class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200">
                                        <option value="">بدون طاولة (طلب خارجي)</option>
                                        @foreach($tables as $table)
                                            <option value="{{ $table->table_id }}" {{ old('table_id') == $table->table_id ? 'selected' : '' }} {{ $table->status != 'available' ? 'disabled' : '' }}>
                                                طاولة {{ $table->table_number }} ({{ $table->capacity }} أشخاص) {{ $table->status != 'available' ? '- مشغولة' : '' }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-chevron-down text-gray-400"></i>
                                    </div>
                                </div>
                                @error('table_id')
                                    <p class="mt-1 text-sm text-red-500 flex items-center">
                                        <i class="fas fa-exclamation-circle ml-1"></i>
                                        {{ $message }}
                                    </p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Guest Customer Fields -->
                    <div id="guest_customer_fields" class="space-y-6" style="display: none;">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-2">
                                <label for="customer_name" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                                    <i class="fas fa-user-edit ml-2 text-purple-500"></i>
                                    اسم الزبون
                                </label>
                                <input type="text" id="customer_name" name="customer_name" value="{{ old('customer_name') }}"
                                       class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                       placeholder="أدخل اسم الزبون">
                                @error('customer_name')
                                    <p class="mt-1 text-sm text-red-500 flex items-center">
                                        <i class="fas fa-exclamation-circle ml-1"></i>
                                        {{ $message }}
                                    </p>
                                @enderror
                            </div>

                            <div class="space-y-2">
                                <label for="customer_phone" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                                    <i class="fas fa-phone ml-2 text-orange-500"></i>
                                    رقم الهاتف (اختياري)
                                </label>
                                <input type="text" id="customer_phone" name="customer_phone" value="{{ old('customer_phone') }}"
                                       class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                       placeholder="أدخل رقم الهاتف">
                                @error('customer_phone')
                                    <p class="mt-1 text-sm text-red-500 flex items-center">
                                        <i class="fas fa-exclamation-circle ml-1"></i>
                                        {{ $message }}
                                    </p>
                                @enderror
                            </div>
                        </div>

                        <div class="space-y-2">
                            <label for="table_id_guest" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                                <i class="fas fa-chair ml-2 text-green-500"></i>
                                الطاولة (اختياري)
                            </label>
                            <!-- Table Select for Guest -->
                            <div class="relative">
                                <select id="table_id_guest" name="table_id" class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200">
                                    <option value="">بدون طاولة (طلب خارجي)</option>
                                    @foreach($tables as $table)
                                        <option value="{{ $table->table_id }}" {{ old('table_id') == $table->table_id ? 'selected' : '' }} {{ $table->status != 'available' ? 'disabled' : '' }}>
                                            طاولة {{ $table->table_number }} ({{ $table->capacity }} أشخاص) {{ $table->status != 'available' ? '- مشغولة' : '' }}
                                        </option>
                                    @endforeach
                                </select>
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-chevron-down text-gray-400"></i>
                                </div>
                            </div>
                            @error('table_id')
                                <p class="mt-1 text-sm text-red-500 flex items-center">
                                    <i class="fas fa-exclamation-circle ml-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Summary Card (1 column - متوسط الحجم) -->
        <div class="lg:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden border border-orange-200 dark:border-orange-700 h-fit">
                <!-- Summary Header -->
                <div class="bg-gradient-to-r from-orange-500 to-orange-600 px-4 py-3">
                    <div class="flex items-center justify-between text-white">
                        <div class="flex items-center">
                            <div class="bg-white bg-opacity-20 rounded-full p-2 ml-2">
                                <i class="fas fa-shopping-cart text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-sm font-bold">ملخص الطلب</h3>
                            </div>
                        </div>
                        <div class="text-right">
                            <p id="total-amount-header" class="text-sm font-bold">0.00 د.ل</p>
                        </div>
                    </div>
                </div>

                <div class="p-4">
                    <!-- Selected Items -->
                    <div class="space-y-2 mb-4" id="selected-items-container">
                        <div id="no-items-message" class="text-center py-4">
                            <div class="w-8 h-8 mx-auto mb-2 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                                <i class="fas fa-shopping-cart text-sm text-gray-400"></i>
                            </div>
                            <p class="text-gray-500 dark:text-gray-400 font-medium text-sm">السلة فارغة</p>
                            <p class="text-gray-400 dark:text-gray-500 text-xs mt-1">اختر من قائمة الطعام</p>
                        </div>
                    </div>

                    <!-- Order Total -->
                    <div id="order-summary-footer" class="border-t border-gray-200 dark:border-gray-600 pt-3">
                        <div class="space-y-2 mb-3">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">المجموع:</span>
                                <span id="subtotal-amount" class="font-medium text-gray-800 dark:text-white">0.00 د.ل</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400" data-tax-rate>الضريبة ({{ $taxRate ?? 15 }}%):</span>
                                <span id="tax-amount" class="font-medium text-gray-800 dark:text-white">0.00 د.ل</span>
                            </div>
                            <hr class="border-gray-200 dark:border-gray-700">
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-bold text-gray-800 dark:text-white">الإجمالي:</span>
                                <span id="total-amount" class="text-lg font-bold text-orange-600 dark:text-orange-400">0.00 د.ل</span>
                            </div>
                        </div>

                        <button type="submit" form="orderForm" class="w-full bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white font-bold py-3 px-4 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center text-sm">
                            <i class="fas fa-check-circle ml-2"></i>
                            <span>إنشاء الطلب</span>
                        </button>
                    </div>

                    @error('items')
                        <div class="mt-1 p-1 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                            <p class="text-xs text-red-600 dark:text-red-400 flex items-center">
                                <i class="fas fa-exclamation-triangle ml-1"></i>
                                {{ $message }}
                            </p>
                        </div>
                    @enderror
                </div>
            </div>
        </div>
    </div>

    <!-- Menu Items Section -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden mt-8">
            <!-- Menu Header -->
            <div class="bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 px-6 py-4 border-b border-gray-200 dark:border-gray-600">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                    <i class="fas fa-utensils ml-2 text-orange-500"></i>
                    قائمة الطعام
                </h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">اختر العناصر لإضافتها للطلب</p>
            </div>

            <div class="p-6">
                <!-- Category Buttons -->
                <div class="mb-6">
                    <div class="flex flex-wrap gap-3">
                        @foreach($menuItems as $category => $items)
                            <button type="button" class="category-btn group relative px-6 py-3 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 text-gray-700 dark:text-gray-300 rounded-xl hover:from-blue-500 hover:to-purple-600 hover:text-white transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg" data-category="{{ $category }}">
                                <span class="relative z-10 font-medium">{{ $categories[$category] ?? ucfirst($category) }}</span>
                                <div class="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            </button>
                        @endforeach
                    </div>
                </div>

                <!-- Menu Categories -->
                @foreach($menuItems as $category => $items)
                    <div class="menu-category mb-8" id="category-{{ $category }}" style="display: none;">
                        <div class="mb-4">
                            <h4 class="text-xl font-bold text-gray-800 dark:text-white flex items-center">
                                <i class="fas fa-list-ul ml-2 text-blue-500"></i>
                                {{ $categories[$category] ?? ucfirst($category) }}
                            </h4>
                            <div class="h-1 w-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mt-2"></div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach($items as $item)
                                <div class="group bg-gradient-to-br from-white to-gray-50 dark:from-gray-700 dark:to-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100 dark:border-gray-600">
                                    <div class="flex justify-between items-start mb-4">
                                        <div class="flex-1">
                                            <h5 class="font-bold text-lg text-gray-800 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                                                {{ $item->name }}
                                            </h5>
                                            <div class="flex items-center space-x-2 space-x-reverse">
                                                <span class="text-2xl font-bold text-green-600 dark:text-green-400">
                                                    {{ number_format($item->price, 2) }}
                                                </span>
                                                <span class="text-sm text-gray-500 dark:text-gray-400">د.ل</span>
                                            </div>
                                        </div>
                                        <div class="flex gap-2">
                                            <button type="button" class="add-item-btn bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-full w-12 h-12 flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg hover:shadow-xl" data-id="{{ $item->item_id }}" data-name="{{ $item->name }}" data-price="{{ $item->price }}">
                                                <i class="fas fa-plus text-lg"></i>
                                            </button>
                                            <button type="button" class="customize-item-btn bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white rounded-full w-12 h-12 flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg hover:shadow-xl" data-id="{{ $item->item_id }}" data-name="{{ $item->name }}" data-price="{{ $item->price }}" title="تخصيص الطلب">
                                                <i class="fas fa-cog text-lg"></i>
                                            </button>
                                        </div>
                                    </div>
                                    @if($item->description)
                                        <p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                                            {{ $item->description }}
                                        </p>
                                    @endif

                                    <!-- Decorative element -->
                                    <div class="absolute top-4 right-4 w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>



                    @error('items')
                        <div class="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl">
                            <p class="text-sm text-red-600 dark:text-red-400 flex items-center">
                                <i class="fas fa-exclamation-triangle ml-2"></i>
                                {{ $message }}
                            </p>
                        </div>
                    @enderror
                </div>
            </div>
        </div>
    </div>
</div>

                </form>
            </div>
        </div>
@endsection

@section('styles')
<style>
    /* Custom animations and transitions */
    .menu-category {
        transition: opacity 0.3s ease-in-out;
    }

    .category-btn {
        position: relative;
        overflow: hidden;
    }

    .category-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .category-btn:hover::before {
        left: 100%;
    }

    .add-item-btn {
        position: relative;
        overflow: hidden;
    }

    .add-item-btn::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255,255,255,0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.3s, height 0.3s;
    }

    .add-item-btn:active::after {
        width: 300px;
        height: 300px;
    }

    /* Floating animation for cards */
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-5px); }
    }

    .group:hover {
        animation: float 2s ease-in-out infinite;
    }

    /* Pulse animation for totals */
    @keyframes pulse-scale {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .scale-110 {
        animation: pulse-scale 0.3s ease-in-out;
    }

    /* Gradient text */
    .gradient-text {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Custom scrollbar */
    .custom-scrollbar::-webkit-scrollbar {
        width: 6px;
    }

    .custom-scrollbar::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 10px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    }

    /* رسائل جديدة في الوسط تماماً */
    .center-message {
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        z-index: 999999 !important;
        min-width: 350px !important;
        max-width: 500px !important;
        padding: 20px 30px !important;
        border-radius: 15px !important;
        font-size: 16px !important;
        font-weight: 600 !important;
        text-align: center !important;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3) !important;
        backdrop-filter: blur(10px) !important;
        border: 2px solid rgba(255, 255, 255, 0.2) !important;
        transition: all 0.3s ease !important;
    }

    .center-message.success {
        background: linear-gradient(135deg, #10b981, #059669) !important;
        color: white !important;
    }

    .center-message.error {
        background: linear-gradient(135deg, #ef4444, #dc2626) !important;
        color: white !important;
    }

    .center-message.info {
        background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
        color: white !important;
    }

    /* إخفاء الرسائل القديمة فقط */
    .toast-message:not(.center-message),
    .alert:not(.center-message),
    div[class*="toast"]:not(.center-message):not(.sidebar):not([class*="sidebar"]),
    div[class*="alert"]:not(.center-message):not(.sidebar):not([class*="sidebar"]) {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
    }
</style>
@endsection

@section('scripts')
<!-- تحميل ملف إعدادات الضريبة -->
<script src="{{ asset('js/settings-refresh.js') }}"></script>
<script>
    console.log('🚀 بدء تحميل ملف الجافا سكريبت...');

    // تعريف المتغيرات العالمية
    let orderItems = [];
    let isSubmitting = false;

    console.log('🎯 بدء تحميل JavaScript للطلبات');

    // تحقق من وجود العناصر المطلوبة
    console.log('🔍 التحقق من العناصر...');
    console.log('Category tabs:', document.querySelectorAll('.category-tab').length);
    console.log('Add item buttons:', document.querySelectorAll('.add-item-btn').length);
    console.log('Tab panes:', document.querySelectorAll('.tab-pane').length);

    document.addEventListener('DOMContentLoaded', function() {
        console.log('📄 تم تحميل الصفحة');

        // إزالة الرسائل فقط - ليس الـ sidebar
        setTimeout(() => {
            const messagesToRemove = document.querySelectorAll(`
                .toast-message,
                .alert,
                div[class*="toast"]:not(.sidebar):not([class*="sidebar"]),
                div[class*="alert"]:not(.sidebar):not([class*="sidebar"])
            `);

            messagesToRemove.forEach(msg => {
                // تأكد أنه ليس جزء من الـ sidebar
                if (!msg.closest('.sidebar') && !msg.closest('nav') && !msg.closest('[class*="sidebar"]')) {
                    msg.style.display = 'none';
                    msg.remove();
                }
            });

            console.log('🧹 تم إزالة الرسائل فقط');
        }, 100);

        // إضافة تأخير للتأكد من تحميل جميع العناصر
        setTimeout(function() {
            console.log('⏰ بدء التهيئة بعد التأخير...');

            // تحقق من العناصر
            console.log('🔍 التحقق من العناصر...');
            console.log('Category buttons:', document.querySelectorAll('.category-btn').length);
            console.log('Add item buttons:', document.querySelectorAll('.add-item-btn').length);

            // تهيئة أزرار نوع العميل
            initializeCustomerTypeRadios();

            // تهيئة البحث عن العميل
            initializeCustomerSearch();

            // تهيئة أزرار الفئات
            initializeCategoryButtons();

            // تهيئة أزرار إضافة العناصر
            initializeAddItemButtons();

            // تهيئة النموذج
            initializeForm();

            console.log('✅ تم تهيئة الصفحة بنجاح');
        }, 500);
    });

    function initializeCustomerTypeRadios() {
        // Enhanced radio button styling
        function updateRadioButtons() {
            document.querySelectorAll('.customer-type-radio').forEach(radio => {
                const container = radio.closest('label');
                const circle = container.querySelector('div > div:first-child');
                const innerCircle = container.querySelector('div > div:nth-child(2)');
                const dot = container.querySelector('div > div:last-child > div');

                if (radio.checked) {
                    circle.classList.add('border-blue-500');
                    innerCircle.classList.add('scale-100');
                    dot.classList.add('opacity-100');
                } else {
                    circle.classList.remove('border-blue-500');
                    innerCircle.classList.remove('scale-100');
                    dot.classList.remove('opacity-100');
                }
            });
        }

        // Toggle between registered customer and guest customer fields
        const customerTypeRadios = document.querySelectorAll('.customer-type-radio');
        customerTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                updateRadioButtons();

                if (this.value === 'registered') {
                    document.getElementById('registered_customer_fields').style.display = 'block';
                    document.getElementById('guest_customer_fields').style.display = 'none';
                    // Clear guest customer fields
                    document.getElementById('customer_name').value = '';
                    document.getElementById('customer_phone').value = '';
                } else {
                    document.getElementById('registered_customer_fields').style.display = 'none';
                    document.getElementById('guest_customer_fields').style.display = 'block';
                    // Clear registered customer fields
                    document.getElementById('user_id').value = '';
                }
            });
        });

        // Initialize radio buttons
        updateRadioButtons();

        // تم نقل البحث إلى دالة منفصلة

        // Show first category by default
        const firstCategoryBtn = document.querySelector('.category-btn');
        if (firstCategoryBtn) {
            const firstCategory = firstCategoryBtn.dataset.category;
            document.getElementById('category-' + firstCategory).style.display = 'block';
            firstCategoryBtn.classList.add('from-blue-500', 'to-purple-600', 'text-white', 'shadow-lg');
            firstCategoryBtn.classList.remove('from-gray-100', 'to-gray-200', 'dark:from-gray-700', 'dark:to-gray-800', 'text-gray-700', 'dark:text-gray-300');
        }

        // Category buttons with enhanced animations
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const category = this.dataset.category;

                // Hide all categories with fade effect
                document.querySelectorAll('.menu-category').forEach(div => {
                    div.style.opacity = '0';
                    setTimeout(() => {
                        div.style.display = 'none';
                    }, 150);
                });

                // Reset all buttons
                document.querySelectorAll('.category-btn').forEach(button => {
                    button.classList.remove('from-blue-500', 'to-purple-600', 'text-white', 'shadow-lg');
                    button.classList.add('from-gray-100', 'to-gray-200', 'dark:from-gray-700', 'dark:to-gray-800', 'text-gray-700', 'dark:text-gray-300');
                });

                // Show selected category with fade in
                setTimeout(() => {
                    const selectedCategory = document.getElementById('category-' + category);
                    selectedCategory.style.display = 'block';
                    selectedCategory.style.opacity = '0';
                    setTimeout(() => {
                        selectedCategory.style.opacity = '1';
                    }, 50);
                }, 150);

                // Highlight selected button
                this.classList.add('from-blue-500', 'to-purple-600', 'text-white', 'shadow-lg');
                this.classList.remove('from-gray-100', 'to-gray-200', 'dark:from-gray-700', 'dark:to-gray-800', 'text-gray-700', 'dark:text-gray-300');
            });
        });

        // Add item buttons
        document.querySelectorAll('.add-item-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const itemId = this.dataset.id;
                const itemName = this.dataset.name;
                const itemPrice = parseFloat(this.dataset.price);

                addItemToOrder(itemId, itemName, itemPrice);
            });
        });

        // Form submission with enhanced validation
        // تم إزالة هذا المعالج لتجنب التضارب مع initializeForm()
    }

    function initializeCustomerSearch() {
        console.log('🔍 تهيئة البحث والسيلكت للعملاء...');

        // تحميل بيانات العملاء
        const customersData = JSON.parse(document.getElementById('customers_data').textContent);

        console.log('📋 تم تحميل', customersData.length, 'عميل');

        // تهيئة البحث التلقائي للعملاء
        initializeAutocomplete('customer_search', 'customer_dropdown', 'user_id', 'selected_customer', 'selected_customer_name', 'clear_customer', customersData, 'العميل');

        // تهيئة السيلكت مع البحث
        initializeSelectWithSearch();

        console.log('✅ تم تهيئة البحث والسيلكت للعملاء بنجاح');
    }

    function initializeSelectWithSearch() {
        const customerSearch = document.getElementById('customer_search');
        const customerSelect = document.getElementById('user_id');
        const selectedDisplay = document.getElementById('selected_customer');
        const selectedName = document.getElementById('selected_customer_name');
        const clearButton = document.getElementById('clear_customer');

        if (!customerSearch || !customerSelect) return;

        // حفظ الخيارات الأصلية
        const originalOptions = Array.from(customerSelect.options);

        // البحث في السيلكت
        customerSearch.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();

            // مسح الخيارات الحالية
            customerSelect.innerHTML = '<option value="">اختر العميل</option>';

            if (searchTerm === '') {
                // إظهار جميع الخيارات
                originalOptions.slice(1).forEach(option => {
                    customerSelect.appendChild(option.cloneNode(true));
                });
            } else {
                // تصفية الخيارات
                let foundCount = 0;
                originalOptions.slice(1).forEach(option => {
                    const searchData = option.getAttribute('data-search') || '';
                    const optionText = option.textContent.toLowerCase();

                    if (searchData.includes(searchTerm) || optionText.includes(searchTerm)) {
                        customerSelect.appendChild(option.cloneNode(true));
                        foundCount++;
                    }
                });

                // إضافة خيار "لا توجد نتائج" إذا لم يتم العثور على شيء
                if (foundCount === 0) {
                    const noResultOption = document.createElement('option');
                    noResultOption.value = '';
                    noResultOption.textContent = 'لا توجد نتائج للبحث "' + searchTerm + '"';
                    noResultOption.disabled = true;
                    noResultOption.style.color = '#9CA3AF';
                    customerSelect.appendChild(noResultOption);
                }
            }
        });

        // عند اختيار عميل من السيلكت
        customerSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (this.value && selectedOption) {
                const customerName = selectedOption.textContent;
                if (selectedDisplay && selectedName) {
                    selectedName.textContent = customerName;
                    selectedDisplay.classList.remove('hidden');
                }
                showSuccessMessage(`تم اختيار العميل: ${customerName}`);
            } else {
                if (selectedDisplay) {
                    selectedDisplay.classList.add('hidden');
                }
            }
        });

        // مسح الاختيار
        if (clearButton) {
            clearButton.addEventListener('click', function() {
                customerSelect.value = '';
                customerSearch.value = '';
                if (selectedDisplay) {
                    selectedDisplay.classList.add('hidden');
                }
                // إعادة تحميل جميع الخيارات
                customerSelect.innerHTML = '<option value="">اختر العميل</option>';
                originalOptions.slice(1).forEach(option => {
                    customerSelect.appendChild(option.cloneNode(true));
                });
                console.log('🗑️ تم مسح اختيار العميل');
            });
        }
    }

    function initializeAutocomplete(searchId, dropdownId, hiddenInputId, selectedDisplayId, selectedNameId, clearButtonId, data, type) {
        const searchInput = document.getElementById(searchId);
        const dropdown = document.getElementById(dropdownId);
        const hiddenInput = document.getElementById(hiddenInputId);
        const selectedDisplay = document.getElementById(selectedDisplayId);
        const selectedName = document.getElementById(selectedNameId);
        const clearButton = document.getElementById(clearButtonId);

        if (!searchInput || !dropdown || !hiddenInput) {
            console.warn('⚠️ عناصر البحث غير مكتملة لـ', type);
            return;
        }

        let selectedIndex = -1;

        // البحث التلقائي
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();
            selectedIndex = -1;

            if (searchTerm === '') {
                dropdown.classList.add('hidden');
                return;
            }

            // تصفية البيانات
            const filteredData = data.filter(item =>
                item.search.includes(searchTerm) ||
                item.name.toLowerCase().includes(searchTerm)
            );

            // عرض النتائج
            if (filteredData.length > 0) {
                dropdown.innerHTML = '';
                filteredData.forEach((item, index) => {
                    const div = document.createElement('div');
                    div.className = 'px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer border-b border-gray-100 dark:border-gray-600 last:border-b-0';
                    div.innerHTML = `
                        <div class="flex items-center">
                            <i class="fas ${type === 'العميل' ? 'fa-user' : 'fa-chair'} text-gray-400 ml-2"></i>
                            <span class="text-gray-800 dark:text-white">${item.name}</span>
                            ${item.status === 'occupied' ? '<span class="text-red-500 text-xs mr-2">(مشغولة)</span>' : ''}
                        </div>
                    `;

                    div.addEventListener('click', () => selectItem(item));
                    dropdown.appendChild(div);
                });
                dropdown.classList.remove('hidden');
            } else {
                dropdown.innerHTML = `
                    <div class="px-4 py-3 text-gray-500 dark:text-gray-400 text-center">
                        <i class="fas fa-search ml-2"></i>
                        لا توجد نتائج للبحث "${searchTerm}"
                    </div>
                `;
                dropdown.classList.remove('hidden');
            }
        });

        // التنقل بالأسهم
        searchInput.addEventListener('keydown', function(e) {
            const items = dropdown.querySelectorAll('div[class*="hover:bg-gray-100"]');

            if (e.key === 'ArrowDown') {
                e.preventDefault();
                selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
                updateSelection(items);
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                selectedIndex = Math.max(selectedIndex - 1, -1);
                updateSelection(items);
            } else if (e.key === 'Enter') {
                e.preventDefault();
                if (selectedIndex >= 0 && items[selectedIndex]) {
                    items[selectedIndex].click();
                }
            } else if (e.key === 'Escape') {
                dropdown.classList.add('hidden');
                selectedIndex = -1;
            }
        });

        function updateSelection(items) {
            items.forEach((item, index) => {
                if (index === selectedIndex) {
                    item.classList.add('bg-blue-100', 'dark:bg-blue-900');
                } else {
                    item.classList.remove('bg-blue-100', 'dark:bg-blue-900');
                }
            });
        }

        function selectItem(item) {
            if (item.status === 'occupied' && type === 'الطاولة') {
                showErrorMessage('هذه الطاولة مشغولة حالياً');
                return;
            }

            hiddenInput.value = item.id;
            searchInput.value = '';
            dropdown.classList.add('hidden');

            if (selectedDisplay && selectedName) {
                selectedName.textContent = item.name;
                selectedDisplay.classList.remove('hidden');
            }

            console.log('✅ تم اختيار', type + ':', item.name);
            showSuccessMessage(`تم اختيار ${type}: ${item.name}`);
        }

        // مسح الاختيار
        if (clearButton) {
            clearButton.addEventListener('click', function() {
                hiddenInput.value = '';
                searchInput.value = '';
                dropdown.classList.add('hidden');
                if (selectedDisplay) {
                    selectedDisplay.classList.add('hidden');
                }
                console.log('🗑️ تم مسح اختيار', type);
            });
        }

        // إخفاء القائمة عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
                dropdown.classList.add('hidden');
                selectedIndex = -1;
            }
        });
    }

    function initializeForm() {
        console.log('🔧 تهيئة النموذج...');

        // Form submission handling
        const orderForm = document.getElementById('orderForm');
        if (orderForm) {
            console.log('✅ تم العثور على النموذج');

            orderForm.addEventListener('submit', function(e) {
                console.log('📝 محاولة إرسال النموذج...');

                // التحقق من اختيار العميل أولاً
                const customerType = document.querySelector('input[name="customer_type"]:checked');
                console.log('🔍 نوع العميل المختار:', customerType?.value);

                if (!customerType) {
                    e.preventDefault();
                    console.log('❌ لم يتم اختيار نوع العميل');
                    showErrorMessage('يرجى اختيار نوع العميل أولاً');
                    return false;
                }

                // التحقق من بيانات العميل حسب النوع
                if (customerType.value === 'registered') {
                    const selectedCustomer = document.getElementById('user_id').value;
                    if (!selectedCustomer) {
                        e.preventDefault();
                        console.log('❌ لم يتم اختيار عميل مسجل');
                        showErrorMessage('يرجى اختيار عميل من القائمة');
                        return false;
                    }
                } else if (customerType.value === 'guest') {
                    const customerName = document.getElementById('customer_name').value.trim();
                    if (!customerName) {
                        e.preventDefault();
                        console.log('❌ لم يتم إدخال اسم العميل');
                        showErrorMessage('يرجى إدخال اسم العميل');
                        return false;
                    }
                }

                // التحقق من وجود عناصر في الطلب
                console.log('🔍 التحقق من العناصر في orderItems:', orderItems.length);
                console.log('📋 محتويات orderItems:', orderItems);

                if (orderItems.length === 0) {
                    e.preventDefault();
                    console.log('❌ لا توجد عناصر في الطلب');

                    // عرض رسالة خطأ في الوسط
                    showErrorMessage('يرجى إضافة عنصر واحد على الأقل إلى الطلب');
                    return false;
                }

                // التحقق من الحقول المخفية في النموذج
                const hiddenInputs = document.querySelectorAll('#order-items-container input[name*="items"]');
                console.log('🔍 عدد الحقول المخفية:', hiddenInputs.length);

                if (hiddenInputs.length === 0) {
                    e.preventDefault();
                    console.log('❌ لا توجد حقول مخفية للعناصر');

                    // محاولة تحديث النموذج
                    updateFormWithOrderItems();

                    // التحقق مرة أخرى
                    const updatedInputs = document.querySelectorAll('#order-items-container input[name*="items"]');
                    if (updatedInputs.length === 0) {
                        showErrorMessage('خطأ في تحديث النموذج. يرجى المحاولة مرة أخرى.');
                        return false;
                    }
                }

                // التحقق من حالة الإرسال
                if (isSubmitting) {
                    e.preventDefault();
                    console.log('⏳ الطلب قيد الإرسال بالفعل');
                    return false;
                }

                console.log('✅ بدء إرسال الطلب...');
                isSubmitting = true;

                // تحديث زر الإرسال
                const submitBtn = orderForm.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.innerHTML = `
                        <i class="fas fa-spinner fa-spin ml-2"></i>
                        <span>جاري إنشاء الطلب...</span>
                    `;
                    submitBtn.disabled = true;
                    console.log('🔄 تم تحديث زر الإرسال');
                }
            });
        } else {
            console.error('❌ لم يتم العثور على النموذج!');
        }
    }

    function initializeCategoryButtons() {
        // Show first category by default
        const firstCategoryBtn = document.querySelector('.category-btn');
        if (firstCategoryBtn) {
            const firstCategory = firstCategoryBtn.dataset.category;
            const firstCategoryDiv = document.getElementById('category-' + firstCategory);
            if (firstCategoryDiv) {
                firstCategoryDiv.style.display = 'block';
                firstCategoryBtn.classList.add('from-blue-500', 'to-purple-600', 'text-white', 'shadow-lg');
                firstCategoryBtn.classList.remove('from-gray-100', 'to-gray-200', 'dark:from-gray-700', 'dark:to-gray-800', 'text-gray-700', 'dark:text-gray-300');
            }
        }

        // Category buttons with enhanced animations
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const category = this.dataset.category;
                console.log(`🏷️ تم النقر على فئة: ${category}`);

                // Hide all categories with fade effect
                document.querySelectorAll('.menu-category').forEach(div => {
                    div.style.opacity = '0';
                    setTimeout(() => {
                        div.style.display = 'none';
                    }, 150);
                });

                // Reset all buttons
                document.querySelectorAll('.category-btn').forEach(button => {
                    button.classList.remove('from-blue-500', 'to-purple-600', 'text-white', 'shadow-lg');
                    button.classList.add('from-gray-100', 'to-gray-200', 'dark:from-gray-700', 'dark:to-gray-800', 'text-gray-700', 'dark:text-gray-300');
                });

                // Show selected category with fade in
                setTimeout(() => {
                    const selectedCategory = document.getElementById('category-' + category);
                    if (selectedCategory) {
                        selectedCategory.style.display = 'block';
                        selectedCategory.style.opacity = '0';
                        setTimeout(() => {
                            selectedCategory.style.opacity = '1';
                        }, 50);
                    }
                }, 150);

                // Highlight selected button
                this.classList.add('from-blue-500', 'to-purple-600', 'text-white', 'shadow-lg');
                this.classList.remove('from-gray-100', 'to-gray-200', 'dark:from-gray-700', 'dark:to-gray-800', 'text-gray-700', 'dark:text-gray-300');
            });
        });
    }

    function initializeAddItemButtons() {
        const addButtons = document.querySelectorAll('.add-item-btn');
        console.log(`🔍 تم العثور على ${addButtons.length} زر إضافة`);

        addButtons.forEach((btn, index) => {
            console.log(`🔧 تهيئة الزر ${index + 1}`);
            btn.addEventListener('click', function() {
                console.log('🖱️ تم النقر على زر إضافة');
                const itemId = this.getAttribute('data-item-id');
                const itemName = this.getAttribute('data-item-name');
                const itemPrice = this.getAttribute('data-item-price');

                console.log(`📝 بيانات العنصر: ID=${itemId}, Name=${itemName}, Price=${itemPrice}`);

                addItemToOrder(itemId, itemName, itemPrice);

                // تأثير بصري للزر
                this.classList.add('pulse-ring');
                setTimeout(() => {
                    this.classList.remove('pulse-ring');
                }, 600);
            });
        });
    }

    function addItemToOrder(itemId, itemName, itemPrice) {
        console.log(`🍽️ إضافة عنصر: ${itemName} - ${itemPrice} د.ل`);

        // تحويل السعر إلى رقم
        const price = parseFloat(itemPrice);
        if (isNaN(price)) {
            console.error(`❌ خطأ في السعر: ${itemPrice}`);
            return;
        }

        console.log(`💰 السعر المحول: ${price}`);

        // البحث عن العنصر إذا كان موجود
        const existingItemIndex = orderItems.findIndex(item => item.id == itemId);

        if (existingItemIndex !== -1) {
            // زيادة الكمية إذا كان العنصر موجود
            orderItems[existingItemIndex].quantity += 1;
            console.log(`📈 زيادة كمية ${itemName} إلى ${orderItems[existingItemIndex].quantity}`);
        } else {
            // إضافة عنصر جديد
            orderItems.push({
                id: itemId,
                name: itemName,
                price: price,
                quantity: 1
            });
            console.log(`➕ إضافة عنصر جديد: ${itemName} بسعر ${price}`);
        }

        updateOrderDisplay();
        updateOrderSummaryNew();

        // تسجيل حالة النموذج بعد الإضافة
        console.log('📝 حالة النموذج بعد إضافة العنصر:');
        console.log('- عدد العناصر في orderItems:', orderItems.length);
        const hiddenInputs = document.querySelectorAll('#order-items-container input');
        console.log('- عدد الحقول المخفية:', hiddenInputs.length);

        // إظهار تأثير بصري
        showSuccessMessage(`تم إضافة ${itemName} إلى الطلب`);
    }

    // دالة تحديث عرض الطلب
    function updateOrderDisplay() {
        const orderItemsList = document.getElementById('selected-items-container');
        const emptyMessage = document.getElementById('no-items-message');

        if (orderItems.length === 0) {
            emptyMessage.style.display = 'block';
            // إزالة جميع العناصر ما عدا رسالة السلة الفارغة
            const itemElements = orderItemsList.querySelectorAll('[id^="order-item-"]');
            itemElements.forEach(item => item.remove());
            return;
        }

        emptyMessage.style.display = 'none';

        // إزالة العناصر القديمة
        const itemElements = orderItemsList.querySelectorAll('[id^="order-item-"]');
        itemElements.forEach(item => item.remove());

        // إضافة العناصر الجديدة
        orderItems.forEach((item, index) => {
            const subtotal = item.price * item.quantity;
            const itemDiv = document.createElement('div');
            itemDiv.id = `order-item-${item.id}-${index}`;
            itemDiv.className = 'flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 mb-2';

            // إنشاء نص المكونات المستبعدة
            let excludedIngredientsText = '';
            if (item.excluded_ingredients && item.excluded_ingredients.length > 0) {
                excludedIngredientsText = `
                    <div class="text-xs text-red-600 dark:text-red-400 mt-1 flex items-center">
                        <i class="fas fa-ban ml-1"></i>
                        <span class="font-medium">بدون:</span>
                        <span class="mr-1" id="excluded-ingredients-${item.id}-${index}">جاري التحميل...</span>
                    </div>
                `;
            }

            itemDiv.innerHTML = `
                <div class="flex-1">
                    <h4 class="font-medium text-gray-800 dark:text-white text-sm">${item.name}</h4>
                    <p class="text-sm text-gray-500 dark:text-gray-400">${item.price.toFixed(2)} د.ل × ${item.quantity} = ${subtotal.toFixed(2)} د.ل</p>
                    ${excludedIngredientsText}
                </div>
                <div class="flex items-center space-x-2 space-x-reverse">
                    <button type="button" onclick="updateItemQuantity(${item.id}, ${item.quantity - 1}, ${index})"
                            class="w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-xs">
                        <i class="fas fa-minus"></i>
                    </button>
                    <span class="w-8 text-center text-sm font-bold text-gray-800 dark:text-white">${item.quantity}</span>
                    <button type="button" onclick="updateItemQuantity(${item.id}, ${item.quantity + 1}, ${index})"
                            class="w-6 h-6 bg-green-500 hover:bg-green-600 text-white rounded-full flex items-center justify-center text-xs">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button type="button" onclick="removeItemFromOrder(${index})"
                            class="w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-xs mr-2">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            orderItemsList.appendChild(itemDiv);

            // تحميل أسماء المكونات المستبعدة
            if (item.excluded_ingredients && item.excluded_ingredients.length > 0) {
                loadExcludedIngredientsNames(item.excluded_ingredients, `excluded-ingredients-${item.id}-${index}`);
            }
        });
    }

    // دالة تحميل أسماء المكونات المستبعدة
    function loadExcludedIngredientsNames(ingredientIds, elementId) {
        if (!ingredientIds || ingredientIds.length === 0) return;

        fetch('/api/ingredients')
            .then(response => response.json())
            .then(data => {
                const ingredientNames = data
                    .filter(ingredient => ingredientIds.includes(ingredient.ingredient_id))
                    .map(ingredient => ingredient.name);

                const element = document.getElementById(elementId);
                if (element) {
                    element.textContent = ingredientNames.join('، ');
                }
            })
            .catch(error => {
                console.error('Error loading ingredient names:', error);
                const element = document.getElementById(elementId);
                if (element) {
                    element.textContent = 'خطأ في التحميل';
                }
            });
    }

    // دالة تحديث كمية العنصر
    function updateItemQuantity(itemId, newQuantity) {
        if (newQuantity <= 0) {
            removeItemFromOrder(itemId);
            return;
        }

        const itemIndex = orderItems.findIndex(item => item.id == itemId);
        if (itemIndex !== -1) {
            orderItems[itemIndex].quantity = newQuantity;
            updateOrderDisplay();
            updateOrderSummaryNew();
        }
    }

    // دالة حذف عنصر من الطلب
    function removeItemFromOrder(itemId) {
        const itemIndex = orderItems.findIndex(item => item.id == itemId);
        if (itemIndex !== -1) {
            const itemName = orderItems[itemIndex].name;
            orderItems.splice(itemIndex, 1);
            updateOrderDisplay();
            updateOrderSummaryNew();
            showSuccessMessage(`تم حذف ${itemName} من الطلب`);
        }
    }

    // دوال الرسائل الجديدة - في الوسط تماماً
    function showSuccessMessage(message) {
        showCenterMessage(message, 'success');
    }

    function showErrorMessage(message) {
        showCenterMessage(message, 'error');
    }

    function showInfoMessage(message) {
        showCenterMessage(message, 'info');
    }

    function clearAllMessages() {
        // إزالة الرسائل القديمة فقط
        const oldMessages = document.querySelectorAll('.center-message');
        oldMessages.forEach(msg => {
            msg.style.opacity = '0';
            msg.style.transform = 'translate(-50%, -50%) scale(0.8)';
            setTimeout(() => {
                if (document.body.contains(msg)) {
                    document.body.removeChild(msg);
                }
            }, 300);
        });
    }

    function showCenterMessage(message, type = 'info') {
        // إزالة الرسائل السابقة
        clearAllMessages();

        // انتظار قصير
        setTimeout(() => {
            // إنشاء عنصر الرسالة
            const messageDiv = document.createElement('div');
            messageDiv.className = `center-message ${type}`;
            messageDiv.style.opacity = '0';
            messageDiv.style.transform = 'translate(-50%, -50%) scale(0.8)';

            // تحديد الأيقونة حسب النوع
            let icon = '';
            switch(type) {
                case 'success':
                    icon = '<i class="fas fa-check-circle text-2xl mb-2"></i>';
                    break;
                case 'error':
                    icon = '<i class="fas fa-exclamation-circle text-2xl mb-2"></i>';
                    break;
                case 'info':
                    icon = '<i class="fas fa-info-circle text-2xl mb-2"></i>';
                    break;
            }

            messageDiv.innerHTML = `
                <div class="flex flex-col items-center">
                    ${icon}
                    <span class="text-lg">${message}</span>
                </div>
            `;

            // إضافة للصفحة
            document.body.appendChild(messageDiv);

            // تأثير الظهور
            setTimeout(() => {
                messageDiv.style.opacity = '1';
                messageDiv.style.transform = 'translate(-50%, -50%) scale(1)';
            }, 50);

            // إخفاء تلقائي
            const hideDelay = type === 'error' ? 4000 : 3000;
            setTimeout(() => {
                messageDiv.style.opacity = '0';
                messageDiv.style.transform = 'translate(-50%, -50%) scale(0.8)';
                setTimeout(() => {
                    if (document.body.contains(messageDiv)) {
                        document.body.removeChild(messageDiv);
                    }
                }, 300);
            }, hideDelay);
        }, 100);
    }

    function increaseQuantity(itemId, itemPrice) {
        const quantityInput = document.getElementById('quantity-' + itemId);
        const currentQuantity = parseInt(quantityInput.value);
        quantityInput.value = currentQuantity + 1;
        updateItemSubtotal(itemId, itemPrice);
    }

    function decreaseQuantity(itemId, itemPrice) {
        const quantityInput = document.getElementById('quantity-' + itemId);
        const currentQuantity = parseInt(quantityInput.value);
        if (currentQuantity > 1) {
            quantityInput.value = currentQuantity - 1;
            updateItemSubtotal(itemId, itemPrice);
        }
    }

    function updateItemSubtotal(itemId, itemPrice) {
        const quantityInput = document.getElementById('quantity-' + itemId);
        const quantity = parseInt(quantityInput.value);

        if (quantity < 1) {
            quantityInput.value = 1;
            return;
        }

        const subtotalElement = document.getElementById('subtotal-' + itemId);
        const subtotal = quantity * itemPrice;
        subtotalElement.textContent = subtotal.toFixed(2) + ' د.ل';

        updateTotalAmount();
    }

    function removeItem(itemId) {
        const item = document.getElementById('item-' + itemId);

        // Add remove animation
        item.style.transform = 'translateX(100%)';
        item.style.opacity = '0';

        setTimeout(() => {
            item.remove();

            // Show "no items" message if no items left
            const remainingItems = document.querySelectorAll('[id^="item-"]');
            if (remainingItems.length === 0) {
                document.getElementById('no-items-message').style.display = 'block';
            }

            updateTotalAmount();
        }, 300);
    }

    function updateTotalAmount() {
        let subtotal = 0;

        // Calculate subtotal from all items
        document.querySelectorAll('[id^="item-"]').forEach(item => {
            const itemId = item.id.replace('item-', '');
            const quantityInput = document.getElementById('quantity-' + itemId);
            if (quantityInput) {
                const quantity = parseInt(quantityInput.value);
                const subtotalText = document.getElementById('subtotal-' + itemId).textContent;
                const itemSubtotal = parseFloat(subtotalText.replace(' د.ل', ''));
                subtotal += itemSubtotal;
            }
        });

        // Calculate tax (نفس طريقة صفحة الموظف)
        const taxRate = window.settingsRefresh ? window.settingsRefresh.getTaxRate() : {!! json_encode($taxRate ?? 15) !!};
        const tax = subtotal * (taxRate / 100);

        console.log('💰 حساب الضريبة في updateTotalAmount:');
        console.log('- المجموع الفرعي:', subtotal.toFixed(2));
        console.log('- نسبة الضريبة:', taxRate + '%');
        console.log('- مبلغ الضريبة:', tax.toFixed(2));
        const total = subtotal + tax;

        // Update display
        document.getElementById('subtotal-amount').textContent = subtotal.toFixed(2) + ' د.ل';
        document.getElementById('tax-amount').textContent = tax.toFixed(2) + ' د.ل';
        document.getElementById('total-amount').textContent = total.toFixed(2) + ' د.ل';

        // Add animation to total
        const totalElement = document.getElementById('total-amount');
        totalElement.classList.add('scale-110');
        setTimeout(() => {
            totalElement.classList.remove('scale-110');
        }, 200);
    }

    // دالة تحديث النموذج بالعناصر المختارة
    function updateFormWithOrderItems() {
        const container = document.getElementById('order-items-container');
        if (!container) return;

        // مسح الحقول القديمة
        container.innerHTML = '';

        // إضافة حقول جديدة لكل عنصر
        orderItems.forEach((item, index) => {
            // حقل ID العنصر
            const idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.name = `items[${index}][id]`;
            idInput.value = item.id;
            container.appendChild(idInput);

            // حقل الكمية
            const quantityInput = document.createElement('input');
            quantityInput.type = 'hidden';
            quantityInput.name = `items[${index}][quantity]`;
            quantityInput.value = item.quantity;
            container.appendChild(quantityInput);

            // حقل المكونات المستبعدة
            if (item.excluded_ingredients && item.excluded_ingredients.length > 0) {
                item.excluded_ingredients.forEach((ingredientId, ingredientIndex) => {
                    const excludedInput = document.createElement('input');
                    excludedInput.type = 'hidden';
                    excludedInput.name = `items[${index}][excluded_ingredients][${ingredientIndex}]`;
                    excludedInput.value = ingredientId;
                    container.appendChild(excludedInput);
                });
            }
        });

        console.log(`📝 تم تحديث النموذج بـ ${orderItems.length} عنصر`);
    }

    // دالة تحديث ملخص الطلب الجديدة
    function updateOrderSummaryNew() {
        console.log('📊 تحديث ملخص الطلب مع الضريبة...');

        const itemCount = orderItems.reduce((sum, item) => sum + item.quantity, 0);
        const subtotal = orderItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);

        // الحصول على نسبة الضريبة من الإعدادات (مثل صفحة الموظف)
        const taxRate = window.settingsRefresh ? window.settingsRefresh.getTaxRate() / 100 : {!! json_encode($taxRate ?? 15) !!} / 100;
        const taxAmount = subtotal * taxRate;
        const total = subtotal + taxAmount;

        console.log('💰 تفاصيل الحساب:');
        console.log('- المجموع الفرعي:', subtotal.toFixed(2));
        console.log('- نسبة الضريبة:', (taxRate * 100) + '%');
        console.log('- مبلغ الضريبة:', taxAmount.toFixed(2));
        console.log('- المجموع الإجمالي:', total.toFixed(2));

        // تحديث العناصر في الواجهة
        const subtotalElement = document.getElementById('subtotal-amount');
        const taxElement = document.getElementById('tax-amount');
        const totalElement = document.getElementById('total-amount');
        const totalHeaderElement = document.getElementById('total-amount-header');

        if (subtotalElement) {
            subtotalElement.textContent = subtotal.toFixed(2) + ' د.ل';
            console.log('✅ تم تحديث المجموع الفرعي');
        }
        if (taxElement) {
            taxElement.textContent = taxAmount.toFixed(2) + ' د.ل';
            console.log('✅ تم تحديث الضريبة');
        }
        if (totalElement) {
            totalElement.textContent = total.toFixed(2) + ' د.ل';
            console.log('✅ تم تحديث المجموع الإجمالي');
        }
        if (totalHeaderElement) {
            totalHeaderElement.textContent = total.toFixed(2) + ' د.ل';
            console.log('✅ تم تحديث المجموع في الرأس');
        }

        // تحديث النموذج
        updateFormWithOrderItems();

        console.log('✅ تم تحديث ملخص الطلب بنجاح');
    }

    // Export function for external use
    window.updateOrderSummary = function() {
        updateTotalAmount();
        updateOrderSummaryNew();
    };
</script>

<!-- نافذة تخصيص الطلب -->
<div id="customizeOrderModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <!-- رأس النافذة -->
        <div class="bg-gradient-to-r from-orange-500 to-red-600 p-6 rounded-t-2xl">
            <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-white" id="modalOrderItemName">تخصيص الطلب</h3>
                <button onclick="closeCustomizeOrderModal()" class="text-white hover:text-gray-200 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- محتوى النافذة -->
        <div class="p-6">
            <!-- الكمية -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الكمية</label>
                <div class="flex items-center justify-center gap-4">
                    <button onclick="changeOrderModalQuantity(-1)" class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 w-10 h-10 rounded-full flex items-center justify-center transition-colors">
                        <i class="fas fa-minus"></i>
                    </button>
                    <span id="modalOrderQuantity" class="text-2xl font-bold text-gray-800 dark:text-white min-w-[3rem] text-center">1</span>
                    <button onclick="changeOrderModalQuantity(1)" class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 w-10 h-10 rounded-full flex items-center justify-center transition-colors">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>

            <!-- المكونات المستبعدة -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    <i class="fas fa-ban text-red-500 ml-2"></i>
                    المكونات المراد استبعادها
                </label>
                <div id="orderIngredientsList" class="space-y-2 max-h-48 overflow-y-auto">
                    <!-- سيتم ملء المكونات هنا بـ JavaScript -->
                </div>
            </div>

            <!-- السعر -->
            <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="flex justify-between items-center">
                    <span class="text-lg font-medium text-gray-700 dark:text-gray-300">السعر الإجمالي:</span>
                    <span id="modalOrderTotalPrice" class="text-2xl font-bold text-orange-600">0.00 د.ل</span>
                </div>
            </div>

            <!-- أزرار العمل -->
            <div class="flex gap-3">
                <button onclick="closeCustomizeOrderModal()"
                        class="flex-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 py-3 px-4 rounded-lg font-medium transition-colors">
                    إلغاء
                </button>
                <button onclick="addCustomizedOrderItem()"
                        class="flex-1 bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white py-3 px-4 rounded-lg font-medium transition-all transform hover:scale-105">
                    <i class="fas fa-plus ml-2"></i>
                    إضافة للطلب
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// متغيرات النافذة المنبثقة للطلبات
let currentOrderModalItem = {
    id: null,
    name: '',
    price: 0,
    quantity: 1
};

// إظهار نافذة تخصيص الطلب
function showCustomizeOrderModal(itemId, itemName, itemPrice) {
    currentOrderModalItem = {
        id: itemId,
        name: itemName,
        price: parseFloat(itemPrice),
        quantity: 1
    };

    // تحديث عنوان النافذة
    document.getElementById('modalOrderItemName').textContent = `تخصيص: ${itemName}`;

    // إعادة تعيين الكمية
    document.getElementById('modalOrderQuantity').textContent = '1';

    // تحديث السعر
    updateOrderModalPrice();

    // تحميل المكونات
    loadOrderIngredients();

    // إظهار النافذة
    document.getElementById('customizeOrderModal').classList.remove('hidden');
}

// إغلاق نافذة التخصيص
function closeCustomizeOrderModal() {
    document.getElementById('customizeOrderModal').classList.add('hidden');
}

// تغيير الكمية في النافذة
function changeOrderModalQuantity(change) {
    const quantityElement = document.getElementById('modalOrderQuantity');
    let quantity = parseInt(quantityElement.textContent) + change;

    if (quantity < 1) quantity = 1;
    if (quantity > 10) quantity = 10;

    quantityElement.textContent = quantity;
    currentOrderModalItem.quantity = quantity;
    updateOrderModalPrice();
}

// تحديث السعر في النافذة
function updateOrderModalPrice() {
    const totalPrice = currentOrderModalItem.price * currentOrderModalItem.quantity;
    document.getElementById('modalOrderTotalPrice').textContent = totalPrice.toFixed(2) + ' د.ل';
}

// تحميل المكونات للطلبات
function loadOrderIngredients() {
    const ingredientsList = document.getElementById('orderIngredientsList');
    ingredientsList.innerHTML = '<div class="text-center py-4"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';

    fetch('/api/ingredients')
        .then(response => response.json())
        .then(data => {
            let html = '';
            data.forEach(ingredient => {
                html += `
                    <label class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer transition-colors">
                        <input type="checkbox"
                               class="form-checkbox h-5 w-5 text-red-500 rounded focus:ring-red-500 focus:ring-2"
                               value="${ingredient.ingredient_id}">
                        <span class="mr-3 text-gray-700 dark:text-gray-300">${ingredient.name}</span>
                        <i class="fas fa-ban text-red-400 mr-auto"></i>
                    </label>
                `;
            });
            ingredientsList.innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading ingredients:', error);
            ingredientsList.innerHTML = '<div class="text-center py-4 text-red-500">خطأ في تحميل المكونات</div>';
        });
}

// إضافة العنصر المخصص للطلب
function addCustomizedOrderItem() {
    const excludedIngredients = [];
    const checkboxes = document.querySelectorAll('#orderIngredientsList input[type="checkbox"]:checked');
    checkboxes.forEach(checkbox => {
        excludedIngredients.push(parseInt(checkbox.value));
    });

    // البحث عن العنصر إذا كان موجود
    const existingItemIndex = orderItems.findIndex(item =>
        item.id == currentOrderModalItem.id &&
        JSON.stringify(item.excluded_ingredients || []) === JSON.stringify(excludedIngredients)
    );

    if (existingItemIndex !== -1) {
        // زيادة الكمية إذا كان العنصر موجود بنفس المكونات المستبعدة
        orderItems[existingItemIndex].quantity += currentOrderModalItem.quantity;
    } else {
        // إضافة عنصر جديد
        orderItems.push({
            id: currentOrderModalItem.id,
            name: currentOrderModalItem.name,
            price: currentOrderModalItem.price,
            quantity: currentOrderModalItem.quantity,
            excluded_ingredients: excludedIngredients
        });
    }

    updateOrderDisplay();
    updateOrderSummaryNew();

    // إغلاق النافذة
    closeCustomizeOrderModal();

    // إظهار رسالة نجاح
    showSuccessMessage(`تم إضافة ${currentOrderModalItem.name} المخصص إلى الطلب`);
}

// تحديث معالج أزرار التخصيص
document.addEventListener('DOMContentLoaded', function() {
    // إضافة معالج لأزرار التخصيص
    document.addEventListener('click', function(e) {
        if (e.target.closest('.customize-item-btn')) {
            const btn = e.target.closest('.customize-item-btn');
            const itemId = btn.dataset.id;
            const itemName = btn.dataset.name;
            const itemPrice = btn.dataset.price;

            showCustomizeOrderModal(itemId, itemName, itemPrice);
        }
    });
});
</script>
@endsection
