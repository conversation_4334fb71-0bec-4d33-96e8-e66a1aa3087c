@extends('auth.layouts.auth')

@section('title', 'اختبار الحسابات الاجتماعية')

@section('content')
<div class="w-full max-w-2xl mx-auto">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-primary mb-2">اختبار OAuth</h1>
            <p class="text-gray-600 dark:text-gray-400">اختبار تسجيل الدخول بالحسابات الاجتماعية</p>
        </div>

        <!-- حالة الإعدادات -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-white">حالة الإعدادات</h2>
            <div class="space-y-3">
                <!-- Google -->
                <div class="flex items-center justify-between p-3 border rounded-lg {{ config('services.google.client_id') && config('services.google.client_id') !== 'your_google_client_id' ? 'border-green-300 bg-green-50' : 'border-red-300 bg-red-50' }}">
                    <div class="flex items-center">
                        <svg class="w-6 h-6 mr-3" viewBox="0 0 24 24">
                            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        <span class="font-medium">Google OAuth</span>
                    </div>
                    <span class="px-3 py-1 rounded-full text-sm {{ config('services.google.client_id') && config('services.google.client_id') !== 'your_google_client_id' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        {{ config('services.google.client_id') && config('services.google.client_id') !== 'your_google_client_id' ? 'مُعد' : 'غير مُعد' }}
                    </span>
                </div>

                <!-- Facebook -->
                <div class="flex items-center justify-between p-3 border rounded-lg {{ config('services.facebook.client_id') && config('services.facebook.client_id') !== 'your_facebook_app_id' ? 'border-green-300 bg-green-50' : 'border-red-300 bg-red-50' }}">
                    <div class="flex items-center">
                        <svg class="w-6 h-6 mr-3" fill="#1877F2" viewBox="0 0 24 24">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                        <span class="font-medium">Facebook OAuth</span>
                    </div>
                    <span class="px-3 py-1 rounded-full text-sm {{ config('services.facebook.client_id') && config('services.facebook.client_id') !== 'your_facebook_app_id' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        {{ config('services.facebook.client_id') && config('services.facebook.client_id') !== 'your_facebook_app_id' ? 'مُعد' : 'غير مُعد' }}
                    </span>
                </div>

                <!-- Apple -->
                <div class="flex items-center justify-between p-3 border rounded-lg {{ config('services.apple.client_id') && config('services.apple.client_id') !== 'your_apple_service_id' ? 'border-green-300 bg-green-50' : 'border-red-300 bg-red-50' }}">
                    <div class="flex items-center">
                        <svg class="w-6 h-6 mr-3" fill="#000000" viewBox="0 0 24 24">
                            <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                        </svg>
                        <span class="font-medium">Apple OAuth</span>
                    </div>
                    <span class="px-3 py-1 rounded-full text-sm {{ config('services.apple.client_id') && config('services.apple.client_id') !== 'your_apple_service_id' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        {{ config('services.apple.client_id') && config('services.apple.client_id') !== 'your_apple_service_id' ? 'مُعد' : 'غير مُعد' }}
                    </span>
                </div>
            </div>
        </div>

        <!-- أزرار الاختبار -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-white">اختبار التسجيل</h2>
            <div class="space-y-3">
                <!-- Google Test -->
                <a href="{{ route('auth.google') }}" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 text-gray-700 transition duration-200 group">
                    <svg class="w-5 h-5 ml-2" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    اختبار Google OAuth
                </a>

                <!-- Facebook Test -->
                <a href="{{ route('auth.facebook') }}" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 text-gray-700 transition duration-200 group">
                    <svg class="w-5 h-5 ml-2" fill="#1877F2" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                    اختبار Facebook OAuth
                </a>

                <!-- Apple Test -->
                <a href="{{ route('auth.apple') }}" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 text-gray-700 transition duration-200 group">
                    <svg class="w-5 h-5 ml-2" fill="#000000" viewBox="0 0 24 24">
                        <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                    </svg>
                    اختبار Apple OAuth
                </a>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
            <h3 class="font-semibold text-blue-800 dark:text-blue-200 mb-2">ملاحظات:</h3>
            <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                <li>• يجب إعداد مفاتيح OAuth في ملف .env أولاً</li>
                <li>• راجع ملف OAUTH_SETUP_GUIDE.md للتعليمات التفصيلية</li>
                <li>• في حالة الخطأ، تحقق من صحة redirect URIs</li>
            </ul>
        </div>

        <div class="mt-6 text-center">
            <a href="{{ route('login') }}" class="text-primary hover:underline">العودة لصفحة تسجيل الدخول</a>
        </div>
    </div>
</div>
@endsection
