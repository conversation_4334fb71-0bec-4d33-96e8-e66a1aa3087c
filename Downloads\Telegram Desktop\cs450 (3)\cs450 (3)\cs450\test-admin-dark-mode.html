<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر الوضع المظلم - صفحة الأدمن</title>
    
    <!-- تطبيق الوضع المظلم فوراً لمنع الوميض -->
    <script>
        (function() {
            const savedTheme = localStorage.getItem('darkMode');
            console.log('🔍 الثيم المحفوظ:', savedTheme);
            
            if (savedTheme === 'true' || savedTheme === 'enabled') {
                document.documentElement.classList.add('dark');
                console.log('🌙 تم تطبيق الوضع المظلم من الإعدادات المحفوظة');
            } else if (savedTheme === 'false' || savedTheme === 'disabled') {
                document.documentElement.classList.remove('dark');
                console.log('☀️ تم تطبيق الوضع الفاتح من الإعدادات المحفوظة');
            } else {
                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                    document.documentElement.classList.add('dark');
                    localStorage.setItem('darkMode', 'true');
                    console.log('🌙 تم تطبيق الوضع المظلم من إعدادات النظام');
                } else {
                    document.documentElement.classList.remove('dark');
                    localStorage.setItem('darkMode', 'false');
                    console.log('☀️ تم تطبيق الوضع الفاتح كافتراضي');
                }
            }
        })();
    </script>
    
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class'
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100 dark:bg-gray-900 min-h-screen">
    <!-- محاكاة header الأدمن -->
    <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 z-40 relative">
        <div class="px-6 py-4 flex justify-between items-center">
            <!-- الجانب الأيسر -->
            <div class="flex items-center space-x-4 space-x-reverse">
                <h1 class="text-xl font-bold text-gray-900 dark:text-white">
                    اختبار صفحة الأدمن
                </h1>
            </div>
            
            <!-- الجانب الأيمن -->
            <div class="flex items-center space-x-4 space-x-reverse">
                <!-- زر تبديل الثيم - نفس الكود من header الأصلي -->
                <button id="darkModeToggle" data-theme-toggle class="theme-toggle p-3 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group" title="تبديل الثيم">
                    <i id="darkModeIcon" class="theme-icon fas fa-moon group-hover:rotate-180 transition-transform duration-500"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="p-8">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                    اختبار زر الوضع المظلم
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- معلومات الوضع الحالي -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                            <i class="fas fa-info-circle mr-2 text-blue-500"></i>
                            معلومات الوضع الحالي
                        </h3>
                        <div id="currentModeInfo" class="text-sm text-gray-600 dark:text-gray-300 space-y-2">
                            جاري التحميل...
                        </div>
                    </div>
                    
                    <!-- أزرار الاختبار -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                            <i class="fas fa-cogs mr-2 text-green-500"></i>
                            أزرار الاختبار
                        </h3>
                        <div class="space-y-3">
                            <button onclick="toggleUnifiedDarkMode()" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-exchange-alt mr-2"></i>
                                تبديل الوضع (دالة مباشرة)
                            </button>
                            
                            <button onclick="diagnoseDarkModeSystem()" class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-search mr-2"></i>
                                تشخيص النظام
                            </button>
                            
                            <button onclick="location.reload()" class="w-full bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-refresh mr-2"></i>
                                إعادة تحميل الصفحة
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات localStorage -->
                <div class="mt-6 bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
                    <h3 class="text-lg font-semibold mb-3 text-yellow-800 dark:text-yellow-200">
                        <i class="fas fa-database mr-2"></i>
                        محتويات localStorage
                    </h3>
                    <div id="storageContent" class="text-sm font-mono text-yellow-700 dark:text-yellow-300">
                        جاري التحميل...
                    </div>
                </div>
                
                <!-- سجل الأحداث -->
                <div class="mt-6 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                        <i class="fas fa-list mr-2 text-purple-500"></i>
                        سجل الأحداث
                    </h3>
                    <div id="eventLog" class="text-sm text-gray-600 dark:text-gray-300 max-h-40 overflow-y-auto bg-white dark:bg-gray-800 p-3 rounded border">
                        جاري التحميل...
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- النظام الموحد للوضع المظلم -->
    <script src="js/unified-dark-mode.js"></script>
    
    <script>
        let eventLogEntries = [];
        
        // تحديث معلومات الوضع
        function updateModeInfo() {
            const isDark = document.documentElement.classList.contains('dark');
            const savedMode = localStorage.getItem('darkMode');
            
            document.getElementById('currentModeInfo').innerHTML = `
                <div><strong>الوضع الحالي:</strong> ${isDark ? 'مظلم 🌙' : 'فاتح ☀️'}</div>
                <div><strong>القيمة المحفوظة:</strong> ${savedMode || 'غير محدد'}</div>
                <div><strong>وقت التحديث:</strong> ${new Date().toLocaleTimeString('ar')}</div>
                <div><strong>CSS Class:</strong> ${document.documentElement.className || 'فارغ'}</div>
            `;
            
            // تحديث محتويات localStorage
            const storageKeys = ['darkMode', 'theme_preference', 'effective_theme'];
            const storageInfo = storageKeys.map(key => 
                `${key}: "${localStorage.getItem(key) || 'غير موجود'}"`
            ).join('<br>');
            
            document.getElementById('storageContent').innerHTML = storageInfo;
        }
        
        // إضافة حدث إلى السجل
        function addToEventLog(message) {
            const timestamp = new Date().toLocaleTimeString('ar');
            eventLogEntries.unshift(`[${timestamp}] ${message}`);
            
            // الاحتفاظ بآخر 10 أحداث فقط
            if (eventLogEntries.length > 10) {
                eventLogEntries = eventLogEntries.slice(0, 10);
            }
            
            document.getElementById('eventLog').innerHTML = eventLogEntries.join('<br>');
        }
        
        // مراقبة تغييرات الوضع المظلم
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const isDark = document.documentElement.classList.contains('dark');
                    addToEventLog(`تم تغيير الوضع إلى: ${isDark ? 'مظلم' : 'فاتح'}`);
                    updateModeInfo();
                }
            });
        });
        
        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['class']
        });
        
        // تحديث المعلومات عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            addToEventLog('تم تحميل الصفحة');
            updateModeInfo();
            
            // تحديث المعلومات كل ثانيتين
            setInterval(updateModeInfo, 2000);
        });
        
        // مراقبة النقرات على الزر
        document.addEventListener('click', function(e) {
            if (e.target.closest('#darkModeToggle')) {
                addToEventLog('تم النقر على زر تبديل الوضع المظلم');
            }
        });
    </script>
</body>
</html>
