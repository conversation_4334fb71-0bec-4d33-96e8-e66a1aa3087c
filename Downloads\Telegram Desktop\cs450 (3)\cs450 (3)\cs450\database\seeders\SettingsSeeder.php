<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // إعدادات عامة
            ['key' => 'restaurant_name', 'value' => 'Eat Hub', 'group' => 'general'],
            ['key' => 'restaurant_phone', 'value' => '+966123456789', 'group' => 'general'],
            ['key' => 'restaurant_email', 'value' => '<EMAIL>', 'group' => 'general'],
            ['key' => 'restaurant_address', 'value' => 'الرياض، المملكة العربية السعودية', 'group' => 'general'],
            
            // إعدادات الضرائب والرسوم
            ['key' => 'tax_rate', 'value' => '15', 'group' => 'tax'],
            ['key' => 'service_fee', 'value' => '0', 'group' => 'tax'],
            ['key' => 'include_tax_in_price', 'value' => 'لا', 'group' => 'tax'],
            
            // إعدادات النظام
            ['key' => 'currency', 'value' => 'د.ل', 'group' => 'system'],
            ['key' => 'timezone', 'value' => 'Asia/Riyadh', 'group' => 'system'],
            ['key' => 'date_format', 'value' => 'Y-m-d', 'group' => 'system'],
            
            // إعدادات إضافية
            ['key' => 'delivery_fee', 'value' => '10', 'group' => 'delivery'],
            ['key' => 'free_delivery_minimum', 'value' => '100', 'group' => 'delivery'],
            ['key' => 'max_delivery_distance', 'value' => '20', 'group' => 'delivery'],
            
            // إعدادات الطلبات
            ['key' => 'min_order_amount', 'value' => '25', 'group' => 'orders'],
            ['key' => 'max_order_items', 'value' => '50', 'group' => 'orders'],
            ['key' => 'order_preparation_time', 'value' => '30', 'group' => 'orders'],
            
            // إعدادات الدفع
            ['key' => 'accept_cash', 'value' => 'نعم', 'group' => 'payment'],
            ['key' => 'accept_card', 'value' => 'نعم', 'group' => 'payment'],
            ['key' => 'accept_online', 'value' => 'نعم', 'group' => 'payment'],
            
            // إعدادات الإشعارات
            ['key' => 'email_notifications', 'value' => 'نعم', 'group' => 'notifications'],
            ['key' => 'sms_notifications', 'value' => 'لا', 'group' => 'notifications'],
            ['key' => 'push_notifications', 'value' => 'نعم', 'group' => 'notifications'],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                [
                    'value' => $setting['value'],
                    'group' => $setting['group']
                ]
            );
        }
    }
}
