// نظام الإعدادات الديناميكية
class DynamicSettings {
    constructor() {
        this.settings = {};
        this.loadSettings();
        this.setupEventListeners();
    }

    // تحميل الإعدادات من الخادم
    async loadSettings() {
        try {
            const response = await fetch('/api/settings');
            if (response.ok) {
                this.settings = await response.json();
                this.updateUI();
            }
        } catch (error) {
            console.error('خطأ في تحميل الإعدادات:', error);
        }
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // مراقبة تغييرات الإعدادات كل 5 ثوان
        setInterval(() => {
            this.loadSettings();
        }, 5000);

        // مراقبة تحديث الصفحة
        window.addEventListener('focus', () => {
            this.loadSettings();
        });
    }

    // تحديث واجهة المستخدم
    updateUI() {
        // تحديث نسبة الضريبة في النصوص
        const taxElements = document.querySelectorAll('[data-tax-rate]');
        taxElements.forEach(element => {
            const currentText = element.textContent;
            const newText = currentText.replace(/\d+%/, `${this.settings.tax_rate}%`);
            element.textContent = newText;
        });

        // تحديث الحسابات
        this.recalculateAmounts();
    }

    // إعادة حساب المبالغ
    recalculateAmounts() {
        // تحديث حسابات السلة
        if (typeof updateCartSummary === 'function') {
            updateCartSummary();
        }

        // تحديث حسابات الطلبات
        if (typeof updateOrderSummary === 'function') {
            updateOrderSummary();
        }

        // تحديث أي حسابات أخرى
        this.updateAllCalculations();
    }

    // تحديث جميع الحسابات في الصفحة
    updateAllCalculations() {
        // البحث عن عناصر المجموع الفرعي
        const subtotalElements = document.querySelectorAll('[data-subtotal]');
        subtotalElements.forEach(element => {
            const subtotal = parseFloat(element.dataset.subtotal) || 0;
            const tax = this.calculateTax(subtotal);
            const total = subtotal + tax;

            // تحديث عنصر الضريبة
            const taxElement = element.parentElement.querySelector('[data-tax-amount]');
            if (taxElement) {
                taxElement.textContent = this.formatCurrency(tax);
            }

            // تحديث عنصر المجموع
            const totalElement = element.parentElement.querySelector('[data-total-amount]');
            if (totalElement) {
                totalElement.textContent = this.formatCurrency(total);
            }
        });
    }

    // حساب الضريبة
    calculateTax(amount) {
        const taxRate = parseFloat(this.settings.tax_rate) || 15;
        return amount * (taxRate / 100);
    }

    // حساب رسوم الخدمة
    calculateServiceFee(amount) {
        const serviceFee = parseFloat(this.settings.service_fee) || 0;
        return amount * (serviceFee / 100);
    }

    // حساب المجموع الكلي
    calculateTotal(subtotal, includeTax = true, includeServiceFee = true) {
        let total = subtotal;
        
        if (includeTax) {
            total += this.calculateTax(subtotal);
        }
        
        if (includeServiceFee) {
            total += this.calculateServiceFee(subtotal);
        }
        
        return total;
    }

    // تنسيق العملة
    formatCurrency(amount, decimals = 2) {
        const currency = this.settings.currency || 'د.ل';
        return `${amount.toFixed(decimals)} ${currency}`;
    }

    // الحصول على إعداد معين
    get(key, defaultValue = null) {
        return this.settings[key] || defaultValue;
    }

    // تحديث إعداد معين
    set(key, value) {
        this.settings[key] = value;
        this.updateUI();
    }
}

// إنشاء مثيل عام
window.dynamicSettings = new DynamicSettings();

// دوال مساعدة عامة
window.getTaxRate = function() {
    return parseFloat(window.dynamicSettings.get('tax_rate', 15));
};

window.calculateTax = function(amount) {
    return window.dynamicSettings.calculateTax(amount);
};

window.calculateTotal = function(subtotal, includeTax = true, includeServiceFee = true) {
    return window.dynamicSettings.calculateTotal(subtotal, includeTax, includeServiceFee);
};

window.formatCurrency = function(amount, decimals = 2) {
    return window.dynamicSettings.formatCurrency(amount, decimals);
};

// تحديث الحسابات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        window.dynamicSettings.updateAllCalculations();
    }, 1000);
});

console.log('✅ نظام الإعدادات الديناميكية تم تحميله بنجاح');
