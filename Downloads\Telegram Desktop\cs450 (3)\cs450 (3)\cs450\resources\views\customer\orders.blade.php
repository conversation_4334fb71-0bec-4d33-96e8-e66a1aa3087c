@extends('customer.layouts.simple')

@section('title', 'طلباتي - Eat Hub')

@section('content')
<!-- المحتوى الرئيسي -->
<main class="min-h-screen pt-20">
    <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Unified User Header -->
    @include('customer.partials.user-header', [
        'title' => 'طلباتي',
        'showStats' => false
    ])

    <div class="container mx-auto px-4 pb-8">
        <div class="max-w-6xl mx-auto">
            <!-- عنوان الصفحة -->
            <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2">طلباتي</h1>
                <p class="text-gray-600 dark:text-gray-400">تتبع جميع طلباتك السابقة والحالية</p>
            </div>
            <a href="{{ route('customer.menu') }}" class="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition">
                <i class="fas fa-plus ml-2"></i>
                طلب جديد
            </a>
        </div>

        <!-- فلاتر الطلبات -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <div class="flex flex-wrap gap-4 items-center">
                <div class="flex space-x-2 space-x-reverse">
                    <a href="{{ route('customer.orders') }}" class="order-filter-btn {{ $status == 'all' ? 'active bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white' }} px-4 py-2 rounded-full transition text-sm">
                        جميع الطلبات
                    </a>
                    <a href="{{ route('customer.orders', ['status' => 'pending']) }}" class="order-filter-btn {{ $status == 'pending' ? 'active bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white' }} px-4 py-2 rounded-full transition text-sm">
                        قيد التجهيز
                    </a>
                    <a href="{{ route('customer.orders', ['status' => 'completed']) }}" class="order-filter-btn {{ $status == 'completed' ? 'active bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white' }} px-4 py-2 rounded-full transition text-sm">
                        مكتملة
                    </a>
                    <a href="{{ route('customer.orders', ['status' => 'cancelled']) }}" class="order-filter-btn {{ $status == 'cancelled' ? 'active bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white' }} px-4 py-2 rounded-full transition text-sm">
                        ملغية
                    </a>
                </div>
            </div>
        </div>
        <style>
            /* توحيد العرض وتوسيط جميع بطاقات الطلب أينما كانت */
            .order-card { width: 100%; max-width: 860px; margin-left: auto; margin-right: auto; display: block; }
        </style>





        <!-- قائمة الطلبات -->
        <div class="space-y-6" id="orders-container">
            @forelse($orders as $order)
            <div class="order-card bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="p-6">
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <div class="flex items-center space-x-4 space-x-reverse mb-2">
                                <h3 class="text-lg font-bold text-gray-800 dark:text-white">{{ $order->order_number }}</h3>
                                @if($order->status == 'completed')
                                    <span class="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full text-sm">مكتمل</span>
                                @elseif($order->status == 'processing' || $order->status == 'pending')
                                    <span class="px-3 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 rounded-full text-sm">قيد التجهيز</span>
                                @elseif($order->status == 'cancelled')
                                    <span class="px-3 py-1 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-full text-sm">ملغي</span>
                                @else
                                    <span class="px-3 py-1 bg-gray-100 dark:bg-gray-900/30 text-gray-600 dark:text-gray-400 rounded-full text-sm">{{ $order->status }}</span>
                                @endif
                            </div>
                            @if(is_object($order->created_at) && method_exists($order->created_at, 'format'))
                                <p class="text-gray-600 dark:text-gray-400 text-sm">تم الطلب في {{ $order->created_at->format('d M Y - H:i') }}</p>
                            @else
                                <p class="text-gray-600 dark:text-gray-400 text-sm">تم الطلب في {{ $order->created_at }}</p>
                            @endif
                        </div>
                        <div class="text-right">
                            <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ number_format($order->total_amount, 2) }} د.ل</p>
                            @if(isset($order->items) && $order->items->count() > 0)
                                <p class="text-gray-600 dark:text-gray-400 text-sm">{{ $order->items->count() }} عناصر</p>
                            @elseif(isset($order->orderItems) && $order->orderItems->count() > 0)
                                <p class="text-gray-600 dark:text-gray-400 text-sm">{{ $order->orderItems->count() }} عناصر</p>
                            @endif
                        </div>
                    </div>
                        <!-- ضمان مساحة موحدة لشريط التقدم -->
                        <div class="section-progress hidden"></div>


                    @if($order->status == 'processing' || $order->status == 'pending')
                    <!-- شريط التقدم -->
                    <div class="mb-4">
                        <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                            <span>تم استلام الطلب</span>
                            <span>قيد التجهيز</span>
                            <span>جاهز للتسليم</span>
                            <span>تم التسليم</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div class="bg-primary h-2 rounded-full" style="width: 50%"></div>
                        </div>
                    </div>
                    @endif

                    <!-- عناصر الطلب -->
                    @if((isset($order->items) && $order->items->count() > 0) || (isset($order->orderItems) && $order->orderItems->count() > 0))
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                        <div class="space-y-3 items-preview">
                            @if(isset($order->items) && $order->items->count() > 0)
                                @foreach($order->items as $item)
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-md flex items-center justify-center ml-3">
                                            @if($item->menuItem && $item->menuItem->image_url)
                                                <img src="{{ $item->menuItem->image_url }}"
                                                     alt="{{ $item->menuItem->name }}"
                                                     class="w-full h-full object-cover rounded-md">
                                            @else
                                                <i class="fas fa-utensils text-gray-400"></i>
                                            @endif
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-800 dark:text-white">{{ $item->menuItem->name ?? 'عنصر محذوف' }}</p>
                                            <p class="text-gray-600 dark:text-gray-400 text-sm">الكمية: {{ $item->quantity }} × {{ number_format($item->price, 2) }} د.ل</p>
                                            @if($item->excluded_ingredients && count($item->excluded_ingredients) > 0)
                                            <div class="text-xs text-red-600 dark:text-red-400 mt-1 flex items-center">
                                                <i class="fas fa-ban ml-1"></i>
                                                <span class="font-medium">بدون:</span>
                                                @php
                                                    $excludedNames = \App\Models\Ingredient::whereIn('ingredient_id', $item->excluded_ingredients)->pluck('name')->toArray();
                                                @endphp
                                                <span class="mr-1">{{ implode('، ', $excludedNames) }}</span>
                                            </div>
                                            @endif
                                        </div>
                                    </div>
                                    <span class="font-medium text-gray-800 dark:text-white">{{ number_format($item->price * $item->quantity, 2) }} د.ل</span>
                                </div>
                                @endforeach
                            @elseif(isset($order->orderItems) && $order->orderItems->count() > 0)
                                @foreach($order->orderItems as $item)
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img src="{{ $item->menuItem->image_url ?? 'https://images.unsplash.com/photo-1513104890138-7c749659a591' }}?ixlib=rb-1.2.1&auto=format&fit=crop&w=60&q=80" alt="{{ $item->menuItem->name }}" class="w-12 h-12 object-cover rounded-md ml-3">
                                        <div>
                                            <p class="font-medium text-gray-800 dark:text-white">{{ $item->menuItem->name }}</p>
                                            <p class="text-gray-600 dark:text-gray-400 text-sm">الكمية: {{ $item->quantity }} × {{ number_format($item->price, 2) }} د.ل</p>
                                        </div>
                                    </div>
                                    <span class="font-medium text-gray-800 dark:text-white">{{ number_format($item->price * $item->quantity, 2) }} د.ل</span>
                                </div>
                                @endforeach
                            @endif
                        </div>
                    </div>
                    @endif

                    <!-- إجراءات الطلب -->
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
                        <div class="flex justify-between items-center">
                            <div class="flex space-x-3 space-x-reverse">
                                <a href="{{ route('customer.orders.show', $order->order_id) }}" class="text-primary hover:text-primary/80 text-sm">
                                    <i class="fas fa-eye ml-1"></i>عرض التفاصيل
                                </a>
                                <!-- أزرار متاحة لجميع الطلبات -->
                                <a href="{{ route('customer.orders.invoice', $order->order_id) }}" class="text-primary hover:text-primary/80 text-sm">
                                    <i class="fas fa-download ml-1"></i>عرض الفاتورة
                                </a>

                                @if($order->status == 'completed')
                                    <button onclick="reorderOrder('{{ $order->order_id }}')" class="text-primary hover:text-primary/80 text-sm">
                                        <i class="fas fa-redo ml-1"></i>إعادة الطلب
                                    </button>
                                @elseif(in_array($order->status, ['pending', 'preparing']))
                                    <a href="{{ route('customer.orders.show', $order->order_id) }}" class="text-primary hover:text-primary/80 text-sm">
                                        <i class="fas fa-eye ml-1"></i>تتبع الطلب
                                    </a>
                                    <a href="{{ route('customer.orders.cancel.page', $order->order_id) }}" class="text-red-600 hover:text-red-700 text-sm">
                                        <i class="fas fa-times ml-1"></i>إلغاء الطلب
                                    </a>
                                @elseif($order->status == 'canceled')
                                    <button onclick="reorderOrder('{{ $order->order_id }}')" class="text-primary hover:text-primary/80 text-sm">
                                        <i class="fas fa-redo ml-1"></i>إعادة الطلب
                                    </button>
                                @endif
                            </div>
                            @if($order->status == 'completed')
                                @if($order->review)
                                    <!-- عرض التقييم الموجود -->
                                    <div class="flex items-center">
                                        <span class="text-gray-600 dark:text-gray-400 text-sm ml-2">تقييمك:</span>
                                        <div class="flex text-yellow-400">
                                            @for($i = 1; $i <= 5; $i++)
                                                <i class="fas fa-star {{ $i <= $order->review->rating ? 'text-yellow-400' : 'text-gray-300' }}"></i>
                                            @endfor
                                        </div>
                                        <button onclick="editReview('{{ $order->order_id }}')" class="text-primary hover:text-primary/80 text-sm mr-3">
                                            <i class="fas fa-edit ml-1"></i>تعديل التقييم
                                        </button>
                                    </div>
                                @else
                                    <!-- زر إضافة تقييم -->
                                    <button onclick="addReview('{{ $order->order_id }}')" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm transition">
                                        <i class="fas fa-star ml-1"></i>قيم الطلب
                                    </button>
                                @endif
                            </div>
                            @elseif($order->status == 'processing' || $order->status == 'pending')
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                <i class="fas fa-clock ml-1"></i>
                                الوقت المتبقي: 25 دقيقة
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            @empty
            <!-- رسالة عدم وجود طلبات -->
            <div class="text-center py-12">
                <div class="w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-shopping-bag text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">لا توجد طلبات</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-6">لم تقم بأي طلبات بعد</p>
                <a href="{{ route('customer.menu') }}" class="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition">
                    <i class="fas fa-plus ml-2"></i>
                    اطلب الآن
                </a>
            </div>
            @endforelse
        </div>

        <!-- Pagination -->
        @if(method_exists($orders, 'links'))
        <div class="mt-8">
            {{ $orders->links() }}
        </div>
        @endif
    </div>
</div>
</main>
@endsection

@push('scripts')
<script>
// إلغاء الطلب
function cancelOrder(orderId, orderNumber) {
    // التوجه إلى صفحة إلغاء الطلب
    window.location.href = `/customer/orders/${orderId}/cancel`;
}

// إعادة الطلب
function reorderOrder(orderId) {
    // إرسال طلب إعادة الطلب مباشرة بدون تأكيد
    fetch(`/customer/orders/${orderId}/reorder`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم إضافة العناصر إلى السلة بنجاح', 'success');
            // التوجه إلى السلة
            setTimeout(() => {
                window.location.href = '/customer/cart';
            }, 1500);
        } else {
            showNotification('حدث خطأ أثناء إعادة الطلب', 'error');
        }
    })
    .catch(error => {
        showNotification('حدث خطأ في الاتصال', 'error');
    });
}

// تحميل الفاتورة
function downloadInvoice(orderId) {
    window.open(`/customer/orders/${orderId}/invoice`, '_blank');
}

// عرض الإشعارات
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full`;

    // تحديد لون الإشعار حسب النوع
    if (type === 'success') {
        notification.className += ' bg-green-500 text-white';
    } else if (type === 'error') {
        notification.className += ' bg-red-500 text-white';
    } else if (type === 'warning') {
        notification.className += ' bg-yellow-500 text-white';
    } else {
        notification.className += ' bg-blue-500 text-white';
    }

    notification.innerHTML = `
        <div class="flex items-center">
            <span class="flex-1">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="mr-2 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);

    // عرض الإشعار
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // إخفاء الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }, 5000);
}

// وظائف التقييم
function addReview(orderId) {
    showReviewModal(orderId);
}

function editReview(orderId) {
    showReviewModal(orderId, true);
}

function showReviewModal(orderId, isEdit = false) {
    const modalHtml = `
        <div id="reviewModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">
                        ${isEdit ? 'تعديل التقييم' : 'تقييم الطلب'}
                    </h3>
                    <button onclick="closeReviewModal()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form id="reviewForm" onsubmit="submitOrderReview(event, '${orderId}', ${isEdit})"
                    <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
                    <input type="hidden" name="order_id" value="${orderId}">
                    ${isEdit ? '<input type="hidden" name="_method" value="PUT">' : ''}

                    <div class="mb-4">
                        <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2">
                            التقييم
                        </label>
                        <div class="flex justify-center space-x-1 space-x-reverse mb-2">
                            ${[1,2,3,4,5].map(i => `
                                <button type="button" class="review-star text-2xl text-gray-300 hover:text-yellow-400 transition" data-rating="${i}">
                                    <i class="fas fa-star"></i>
                                </button>
                            `).join('')}
                        </div>
                        <input type="hidden" name="rating" id="selectedRating" required>
                        <p id="ratingText" class="text-center text-sm text-gray-500">اختر تقييمك</p>
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2">
                            التعليق (اختياري)
                        </label>
                        <textarea name="comment" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700 dark:text-white" placeholder="شاركنا رأيك في الطلب..."></textarea>
                    </div>

                    <div class="flex justify-end space-x-2 space-x-reverse">
                        <button type="button" onclick="closeReviewModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition">
                            إلغاء
                        </button>
                        <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition">
                            ${isEdit ? 'تحديث التقييم' : 'إرسال التقييم'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // إعداد نجوم التقييم
    setupReviewStars();
}

function setupReviewStars() {
    const stars = document.querySelectorAll('.review-star');
    const ratingInput = document.getElementById('selectedRating');
    const ratingText = document.getElementById('ratingText');

    const ratingTexts = {
        1: 'ضعيف جداً ⭐',
        2: 'ضعيف ⭐⭐',
        3: 'متوسط ⭐⭐⭐',
        4: 'جيد ⭐⭐⭐⭐',
        5: 'ممتاز ⭐⭐⭐⭐⭐'
    };

    stars.forEach(star => {
        star.addEventListener('click', function() {
            const rating = parseInt(this.dataset.rating);
            ratingInput.value = rating;
            ratingText.textContent = ratingTexts[rating];
            ratingText.classList.remove('text-gray-500');
            ratingText.classList.add('text-primary', 'font-medium');

            // تحديث النجوم
            stars.forEach((s, index) => {
                const icon = s.querySelector('i');
                if (index < rating) {
                    icon.classList.remove('text-gray-300');
                    icon.classList.add('text-yellow-400');
                } else {
                    icon.classList.remove('text-yellow-400');
                    icon.classList.add('text-gray-300');
                }
            });
        });

        star.addEventListener('mouseenter', function() {
            const rating = parseInt(this.dataset.rating);
            stars.forEach((s, index) => {
                const icon = s.querySelector('i');
                if (index < rating) {
                    icon.classList.add('text-yellow-400');
                } else {
                    icon.classList.remove('text-yellow-400');
                }
            });
        });
    });

    document.querySelector('.review-star').parentElement.addEventListener('mouseleave', function() {
        const currentRating = parseInt(ratingInput.value) || 0;
        stars.forEach((s, index) => {
            const icon = s.querySelector('i');
            if (index < currentRating) {
                icon.classList.add('text-yellow-400');
            } else {
                icon.classList.remove('text-yellow-400');
            }
        });
    });
}

function submitOrderReview(event, orderId, isEdit) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);

    // التحقق من وجود التقييم
    if (!formData.get('rating')) {
        showNotification('يرجى اختيار تقييم', 'warning');
        return;
    }

    showNotification('جاري إرسال التقييم...', 'info');

    const url = isEdit ? `/customer/reviews/${orderId}` : '/customer/reviews/store';
    const method = isEdit ? 'PUT' : 'POST';

    // إضافة CSRF token
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

    if (isEdit) {
        formData.append('_method', 'PUT');
    }

    fetch(url, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message || 'تم إرسال التقييم بنجاح', 'success');
            closeReviewModal();
            // إعادة تحميل الصفحة لإظهار التقييم الجديد
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification(data.message || 'حدث خطأ أثناء إرسال التقييم', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('حدث خطأ أثناء إرسال التقييم', 'error');
    });
}

function closeReviewModal() {
    const modal = document.getElementById('reviewModal');
    if (modal) {
        modal.remove();
    }
}
</script>
@endpush
