@extends('employee.layouts.app')

@section('title', 'إدارة العروض')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">إدارة العروض</h1>
            <p class="text-gray-600 dark:text-gray-400">إدارة وتتبع جميع العروض والخصومات</p>
        </div>
        <div class="mt-4 lg:mt-0">
            <a href="{{ route('employee.offers.create') }}" 
               class="inline-flex items-center px-6 py-3 bg-primary hover:bg-primary/90 text-white font-semibold rounded-lg transition-colors">
                <i class="fas fa-plus ml-2"></i>
                إضافة عرض جديد
            </a>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30">
                    <i class="fas fa-tags text-blue-600 dark:text-blue-400"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي العروض</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $stats['total'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/30">
                    <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">العروض النشطة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $stats['active'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30">
                    <i class="fas fa-clock text-yellow-600 dark:text-yellow-400"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">العروض القادمة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $stats['upcoming'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 dark:bg-red-900/30">
                    <i class="fas fa-times-circle text-red-600 dark:text-red-400"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">العروض المنتهية</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $stats['expired'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-gray-100 dark:bg-gray-900/30">
                    <i class="fas fa-pause-circle text-gray-600 dark:text-gray-400"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">غير نشطة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $stats['inactive'] }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
        <form method="GET" action="{{ route('employee.offers.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البحث</label>
                <input type="text" name="search" value="{{ request('search') }}" 
                       placeholder="ابحث في العروض..."
                       class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع العرض</label>
                <select name="offer_type" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <option value="">جميع الأنواع</option>
                    <option value="percentage" {{ request('offer_type') == 'percentage' ? 'selected' : '' }}>نسبة مئوية</option>
                    <option value="fixed_amount" {{ request('offer_type') == 'fixed_amount' ? 'selected' : '' }}>مبلغ ثابت</option>
                    <option value="buy_get" {{ request('offer_type') == 'buy_get' ? 'selected' : '' }}>اشتري واحد واحصل على آخر</option>
                    <option value="combo" {{ request('offer_type') == 'combo' ? 'selected' : '' }}>عرض مجموعة</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحالة</label>
                <select name="status" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <option value="">جميع الحالات</option>
                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>نشط</option>
                    <option value="upcoming" {{ request('status') == 'upcoming' ? 'selected' : '' }}>قادم</option>
                    <option value="expired" {{ request('status') == 'expired' ? 'selected' : '' }}>منتهي</option>
                    <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>غير نشط</option>
                </select>
            </div>

            <div class="flex items-end">
                <button type="submit" class="w-full px-4 py-2 bg-primary hover:bg-primary/90 text-white rounded-lg transition-colors">
                    <i class="fas fa-search ml-2"></i>
                    بحث
                </button>
            </div>
        </form>
    </div>

    <!-- قائمة العروض -->
    @if($offers->count() > 0)
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            @foreach($offers as $offer)
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-lg transition-shadow">
                    <!-- صورة العرض -->
                    <div class="relative h-48 bg-gray-200 dark:bg-gray-700">
                        @if($offer->image_path)
                            <img src="{{ $offer->image_url }}" alt="{{ $offer->title }}" 
                                 class="w-full h-full object-cover">
                        @else
                            <div class="w-full h-full flex items-center justify-center">
                                <i class="fas fa-image text-4xl text-gray-400"></i>
                            </div>
                        @endif
                        
                        <!-- حالة العرض -->
                        <div class="absolute top-4 right-4">
                            @php
                                $statusClasses = [
                                    'active' => 'bg-green-500 text-white',
                                    'upcoming' => 'bg-yellow-500 text-white',
                                    'expired' => 'bg-red-500 text-white',
                                    'inactive' => 'bg-gray-500 text-white'
                                ];
                            @endphp
                            <span class="px-3 py-1 rounded-full text-sm font-medium {{ $statusClasses[$offer->status] ?? 'bg-gray-500 text-white' }}">
                                {{ $offer->status_text }}
                            </span>
                        </div>

                        <!-- نوع العرض -->
                        <div class="absolute top-4 left-4">
                            <span class="px-3 py-1 bg-primary text-white rounded-full text-sm font-medium">
                                @switch($offer->offer_type)
                                    @case('percentage')
                                        نسبة مئوية
                                        @break
                                    @case('fixed_amount')
                                        مبلغ ثابت
                                        @break
                                    @case('buy_one_get_one')
                                        اشتري واحد واحصل على آخر
                                        @break
                                    @case('combo')
                                        عرض مجموعة
                                        @break
                                    @default
                                        {{ $offer->offer_type }}
                                @endswitch
                            </span>
                        </div>
                    </div>

                    <!-- محتوى البطاقة -->
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">{{ $offer->title }}</h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">{{ Str::limit($offer->description, 100) }}</p>
                        
                        <!-- معلومات الخصم -->
                        @if($offer->discount_percentage || $offer->discount_amount)
                            <div class="flex items-center mb-4">
                                <i class="fas fa-percentage text-primary ml-2"></i>
                                <span class="text-lg font-semibold text-primary">
                                    @if($offer->discount_percentage)
                                        {{ $offer->discount_percentage }}%
                                    @elseif($offer->discount_amount)
                                        {{ $offer->discount_amount }} د.ل
                                    @endif
                                </span>
                            </div>
                        @endif

                        <!-- تواريخ العرض -->
                        <div class="space-y-2 mb-4">
                            <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                <i class="fas fa-calendar-alt ml-2"></i>
                                <span>يبدأ: {{ $offer->start_date->format('Y/m/d H:i') }}</span>
                            </div>
                            @if($offer->end_date)
                                <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                    <i class="fas fa-calendar-times ml-2"></i>
                                    <span>ينتهي: {{ $offer->end_date->format('Y/m/d H:i') }}</span>
                                </div>
                            @endif
                        </div>

                        <!-- إحصائيات الاستخدام -->
                        @if($offer->max_uses)
                            <div class="mb-4">
                                <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                    <span>الحد الأقصى للاستخدام</span>
                                    <span>{{ $offer->max_uses }}</span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="bg-primary h-2 rounded-full" style="width: 30%"></div>
                                </div>
                            </div>
                        @endif

                        <!-- أزرار الإجراءات -->
                        <div class="flex flex-wrap gap-2">
                            <a href="{{ route('employee.offers.show', $offer) }}" 
                               class="flex-1 text-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors">
                                <i class="fas fa-eye ml-1"></i>
                                عرض
                            </a>
                            <a href="{{ route('employee.offers.edit', $offer) }}" 
                               class="flex-1 text-center px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded-lg transition-colors">
                                <i class="fas fa-edit ml-1"></i>
                                تعديل
                            </a>
                            <form action="{{ route('employee.offers.toggleStatus', $offer) }}" method="POST" class="inline">
                                @csrf
                                @method('PATCH')
                                <button type="submit" 
                                        class="px-4 py-2 {{ $offer->is_active ? 'bg-orange-500 hover:bg-orange-600' : 'bg-green-500 hover:bg-green-600' }} text-white rounded-lg transition-colors">
                                    <i class="fas fa-{{ $offer->is_active ? 'pause' : 'play' }} ml-1"></i>
                                    {{ $offer->is_active ? 'إيقاف' : 'تفعيل' }}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="mt-8">
            {{ $offers->appends(request()->query())->links() }}
        </div>
    @else
        <!-- حالة عدم وجود عروض -->
        <div class="bg-white dark:bg-gray-800 rounded-xl p-12 text-center shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <i class="fas fa-tags text-4xl text-gray-400"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">لا توجد عروض</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
                @if(request()->hasAny(['search', 'type', 'status']))
                    لم يتم العثور على عروض تطابق معايير البحث المحددة
                @else
                    لم يتم إنشاء أي عروض بعد
                @endif
            </p>
            @if(!request()->hasAny(['search', 'type', 'status']))
                <a href="{{ route('employee.offers.create') }}" 
                   class="inline-flex items-center px-6 py-3 bg-primary hover:bg-primary/90 text-white font-semibold rounded-lg transition-colors">
                    <i class="fas fa-plus ml-2"></i>
                    إنشاء أول عرض
                </a>
            @else
                <a href="{{ route('employee.offers.index') }}" 
                   class="inline-flex items-center px-6 py-3 bg-gray-500 hover:bg-gray-600 text-white font-semibold rounded-lg transition-colors">
                    <i class="fas fa-list ml-2"></i>
                    عرض جميع العروض
                </a>
            @endif
        </div>
    @endif
</div>

@push('scripts')
<script>
// تأكيد حذف العرض
function confirmDelete(offerId) {
    if (confirm('هل أنت متأكد من حذف هذا العرض؟ لا يمكن التراجع عن هذا الإجراء.')) {
        document.getElementById('delete-form-' + offerId).submit();
    }
}

// تحديث الصفحة كل 30 ثانية لتحديث الإحصائيات
setInterval(function() {
    if (!document.hidden) {
        location.reload();
    }
}, 30000);
</script>
@endpush
@endsection
