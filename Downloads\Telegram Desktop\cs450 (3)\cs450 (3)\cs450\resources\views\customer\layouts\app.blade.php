<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Eat Hub - تجربة طعام لا تُنسى')</title>

    <!-- تطبيق الوضع المظلم فوراً لمنع الوميض -->
    <script>
        // تطبيق الوضع المظلم فوراً قبل تحميل أي شيء آخر
        (function() {
            const savedTheme = localStorage.getItem('darkMode');
            if (savedTheme === 'true') {
                document.documentElement.classList.add('dark');
            } else if (savedTheme === 'false') {
                document.documentElement.classList.remove('dark');
            } else {
                // إذا لم يكن هناك إعداد محفوظ، استخدم إعداد النظام
                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                    document.documentElement.classList.add('dark');
                }
            }
        })();
    </script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#FF6B35',
                        secondary: '#2C3E50',
                        darkText: '#1A202C'
                    },
                    fontFamily: {
                        'arabic': ['Cairo', 'Tajawal', 'sans-serif']
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- النظام الموحد للوضع المظلم -->
    <script src="{{ asset('js/unified-dark-mode.js') }}"></script>

    <!-- Custom Styles -->
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
        }

        .animate-fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .navbar-fixed {
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .page {
            min-height: calc(100vh - 80px);
        }

        .hidden {
            display: none !important;
        }

        .tab-button.active {
            background-color: #FF6B35;
            color: white;
        }

        .category-btn:hover {
            background-color: #FF6B35;
            color: white;
        }

        .menu-item-card:hover .menu-item-image {
            transform: scale(1.05);
            transition: transform 0.3s ease;
        }
    </style>

    @stack('styles')
</head>
<body class="bg-gray-50 dark:bg-gray-900 font-arabic">
    <!-- Navigation -->
    <header id="navbar" class="w-full bg-white dark:bg-gray-800 shadow-md z-30 transition-all duration-300 fixed top-0">
        <!-- القائمة العلوية -->
        <div class="container mx-auto px-4 py-2">
            <div class="flex justify-between items-center">
                <!-- الشعار -->
                <div class="flex items-center">
                    <div class="text-2xl font-bold text-primary flex items-center">
                        <i class="fas fa-utensils ml-2"></i>
                        <span>Eat Hub</span>
                    </div>
                </div>

                <!-- القائمة الرئيسية -->
                <nav class="flex space-x-4 space-x-reverse items-center">
                    <a href="{{ route('customer.index') }}" class="nav-link flex items-center px-2 py-1 text-gray-700 dark:text-gray-300 hover:text-primary hover:bg-primary/10 rounded-lg transition text-sm font-medium {{ request()->routeIs('customer.index') ? 'text-primary bg-primary/10' : '' }}">
                        <i class="fas fa-home ml-1 text-xs hidden sm:block"></i>
                        <span class="hidden sm:block">الرئيسية</span>
                        <i class="fas fa-home sm:hidden"></i>
                    </a>
                    <a href="{{ route('customer.menu') }}" class="nav-link flex items-center px-2 py-1 text-gray-700 dark:text-gray-300 hover:text-primary hover:bg-primary/10 rounded-lg transition text-sm font-medium {{ request()->routeIs('customer.menu*') ? 'text-primary bg-primary/10' : '' }}">
                        <i class="fas fa-utensils ml-1 text-xs hidden sm:block"></i>
                        <span class="hidden sm:block">القائمة</span>
                        <i class="fas fa-utensils sm:hidden"></i>
                    </a>
                    <a href="{{ route('customer.offers.index') }}" class="nav-link flex items-center px-2 py-1 text-gray-700 dark:text-gray-300 hover:text-primary hover:bg-primary/10 rounded-lg transition text-sm font-medium {{ request()->routeIs('customer.offers*') ? 'text-primary bg-primary/10' : '' }}">
                        <i class="fas fa-tags ml-1 text-xs text-red-500 hidden sm:block"></i>
                        <span class="font-semibold hidden sm:block">العروض</span>
                        <i class="fas fa-tags text-red-500 sm:hidden"></i>
                    </a>
                    <a href="{{ route('customer.reservations') }}" class="nav-link flex items-center px-2 py-1 text-gray-700 dark:text-gray-300 hover:text-primary hover:bg-primary/10 rounded-lg transition text-sm font-medium {{ request()->routeIs('customer.reservations*') ? 'text-primary bg-primary/10' : '' }}">
                        <i class="fas fa-calendar-alt ml-1 text-xs hidden sm:block"></i>
                        <span class="hidden sm:block">الحجوزات</span>
                        <i class="fas fa-calendar-alt sm:hidden"></i>
                    </a>
                    <a href="{{ route('customer.orders') }}" class="nav-link flex items-center px-2 py-1 text-gray-700 dark:text-gray-300 hover:text-primary hover:bg-primary/10 rounded-lg transition text-sm font-medium {{ request()->routeIs('customer.orders*') ? 'text-primary bg-primary/10' : '' }}">
                        <i class="fas fa-shopping-bag ml-1 text-xs hidden sm:block"></i>
                        <span class="hidden sm:block">طلباتي</span>
                        <i class="fas fa-shopping-bag sm:hidden"></i>
                    </a>
                    <a href="{{ route('contact') }}" class="nav-link flex items-center px-2 py-1 text-gray-700 dark:text-gray-300 hover:text-primary hover:bg-primary/10 rounded-lg transition text-sm font-medium {{ request()->routeIs('contact*') ? 'text-primary bg-primary/10' : '' }}">
                        <i class="fas fa-envelope ml-1 text-xs hidden sm:block"></i>
                        <span class="hidden sm:block">اتصل بنا</span>
                        <i class="fas fa-envelope sm:hidden"></i>
                    </a>
                    <a href="{{ route('customer.dashboard') }}" class="nav-link flex items-center px-2 py-1 text-gray-700 dark:text-gray-300 hover:text-primary hover:bg-primary/10 rounded-lg transition text-sm font-medium {{ request()->routeIs('customer.dashboard*') ? 'text-primary bg-primary/10' : '' }}">
                        <i class="fas fa-tachometer-alt ml-1 text-xs hidden sm:block"></i>
                        <span class="hidden sm:block">لوحة التحكم</span>
                        <i class="fas fa-tachometer-alt sm:hidden"></i>
                    </a>
                </nav>

                <!-- أزرار إجراءات المستخدم -->
                <div class="flex items-center space-x-3 space-x-reverse">
                    <!-- زر البحث -->
                    <div class="relative">
                        <button id="searchToggle" class="p-2 rounded-full text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700">
                            <i class="fas fa-search"></i>
                        </button>

                        <!-- نافذة البحث -->
                        <div id="searchModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
                            <div class="flex items-start justify-center pt-20">
                                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl mx-4">
                                    <div class="p-6">
                                        <div class="flex items-center justify-between mb-4">
                                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">البحث</h3>
                                            <button id="closeSearch" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>

                                        <div class="relative">
                                            <input type="text"
                                                   id="searchInput"
                                                   placeholder="ابحث عن الأطباق، الطلبات، الحجوزات..."
                                                   class="w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                                <i class="fas fa-search text-gray-400"></i>
                                            </div>
                                        </div>

                                        <div id="searchResults" class="mt-4 max-h-96 overflow-y-auto">
                                            <!-- نتائج البحث ستظهر هنا -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- زر الوضع المظلم -->
                    <button id="darkModeToggle" class="p-2 rounded-full text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700" onclick="window.simpleDarkModeToggle()">
                        <i id="darkModeIcon" class="fas fa-moon"></i>
                    </button>

                    <!-- زر سلة التسوق - يظهر فقط للمستخدمين المسجلين -->
                    @auth
                    <div id="cartButton" class="relative">
                        <a href="{{ route('customer.cart') }}" class="p-2 rounded-full text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700">
                            <i class="fas fa-shopping-cart"></i>
                            <span id="cartCount" class="absolute -top-1 -right-1 bg-primary text-white rounded-full text-xs w-5 h-5 flex items-center justify-center">0</span>
                        </a>
                    </div>
                    @endauth

                    <!-- زر الإشعارات - يظهر فقط للمستخدمين المسجلين -->
                    @auth
                    <div id="notificationsButton" class="relative">
                        <button id="notificationsToggle" class="p-2 rounded-full text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700">
                            <i class="fas fa-bell"></i>
                            <span id="notificationCount" class="absolute -top-1 -right-1 bg-primary text-white rounded-full text-xs w-5 h-5 flex items-center justify-center hidden">0</span>
                        </button>

                        <!-- قائمة الإشعارات -->
                        <div id="notificationsDropdown" class="absolute left-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg py-2 z-10 hidden">
                            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                                <h3 class="text-sm font-medium text-gray-800 dark:text-white">الإشعارات</h3>
                                <button id="markAllRead" class="text-xs text-primary hover:text-primary/80">تحديد الكل كمقروء</button>
                            </div>

                            <div id="notificationsList" class="max-h-96 overflow-y-auto">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>

                            <div class="px-4 py-3 border-t border-gray-200 dark:border-gray-700">
                                <a href="{{ route('customer.notifications') }}" class="text-sm text-primary hover:text-primary/80">عرض جميع الإشعارات</a>
                            </div>
                        </div>
                    </div>
                    @endauth

                    <!-- حالة تسجيل الدخول -->
                    @guest
                    <div id="guestControls">
                        <a href="{{ route('login') }}" class="btn-hover-effect mr-2 px-4 py-2 bg-white dark:bg-gray-700 text-primary border border-primary dark:border-primary hover:bg-primary/5 dark:hover:bg-gray-600 transition rounded-md">
                            تسجيل دخول
                        </a>
                        <a href="{{ route('register') }}" class="btn-hover-effect px-4 py-2 bg-primary text-white hover:bg-primary/90 transition rounded-md">
                            إنشاء حساب
                        </a>
                    </div>
                    @endguest

                    <!-- معلومات المستخدم بعد تسجيل الدخول -->
                    @auth
                    <div id="userControls" class="relative">
                        <button id="userMenuBtn" class="flex items-center p-1 rounded-full text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700">
                            <div class="w-8 h-8 rounded-full bg-primary flex items-center justify-center text-white font-bold">
                                {{ auth()->check() ? substr(auth()->user()->first_name ?? 'أ', 0, 1) : 'أ' }}
                            </div>
                            <span class="mr-2 hidden md:block">{{ auth()->check() ? (auth()->user()->first_name ?? 'المستخدم') . ' ' . (auth()->user()->last_name ?? '') : 'المستخدم' }}</span>
                            <i class="fas fa-chevron-down text-xs mr-1 hidden md:block"></i>
                        </button>

                        <!-- قائمة المستخدم -->
                        <div id="userMenu" class="absolute left-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg py-2 z-10 hidden">
                            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                <p class="text-sm font-medium text-gray-800 dark:text-white">{{ auth()->check() ? (auth()->user()->first_name ?? 'المستخدم') . ' ' . (auth()->user()->last_name ?? '') : 'المستخدم' }}</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">{{ auth()->check() ? (auth()->user()->email ?? '<EMAIL>') : '<EMAIL>' }}</p>
                            </div>
                            <a href="{{ route('customer.dashboard') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-tachometer-alt ml-2"></i>لوحة التحكم
                            </a>
                            <a href="{{ route('customer.profile') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-user-circle ml-2"></i>الملف الشخصي
                            </a>
                            <a href="{{ route('customer.orders') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-clipboard-list ml-2"></i>طلباتي
                            </a>
                            <a href="{{ route('customer.reservations') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-calendar-check ml-2"></i>حجوزاتي
                            </a>
                            <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                            <form action="{{ route('logout') }}" method="POST" class="block">
                                @csrf
                                <button type="submit" class="block w-full text-right px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-sign-out-alt ml-2"></i>تسجيل الخروج
                                </button>
                            </form>
                        </div>
                    </div>
                    @endauth

                    <!-- زر القائمة للشاشات الصغيرة جداً -->
                    <button id="mobileMenuToggle" class="sm:hidden p-2 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700">
                        <i class="fas fa-bars text-lg"></i>
                    </button>
                </div>
            </div>
        </div>

        @include('customer.partials.mobile-menu')
    </header>

    <!-- Main Content -->
    <main class="pt-16">
        @yield('content')
    </main>

    <!-- Footer -->
    @include('customer.partials.footer')

    <!-- Scripts -->
    <script>
        // استخدام النظام الموحد للوضع المظلم
        window.simpleDarkModeToggle = function() {
            if (typeof toggleUnifiedDarkMode === 'function') {
                toggleUnifiedDarkMode();
            }
        };

        // Search Modal
        const searchToggle = document.getElementById('searchToggle');
        const searchModal = document.getElementById('searchModal');
        const closeSearch = document.getElementById('closeSearch');
        const searchInput = document.getElementById('searchInput');

        if (searchToggle) {
            searchToggle.addEventListener('click', function() {
                searchModal.classList.remove('hidden');
                searchInput.focus();
            });
        }

        if (closeSearch) {
            closeSearch.addEventListener('click', function() {
                searchModal.classList.add('hidden');
            });
        }

        if (searchModal) {
            searchModal.addEventListener('click', function(e) {
                if (e.target === searchModal) {
                    searchModal.classList.add('hidden');
                }
            });
        }

        // Notifications Toggle
        const notificationsToggle = document.getElementById('notificationsToggle');
        const notificationsDropdown = document.getElementById('notificationsDropdown');

        if (notificationsToggle) {
            notificationsToggle.addEventListener('click', function() {
                notificationsDropdown.classList.toggle('hidden');
            });
        }

        // User Menu Toggle
        const userMenuBtn = document.getElementById('userMenuBtn');
        const userMenu = document.getElementById('userMenu');

        if (userMenuBtn) {
            userMenuBtn.addEventListener('click', function() {
                userMenu.classList.toggle('hidden');
            });
        }

        // Mobile Menu Toggle
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const mobileMenu = document.getElementById('mobileMenu');

        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', function() {
                mobileMenu.classList.toggle('hidden');
            });
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e) {
            if (notificationsDropdown && !notificationsToggle.contains(e.target) && !notificationsDropdown.contains(e.target)) {
                notificationsDropdown.classList.add('hidden');
            }
            if (userMenu && !userMenuBtn.contains(e.target) && !userMenu.contains(e.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // النظام الموحد للوضع المظلم سيتولى كل شيء
    </script>

    <script src="{{ asset('js/settings-refresh.js') }}"></script>
    @stack('scripts')
</body>
</html>
