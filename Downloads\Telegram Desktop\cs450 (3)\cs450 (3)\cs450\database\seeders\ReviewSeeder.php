<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Review;
use App\Models\User;
use App\Models\Order;
use App\Models\MenuItem;

class ReviewSeeder extends Seeder
{
    public function run()
    {
        // إنشاء مستخدمين تجريبيين إذا لم يكونوا موجودين
        $user1 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'أحمد',
                'last_name' => 'محمد',
                'phone' => '0501234567',
                'password' => bcrypt('password'),
                'user_type' => 'customer',
                'is_active' => true
            ]
        );

        $user2 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'سارة',
                'last_name' => 'أحمد',
                'phone' => '0507654321',
                'password' => bcrypt('password'),
                'user_type' => 'customer',
                'is_active' => true
            ]
        );

        // إنشاء طلبات تجريبية
        $order1 = Order::firstOrCreate([
            'user_id' => $user1->user_id,
            'total_amount' => 55.00,
            'status' => 'completed'
        ]);

        $order2 = Order::firstOrCreate([
            'user_id' => $user2->user_id,
            'total_amount' => 65.00,
            'status' => 'completed'
        ]);

        // الحصول على عناصر المنيو
        $menuItems = MenuItem::all();

        if ($menuItems->isEmpty()) {
            $this->command->info('لا توجد عناصر منيو لإنشاء التقييمات');
            return;
        }

        // تقييمات متنوعة لعناصر المنيو
        $menuReviews = [
            ['rating' => 5, 'comment' => 'طعم رائع جداً! أنصح الجميع بتجربته'],
            ['rating' => 4, 'comment' => 'جيد جداً، لكن يمكن تحسين التوابل قليلاً'],
            ['rating' => 5, 'comment' => 'أفضل طبق جربته في المطعم، سأطلبه مرة أخرى'],
            ['rating' => 3, 'comment' => 'مقبول، لكن التوقعات كانت أعلى'],
            ['rating' => 4, 'comment' => 'طعم جميل وتقديم أنيق'],
            ['rating' => 5, 'comment' => 'ممتاز! الطعم والجودة فوق التوقعات'],
            ['rating' => 4, 'comment' => 'جيد جداً، الكمية مناسبة والطعم لذيذ'],
            ['rating' => 2, 'comment' => 'لم يعجبني كثيراً، ربما لا يناسب ذوقي'],
            ['rating' => 5, 'comment' => 'رائع! سأطلبه دائماً'],
            ['rating' => 4, 'comment' => 'طبق لذيذ ومشبع، أنصح به'],
        ];

        // إنشاء تقييمات لعناصر المنيو
        foreach ($menuItems as $menuItem) {
            // عدد عشوائي من التقييمات لكل عنصر (1-3)
            $reviewCount = rand(1, 3);

            for ($i = 0; $i < $reviewCount; $i++) {
                $randomReview = $menuReviews[array_rand($menuReviews)];
                $randomUser = rand(0, 1) ? $user1 : $user2;
                $randomOrder = rand(0, 1) ? $order1 : $order2;

                Review::firstOrCreate([
                    'user_id' => $randomUser->user_id,
                    'order_id' => $randomOrder->order_id,
                    'menu_item_id' => $menuItem->item_id,
                    'rating' => $randomReview['rating'],
                    'comment' => $randomReview['comment'],
                    'is_approved' => true,
                ]);
            }
        }

        // إنشاء تقييمات عامة للمطعم (بدون menu_item_id)
        $generalReviews = [
            [
                'user_id' => $user1->user_id,
                'order_id' => $order1->order_id,
                'rating' => 5,
                'comment' => 'تجربة رائعة! الطعام لذيذ والخدمة ممتازة.',
                'is_approved' => true
            ],
            [
                'user_id' => $user2->user_id,
                'order_id' => $order2->order_id,
                'rating' => 4,
                'comment' => 'أحببت الأجواء والديكور. الطعام كان شهياً.',
                'is_approved' => true
            ]
        ];

        foreach ($generalReviews as $review) {
            Review::firstOrCreate(
                [
                    'user_id' => $review['user_id'],
                    'order_id' => $review['order_id'],
                    'menu_item_id' => null
                ],
                $review
            );
        }
    }
}
