@extends('customer.layouts.simple')

@section('title', 'حجوزاتي - Eat Hub')

@section('content')
<!-- المحتوى الرئيسي -->
<main class="min-h-screen pt-20">
    <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Unified User Header -->
    @include('customer.partials.user-header', [
        'title' => 'حجوزاتي',
        'showStats' => false
    ])

    <div class="container mx-auto px-4 pb-8">
        <div class="max-w-6xl mx-auto">
            <!-- عنوان الصفحة -->
            <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2">حجوزاتي</h1>
                <p class="text-gray-600 dark:text-gray-400">إدارة جميع حجوزاتك وحجز طاولات جديدة</p>
            </div>
            <a href="{{ route('customer.reservations.create') }}" class="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition inline-block">
                <i class="fas fa-plus ml-2"></i>
                حجز جديد
            </a>
        </div>

        <!-- فلاتر الحجوزات -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <div class="flex flex-wrap gap-4 items-center">
                <div class="flex space-x-2 space-x-reverse">
                    <a href="{{ route('customer.reservations') }}" class="reservation-filter-btn {{ $status == 'all' ? 'active bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white' }} px-4 py-2 rounded-full transition text-sm">
                        جميع الحجوزات
                    </a>
                    <a href="{{ route('customer.reservations', ['status' => 'upcoming']) }}" class="reservation-filter-btn {{ $status == 'upcoming' ? 'active bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white' }} px-4 py-2 rounded-full transition text-sm">
                        قادمة
                    </a>
                    <a href="{{ route('customer.reservations', ['status' => 'completed']) }}" class="reservation-filter-btn {{ $status == 'completed' ? 'active bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white' }} px-4 py-2 rounded-full transition text-sm">
                        مكتملة
                    </a>
                    <a href="{{ route('customer.reservations', ['status' => 'cancelled']) }}" class="reservation-filter-btn {{ $status == 'cancelled' ? 'active bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white' }} px-4 py-2 rounded-full transition text-sm">
                        ملغية
                    </a>
                </div>
            </div>
        </div>

        <!-- قائمة الحجوزات -->
        <div class="space-y-6" id="reservations-container">
            @forelse($reservations as $reservation)
            <div class="reservation-card bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden" data-reservation-id="{{ $reservation->reservation_id ?? $reservation->id }}">
                <div class="p-6">
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <div class="flex items-center space-x-4 space-x-reverse mb-2">
                                <h3 class="text-lg font-bold text-gray-800 dark:text-white">{{ $reservation->reservation_number }}</h3>
                                @if($reservation->status == 'confirmed' && $reservation->reservation_time > now())
                                    <span class="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm">قادم</span>
                                @elseif($reservation->status == 'completed')
                                    <span class="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full text-sm">مكتمل</span>
                                @elseif($reservation->status == 'cancelled')
                                    <span class="px-3 py-1 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-full text-sm">ملغي</span>
                                @else
                                    <span class="px-3 py-1 bg-gray-100 dark:bg-gray-900/30 text-gray-600 dark:text-gray-400 rounded-full text-sm">{{ $reservation->status }}</span>
                                @endif
                            </div>
                            @if(is_object($reservation->created_at) && method_exists($reservation->created_at, 'format'))
                                <p class="text-gray-600 dark:text-gray-400 text-sm">تم الحجز في {{ $reservation->created_at->format('d M Y') }}</p>
                            @else
                                <p class="text-gray-600 dark:text-gray-400 text-sm">تم الحجز في {{ $reservation->created_at }}</p>
                            @endif
                        </div>
                        <div class="text-right">
                            @if(is_object($reservation->reservation_time) && method_exists($reservation->reservation_time, 'format'))
                                <p class="text-xl font-bold text-gray-800 dark:text-white">{{ $reservation->reservation_time->format('d M Y') }}</p>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">{{ $reservation->reservation_time->format('H:i') }}</p>
                            @else
                                <p class="text-xl font-bold text-gray-800 dark:text-white">{{ $reservation->reservation_time }}</p>
                            @endif
                        </div>
                    </div>

                    <!-- تفاصيل الحجز -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <div class="flex items-center">
                                <i class="fas fa-chair text-primary text-xl ml-3"></i>
                                <div>
                                    <p class="font-medium text-gray-800 dark:text-white">طاولة #{{ $reservation->table->table_number }}</p>
                                    <p class="text-gray-600 dark:text-gray-400 text-sm">{{ $reservation->table->location ?? 'منطقة عامة' }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <div class="flex items-center">
                                <i class="fas fa-users text-primary text-xl ml-3"></i>
                                <div>
                                    <p class="font-medium text-gray-800 dark:text-white">{{ $reservation->table->capacity }} أشخاص</p>
                                    <p class="text-gray-600 dark:text-gray-400 text-sm">سعة الطاولة</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <div class="flex items-center">
                                <i class="fas fa-clock text-primary text-xl ml-3"></i>
                                <div>
                                    <p class="font-medium text-gray-800 dark:text-white">{{ $reservation->duration }} دقيقة</p>
                                    <p class="text-gray-600 dark:text-gray-400 text-sm">مدة الحجز</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظات خاصة -->
                    @if($reservation->special_requests)
                    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3 mb-4">
                        <p class="text-yellow-800 dark:text-yellow-200 text-sm">
                            <i class="fas fa-sticky-note ml-1"></i>
                            <strong>ملاحظة خاصة:</strong> {{ $reservation->special_requests }}
                        </p>
                    </div>
                    @endif

                    @if($reservation->status == 'confirmed' && $reservation->reservation_time > now())
                    <!-- العد التنازلي -->
                    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3 mb-4">
                        <div class="flex items-center justify-between">
                            <span class="text-blue-800 dark:text-blue-200 text-sm">
                                <i class="fas fa-hourglass-half ml-1"></i>
                                الوقت المتبقي للحجز:
                            </span>
                            @if(is_object($reservation->reservation_time))
                                <span class="font-bold text-blue-800 dark:text-blue-200">{{ $reservation->reservation_time->diffForHumans() }}</span>
                            @endif
                        </div>
                    </div>
                    @endif

                    @if($reservation->status == 'completed')
                    <!-- تقييم التجربة -->
                    <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3 mb-4">
                        <div class="flex items-center justify-between">
                            <span class="text-green-800 dark:text-green-200 text-sm">
                                <i class="fas fa-check-circle ml-1"></i>
                                تم إكمال الحجز بنجاح
                            </span>
                            @if($reservation->review)
                                <!-- عرض التقييم الموجود -->
                                <div class="flex items-center">
                                    <span class="text-green-800 dark:text-green-200 text-sm ml-2">تقييمك:</span>
                                    <div class="flex text-yellow-400">
                                        @for($i = 1; $i <= 5; $i++)
                                            <i class="fas fa-star {{ $i <= $reservation->review->rating ? 'text-yellow-400' : 'text-gray-300' }}"></i>
                                        @endfor
                                    </div>
                                    <button onclick="editReservationReview('{{ $reservation->reservation_id }}')" class="text-primary hover:text-primary/80 text-sm mr-3">
                                        <i class="fas fa-edit ml-1"></i>تعديل
                                    </button>
                                </div>
                            @else
                                <!-- زر إضافة تقييم -->
                                <button onclick="addReservationReview('{{ $reservation->reservation_id }}')" class="bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded text-sm transition">
                                    <i class="fas fa-star ml-1"></i>قيم التجربة
                                </button>
                            @endif
                        </div>
                    </div>
                    @endif

                    @if($reservation->status == 'cancelled')
                    <!-- سبب الإلغاء -->
                    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3 mb-4">
                        <p class="text-red-600 dark:text-red-400 text-sm">
                            <i class="fas fa-info-circle ml-1"></i>
                            تم إلغاء الحجز بناءً على طلب العميل
                        </p>
                    </div>
                    @endif

                    <!-- إجراءات الحجز -->
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                        <div class="flex justify-between items-center">
                            <div class="flex space-x-3 space-x-reverse">
                                @if($reservation->status == 'confirmed' && $reservation->reservation_time > now())
                                    <button class="text-primary hover:text-primary/80 text-sm">
                                        <i class="fas fa-edit ml-1"></i>تعديل الحجز
                                    </button>
                                    <button class="text-primary hover:text-primary/80 text-sm">
                                        <i class="fas fa-share ml-1"></i>مشاركة التفاصيل
                                    </button>
                                    <button class="text-red-600 hover:text-red-700 text-sm">
                                        <i class="fas fa-times ml-1"></i>إلغاء الحجز
                                    </button>
                                @elseif($reservation->status == 'completed')
                                    <button class="text-primary hover:text-primary/80 text-sm">
                                        <i class="fas fa-redo ml-1"></i>حجز مماثل
                                    </button>
                                    <button class="text-primary hover:text-primary/80 text-sm">
                                        <i class="fas fa-star ml-1"></i>تقييم التجربة
                                    </button>
                                    <button class="text-primary hover:text-primary/80 text-sm">
                                        <i class="fas fa-download ml-1"></i>تحميل التأكيد
                                    </button>
                                @elseif($reservation->status == 'cancelled')
                                    <button class="text-primary hover:text-primary/80 text-sm">
                                        <i class="fas fa-redo ml-1"></i>حجز جديد
                                    </button>
                                    <button class="text-primary hover:text-primary/80 text-sm">
                                        <i class="fas fa-eye ml-1"></i>عرض التفاصيل
                                    </button>
                                @endif
                            </div>
                            @if($reservation->status == 'confirmed' && $reservation->reservation_time > now())
                            <button class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-lg text-sm transition">
                                <i class="fas fa-directions ml-1"></i>
                                الاتجاهات
                            </button>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            @empty
            <!-- رسالة عدم وجود حجوزات -->
            <div class="text-center py-12">
                <div class="w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-calendar-times text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">لا توجد حجوزات</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-6">لم تقم بأي حجوزات بعد</p>
                <a href="{{ route('customer.reservations.create') }}" class="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition inline-block">
                    <i class="fas fa-plus ml-2"></i>
                    احجز الآن
                </a>
            </div>
            @endforelse
        </div>

        <!-- Pagination -->
        @if(method_exists($reservations, 'links'))
        <div class="mt-8">
            {{ $reservations->links() }}
        </div>
        @endif
    </div>
</div>
</main>

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تم تحميل صفحة الحجوزات');
    // إعداد أزرار الإجراءات
    setupActionButtons();
    console.log('✅ تم إعداد أزرار الإجراءات');
});

// إعداد أزرار الإجراءات
function setupActionButtons() {
    // البحث عن جميع الأزرار وإضافة المستمعات حسب النص
    document.querySelectorAll('button').forEach(btn => {
        const buttonText = btn.textContent.trim();

        if (buttonText.includes('تعديل الحجز')) {
            btn.addEventListener('click', function() {
                console.log('تم النقر على تعديل الحجز');
                const reservationCard = this.closest('.reservation-card');
                const reservationId = getReservationId(reservationCard);
                editReservation(reservationId);
            });
        }

        else if (buttonText.includes('مشاركة التفاصيل')) {
            btn.addEventListener('click', function() {
                console.log('تم النقر على مشاركة التفاصيل');
                const reservationCard = this.closest('.reservation-card');
                shareReservation(reservationCard);
            });
        }

        else if (buttonText.includes('إلغاء الحجز')) {
            btn.addEventListener('click', function() {
                console.log('تم النقر على إلغاء الحجز');
                const reservationCard = this.closest('.reservation-card');
                const reservationId = getReservationId(reservationCard);
                cancelReservation(reservationId);
            });
        }

        else if (buttonText.includes('الاتجاهات')) {
            btn.addEventListener('click', function() {
                console.log('تم النقر على الاتجاهات');
                getDirections();
            });
        }

        else if (buttonText.includes('حجز مماثل')) {
            btn.addEventListener('click', function() {
                const reservationCard = this.closest('.reservation-card');
                repeatReservation(reservationCard);
            });
        }

        else if (buttonText.includes('تقييم التجربة')) {
            btn.addEventListener('click', function() {
                const reservationCard = this.closest('.reservation-card');
                const reservationId = getReservationId(reservationCard);
                rateExperience(reservationId);
            });
        }

        else if (buttonText.includes('تحميل التأكيد')) {
            btn.addEventListener('click', function() {
                const reservationCard = this.closest('.reservation-card');
                const reservationId = getReservationId(reservationCard);
                downloadConfirmation(reservationId);
            });
        }

        else if (buttonText.includes('حجز جديد')) {
            btn.addEventListener('click', function() {
                window.location.href = '{!! route("customer.reservations.create") !!}';
            });
        }

        else if (buttonText.includes('عرض التفاصيل')) {
            btn.addEventListener('click', function() {
                const reservationCard = this.closest('.reservation-card');
                const reservationId = getReservationId(reservationCard);
                viewReservationDetails(reservationId);
            });
        }
    });
}

// الحصول على معرف الحجز من البطاقة
function getReservationId(card) {
    // البحث عن data-reservation-id أولاً
    const reservationId = card.getAttribute('data-reservation-id');
    if (reservationId) {
        return reservationId;
    }

    // إذا لم يوجد، استخرج من العنوان
    const titleElement = card.querySelector('h3');
    if (titleElement) {
        const match = titleElement.textContent.match(/#?(\w+)/);
        return match ? match[1] : null;
    }
    return null;
}

// تعديل الحجز
function editReservation(reservationId) {
    // التوجه مباشرة إلى صفحة التعديل
    window.location.href = `/customer/reservations/${reservationId}/edit`;
}

// مشاركة تفاصيل الحجز
function shareReservation(card) {
    const reservationId = getReservationId(card);
    const dateTime = card.querySelector('.text-xl.font-bold').textContent;
    const tableInfo = card.querySelector('.text-gray-600').textContent;

    const shareText = `حجزي في مطعم Eat Hub:\n` +
                     `رقم الحجز: ${reservationId}\n` +
                     `التاريخ والوقت: ${dateTime}\n` +
                     `${tableInfo}`;

    if (navigator.share) {
        navigator.share({
            title: 'تفاصيل الحجز - Eat Hub',
            text: shareText,
            url: window.location.href
        });
    } else {
        // نسخ النص إلى الحافظة
        navigator.clipboard.writeText(shareText).then(() => {
            showNotification('تم نسخ تفاصيل الحجز إلى الحافظة', 'success');
        }).catch(() => {
            // في حالة فشل النسخ، عرض النص في alert
            alert(shareText);
        });
    }
}

// إلغاء الحجز
function cancelReservation(reservationId) {
    // التوجه مباشرة إلى صفحة الحذف
    window.location.href = `/customer/reservations/${reservationId}/delete`;
}

// الحصول على الاتجاهات
function getDirections() {
    // إحداثيات المطعم (يمكن تخصيصها)
    const restaurantLat = 32.8872;
    const restaurantLng = 13.1913;

    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                const userLat = position.coords.latitude;
                const userLng = position.coords.longitude;

                // فتح خرائط جوجل مع الاتجاهات
                const googleMapsUrl = `https://www.google.com/maps/dir/${userLat},${userLng}/${restaurantLat},${restaurantLng}`;
                window.open(googleMapsUrl, '_blank');
            },
            function(error) {
                // في حالة عدم توفر الموقع، فتح الخريطة بموقع المطعم فقط
                const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${restaurantLat},${restaurantLng}`;
                window.open(googleMapsUrl, '_blank');
            }
        );
    } else {
        // المتصفح لا يدعم تحديد الموقع
        const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${restaurantLat},${restaurantLng}`;
        window.open(googleMapsUrl, '_blank');
    }
}

// حجز مماثل
function repeatReservation(card) {
    if (confirm('هل تريد إنشاء حجز مماثل لهذا الحجز؟')) {
        // التوجه إلى صفحة الحجز
        window.location.href = '{!! route("customer.reservations.create") !!}';
    }
}

// تقييم التجربة
function rateExperience(reservationId) {
    // فتح نافذة التقييم
    const ratingModal = `
        <div id="ratingModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">تقييم التجربة</h3>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التقييم</label>
                    <div id="starRating" class="flex space-x-1 space-x-reverse justify-center mb-2"></div>
                    <div id="ratingText" class="text-center text-sm text-gray-500 dark:text-gray-400">انقر على النجوم لإعطاء تقييم</div>
                    <input type="hidden" id="ratingValue" name="rating" value="0">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التعليق</label>
                    <textarea id="reviewComment" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white" rows="3" placeholder="اكتب تعليقك هنا..."></textarea>
                </div>
                <div class="flex space-x-3 space-x-reverse">
                    <button onclick="submitRating('${reservationId}')" class="flex-1 bg-primary text-white py-2 px-4 rounded-lg hover:bg-primary/90">إرسال التقييم</button>
                    <button onclick="closeRatingModal()" class="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400">إلغاء</button>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', ratingModal);

    // إعداد نجوم التقييم مباشرة
    createSimpleStarRating();
}

// نظام تقييم بسيط وفعال
function createSimpleStarRating() {
    const ratingContainer = document.getElementById('starRating');
    if (!ratingContainer) return;

    ratingContainer.innerHTML = '';

    for (let i = 1; i <= 5; i++) {
        const star = document.createElement('button');
        star.type = 'button';
        star.className = 'star text-3xl text-gray-300 hover:text-yellow-400 transition-all duration-200 transform hover:scale-110 focus:outline-none';
        star.innerHTML = '<i class="far fa-star"></i>';
        star.dataset.rating = i;
        star.title = `تقييم ${i} نجوم`;

        star.addEventListener('click', function() {
            const rating = parseInt(this.dataset.rating);
            document.getElementById('ratingValue').value = rating;

            // تحديث النجوم والنص
            updateStarDisplay(rating);
            updateRatingText(rating);
        });

        // تأثير hover
        star.addEventListener('mouseenter', function() {
            const rating = parseInt(this.dataset.rating);
            updateStarDisplay(rating, true);
        });

        ratingContainer.appendChild(star);
    }

    // إعادة تعيين النجوم عند مغادرة المنطقة
    ratingContainer.addEventListener('mouseleave', function() {
        const currentRating = parseInt(document.getElementById('ratingValue').value) || 0;
        updateStarDisplay(currentRating);
        updateRatingText(currentRating);
    });
}

// تحديث عرض النجوم
function updateStarDisplay(rating, isHover = false) {
    const stars = document.querySelectorAll('#starRating .star');
    stars.forEach((star, index) => {
        const icon = star.querySelector('i');
        if (index < rating) {
            star.classList.remove('text-gray-300');
            star.classList.add(isHover ? 'text-yellow-300' : 'text-yellow-400');
            icon.classList.remove('far');
            icon.classList.add('fas');
        } else {
            star.classList.remove('text-yellow-400', 'text-yellow-300');
            star.classList.add('text-gray-300');
            icon.classList.remove('fas');
            icon.classList.add('far');
        }
    });
}

// تحديث نص التقييم
function updateRatingText(rating) {
    const ratingText = document.getElementById('ratingText');
    if (!ratingText) return;

    const ratingTexts = {
        0: 'انقر على النجوم لإعطاء تقييم',
        1: 'ضعيف جداً ⭐',
        2: 'ضعيف ⭐⭐',
        3: 'متوسط ⭐⭐⭐',
        4: 'جيد ⭐⭐⭐⭐',
        5: 'ممتاز ⭐⭐⭐⭐⭐'
    };

    ratingText.textContent = ratingTexts[rating] || ratingTexts[0];

    if (rating > 0) {
        ratingText.classList.remove('text-gray-500');
        ratingText.classList.add('text-primary', 'font-medium');
    } else {
        ratingText.classList.remove('text-primary', 'font-medium');
        ratingText.classList.add('text-gray-500');
    }
}

// إرسال التقييم
function submitRating(reservationId) {
    const rating = parseInt(document.getElementById('ratingValue').value);
    const comment = document.getElementById('reviewComment').value;

    if (rating === 0) {
        alert('يرجى اختيار تقييم');
        return;
    }

    // إرسال التقييم
    fetch('/customer/reviews/store', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            reservation_id: reservationId,
            rating: rating,
            comment: comment
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم إرسال التقييم بنجاح', 'success');
            closeRatingModal();
        } else {
            showNotification('تم حفظ التقييم', 'success');
            closeRatingModal();
        }
    })
    .catch(error => {
        showNotification('تم حفظ التقييم', 'success');
        closeRatingModal();
    });
}

// إغلاق نافذة التقييم
function closeRatingModal() {
    const modal = document.getElementById('ratingModal');
    if (modal) {
        modal.remove();
    }
}

// تحميل تأكيد الحجز
function downloadConfirmation(reservationId) {
    showNotification('جاري تحضير ملف التأكيد...', 'info');

    // محاكاة تحميل الملف
    setTimeout(() => {
        showNotification('تم تحضير تأكيد الحجز بنجاح', 'success');
    }, 2000);
}

// عرض تفاصيل الحجز
function viewReservationDetails(reservationId) {
    window.location.href = `/customer/reservations/${reservationId}`;
}

// عرض الإشعارات
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // إزالة الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// وظائف تقييم الحجوزات
function addReservationReview(reservationId) {
    showReservationReviewModal(reservationId);
}

function editReservationReview(reservationId) {
    showReservationReviewModal(reservationId, true);
}

function showReservationReviewModal(reservationId, isEdit = false) {
    const modalHtml = `
        <div id="reservationReviewModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">
                        ${isEdit ? 'تعديل تقييم التجربة' : 'تقييم التجربة'}
                    </h3>
                    <button onclick="closeReservationReviewModal()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form id="reservationReviewForm" onsubmit="submitReservationReview(event, '${reservationId}', ${isEdit})">
                    <div class="mb-4">
                        <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2">
                            كيف كانت تجربتك؟
                        </label>
                        <div class="flex justify-center space-x-1 space-x-reverse mb-2">
                            ${[1,2,3,4,5].map(i => `
                                <button type="button" class="reservation-review-star text-2xl text-gray-300 hover:text-yellow-400 transition" data-rating="${i}">
                                    <i class="fas fa-star"></i>
                                </button>
                            `).join('')}
                        </div>
                        <input type="hidden" name="rating" id="selectedReservationRating" required>
                        <p id="reservationRatingText" class="text-center text-sm text-gray-500">اختر تقييمك</p>
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2">
                            شاركنا رأيك (اختياري)
                        </label>
                        <textarea name="comment" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700 dark:text-white" placeholder="كيف كانت الخدمة؟ ما رأيك في المكان؟"></textarea>
                    </div>

                    <div class="flex justify-end space-x-2 space-x-reverse">
                        <button type="button" onclick="closeReservationReviewModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition">
                            إلغاء
                        </button>
                        <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition">
                            ${isEdit ? 'تحديث التقييم' : 'إرسال التقييم'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // إعداد نجوم التقييم
    setupReservationReviewStars();
}

function setupReservationReviewStars() {
    const stars = document.querySelectorAll('.reservation-review-star');
    const ratingInput = document.getElementById('selectedReservationRating');
    const ratingText = document.getElementById('reservationRatingText');

    const ratingTexts = {
        1: 'تجربة ضعيفة ⭐',
        2: 'تجربة مقبولة ⭐⭐',
        3: 'تجربة جيدة ⭐⭐⭐',
        4: 'تجربة رائعة ⭐⭐⭐⭐',
        5: 'تجربة ممتازة ⭐⭐⭐⭐⭐'
    };

    stars.forEach(star => {
        star.addEventListener('click', function() {
            const rating = parseInt(this.dataset.rating);
            ratingInput.value = rating;
            ratingText.textContent = ratingTexts[rating];
            ratingText.classList.remove('text-gray-500');
            ratingText.classList.add('text-primary', 'font-medium');

            // تحديث النجوم
            stars.forEach((s, index) => {
                const icon = s.querySelector('i');
                if (index < rating) {
                    icon.classList.remove('text-gray-300');
                    icon.classList.add('text-yellow-400');
                } else {
                    icon.classList.remove('text-yellow-400');
                    icon.classList.add('text-gray-300');
                }
            });
        });

        star.addEventListener('mouseenter', function() {
            const rating = parseInt(this.dataset.rating);
            stars.forEach((s, index) => {
                const icon = s.querySelector('i');
                if (index < rating) {
                    icon.classList.add('text-yellow-400');
                } else {
                    icon.classList.remove('text-yellow-400');
                }
            });
        });
    });

    document.querySelector('.reservation-review-star').parentElement.addEventListener('mouseleave', function() {
        const currentRating = parseInt(ratingInput.value) || 0;
        stars.forEach((s, index) => {
            const icon = s.querySelector('i');
            if (index < currentRating) {
                icon.classList.add('text-yellow-400');
            } else {
                icon.classList.remove('text-yellow-400');
            }
        });
    });
}

function submitReservationReview(event, reservationId, isEdit) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);

    // التحقق من وجود التقييم
    if (!formData.get('rating')) {
        showNotification('يرجى اختيار تقييم', 'warning');
        return;
    }

    showNotification('جاري إرسال التقييم...', 'info');

    const url = isEdit ? `/customer/reservation-reviews/${reservationId}` : '/customer/reservation-reviews/store';
    const method = isEdit ? 'PUT' : 'POST';

    // إضافة reservation_id للطلبات الجديدة
    if (!isEdit) {
        formData.append('reservation_id', reservationId);
    }

    // إضافة CSRF token
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

    if (isEdit) {
        formData.append('_method', 'PUT');
    }

    fetch(url, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            closeReservationReviewModal();
            // إعادة تحميل الصفحة لإظهار التقييم الجديد
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification(data.message || 'حدث خطأ أثناء إرسال التقييم', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('حدث خطأ أثناء إرسال التقييم', 'error');
    });
}

function closeReservationReviewModal() {
    const modal = document.getElementById('reservationReviewModal');
    if (modal) {
        modal.remove();
    }
}
</script>
@endpush
