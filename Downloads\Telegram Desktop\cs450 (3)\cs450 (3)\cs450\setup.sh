#!/bin/bash

echo "========================================"
echo "   Eat Hub - Restaurant Management System"
echo "   إعداد سريع للمشروع على جهاز جديد"
echo "========================================"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

echo "[1/10] التحقق من متطلبات النظام..."

# Check PHP
if ! command -v php &> /dev/null; then
    print_error "PHP غير مثبت أو غير موجود في PATH"
    echo "يرجى تثبيت PHP 8.1 أو أحدث"
    exit 1
fi

# Check Composer
if ! command -v composer &> /dev/null; then
    print_error "Composer غير مثبت أو غير موجود في PATH"
    echo "يرجى تثبيت Composer"
    exit 1
fi

# Check Node.js/npm
if ! command -v npm &> /dev/null; then
    print_error "Node.js/npm غير مثبت أو غير موجود في PATH"
    echo "يرجى تثبيت Node.js"
    exit 1
fi

print_success "جميع المتطلبات متوفرة"
echo

echo "[2/10] تثبيت تبعيات PHP..."
if composer install --no-dev --optimize-autoloader; then
    print_success "تم تثبيت تبعيات PHP بنجاح"
else
    print_error "خطأ في تثبيت تبعيات PHP"
    exit 1
fi
echo

echo "[3/10] تثبيت تبعيات Node.js..."
if npm install; then
    print_success "تم تثبيت تبعيات Node.js بنجاح"
else
    print_error "خطأ في تثبيت تبعيات Node.js"
    exit 1
fi
echo

echo "[4/10] إعداد ملف البيئة..."
if [ ! -f .env ]; then
    if [ -f .env.example ]; then
        cp .env.example .env
        print_success "تم نسخ ملف .env من .env.example"
    else
        print_warning "ملف .env.example غير موجود"
        echo "يرجى إنشاء ملف .env يدوياً"
    fi
else
    print_success "ملف .env موجود بالفعل"
fi
echo

echo "[5/10] إنشاء مفتاح التطبيق..."
if php artisan key:generate --force; then
    print_success "تم إنشاء مفتاح التطبيق بنجاح"
else
    print_error "خطأ في إنشاء مفتاح التطبيق"
    exit 1
fi
echo

echo "[6/10] مسح الذاكرة المؤقتة..."
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
print_success "تم مسح الذاكرة المؤقتة"
echo

echo "[7/10] إعطاء صلاحيات للمجلدات..."
chmod -R 775 storage bootstrap/cache
print_success "تم إعطاء الصلاحيات المطلوبة"
echo

echo "[8/10] إنشاء رابط التخزين..."
php artisan storage:link
print_success "تم إنشاء رابط التخزين"
echo

echo "[9/10] بناء ملفات الواجهة الأمامية..."
if npm run build; then
    print_success "تم بناء ملفات الواجهة الأمامية"
else
    print_warning "خطأ في بناء ملفات الواجهة الأمامية"
    echo "يمكنك تشغيل 'npm run dev' لاحقاً"
fi
echo

echo "[10/11] فحص الهجرات..."
php fix_migrations.php
echo

echo "[11/11] الإعداد مكتمل!"
echo

echo "========================================"
echo "الخطوات التالية:"
echo "1. تعديل إعدادات قاعدة البيانات في ملف .env"
echo "2. إنشاء قاعدة البيانات في MySQL"
echo "3. تشغيل: php artisan migrate"
echo "4. تشغيل: php artisan db:seed"
echo "5. تشغيل: php artisan serve"
echo "========================================"
echo

print_warning "تأكد من إعداد قاعدة البيانات في ملف .env"
echo "يجب إنشاء قاعدة البيانات يدوياً قبل تشغيل الهجرات"
echo

read -p "هل تريد تشغيل الهجرات الآن؟ (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo
    echo "تشغيل الهجرات (إعادة إنشاء قاعدة البيانات)..."
    if php artisan migrate:fresh; then
        print_success "تم تشغيل الهجرات بنجاح"
        echo
        read -p "هل تريد تشغيل البذور (إدخال البيانات الأولية)؟ (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            if php artisan db:seed; then
                print_success "تم تشغيل البذور بنجاح"
                echo
                echo "بيانات تسجيل الدخول الافتراضية:"
                echo "البريد الإلكتروني: <EMAIL>"
                echo "كلمة المرور: A178a2002"
            else
                print_error "خطأ في تشغيل البذور"
            fi
        fi
    else
        print_error "خطأ في تشغيل الهجرات"
        echo "تأكد من إعدادات قاعدة البيانات في ملف .env"
        echo "جرب: php artisan migrate بدلاً من migrate:fresh"
    fi
fi

echo
read -p "هل تريد تشغيل الخادم الآن؟ (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo
    echo "تشغيل الخادم على http://localhost:8000"
    echo "اضغط Ctrl+C لإيقاف الخادم"
    php artisan serve
fi

echo
echo "شكراً لاستخدام نظام إدارة المطاعم Eat Hub!"
