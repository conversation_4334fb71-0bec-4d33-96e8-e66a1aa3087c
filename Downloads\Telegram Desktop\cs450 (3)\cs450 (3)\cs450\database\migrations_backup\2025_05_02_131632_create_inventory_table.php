<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        Schema::create('inventory', function (Blueprint $table) {
            $table->id('inventory_id');
            $table->foreignId('ingredient_id')->constrained('ingredients')->onDelete('cascade');
            $table->decimal('quantity', 10, 2)->default(0);
            $table->decimal('cost_per_unit', 10, 2);
            $table->date('expiry_date')->nullable();
            $table->timestamps();
        });

        // إضافة constraint للكمية باستخدام raw SQL
        DB::statement('ALTER TABLE inventory ADD CONSTRAINT quantity_non_negative CHECK (quantity >= 0)');
    }

    public function down()
    {
        Schema::dropIfExists('inventory');
    }
};