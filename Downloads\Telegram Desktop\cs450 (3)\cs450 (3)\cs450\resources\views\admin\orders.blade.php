@extends('layouts.admin')

@section('title', 'إدارة الطلبات - لوحة تحكم Eat Hub')

@section('page-title', 'إدارة الطلبات')

@section('content')
<div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">إدارة الطلبات</h2>
    <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 sm:space-x-reverse">
        <div class="relative">
            <input type="text" placeholder="بحث..." class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary text-base">
            <button class="absolute left-3 top-2.5 text-gray-500 dark:text-gray-400">
                <i class="fas fa-search"></i>
            </button>
        </div>
        <a href="{{ route('admin.orders.create') }}" class="bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-plus ml-2"></i>
            <span>إنشاء طلب</span>
        </a>
    </div>
</div>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <!-- إحصائية الطلبات 1 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">طلبات اليوم</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">42</h3>
            </div>
            <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3">
                <i class="fas fa-shopping-cart text-blue-500 text-xl"></i>
            </div>
        </div>
    </div>
    
    <!-- إحصائية الطلبات 2 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">طلبات قيد التحضير</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">8</h3>
            </div>
            <div class="rounded-full bg-yellow-100 dark:bg-yellow-900/30 p-3">
                <i class="fas fa-utensils text-yellow-500 text-xl"></i>
            </div>
        </div>
    </div>
    
    <!-- إحصائية الطلبات 3 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">طلبات قيد التوصيل</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">12</h3>
            </div>
            <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3">
                <i class="fas fa-truck text-green-500 text-xl"></i>
            </div>
        </div>
    </div>
    
    <!-- إحصائية الطلبات 4 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">متوسط وقت التحضير</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">21 دقيقة</h3>
            </div>
            <div class="rounded-full bg-purple-100 dark:bg-purple-900/30 p-3">
                <i class="fas fa-clock text-purple-500 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
    <div class="flex border-b border-gray-200 dark:border-gray-700">
        <button class="px-6 py-3 border-b-2 border-primary text-primary font-medium">جميع الطلبات</button>
        <button class="px-6 py-3 text-gray-600 dark:text-gray-300 font-medium hover:text-primary">قيد التحضير</button>
        <button class="px-6 py-3 text-gray-600 dark:text-gray-300 font-medium hover:text-primary">قيد التوصيل</button>
        <button class="px-6 py-3 text-gray-600 dark:text-gray-300 font-medium hover:text-primary">مكتمل</button>
        <button class="px-6 py-3 text-gray-600 dark:text-gray-300 font-medium hover:text-primary">ملغي</button>
    </div>
    
    <div class="p-6">
        <div class="mb-4 flex flex-col md:flex-row md:items-center md:justify-between">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2 md:mb-0">سجل الطلبات</h3>
            <div class="flex space-x-2 space-x-reverse">
                <div class="relative">
                    <input type="date" class="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white border-0 rounded-md p-2 text-sm focus:outline-none">
                </div>
                <div class="relative">
                    <select class="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white border-0 rounded-md p-2 text-sm focus:outline-none">
                        <option>جميع طرق الدفع</option>
                        <option>نقدي</option>
                        <option>بطاقة ائتمان</option>
                        <option>محفظة رقمية</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead>
                    <tr class="bg-gray-50 dark:bg-gray-700">
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            رقم الطلب
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            العميل
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            المنتجات
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            المبلغ
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            طريقة الدفع
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            الحالة
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            تاريخ الطلب
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                            #ORD-5328
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            محمد أحمد
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            برجر لحم، بطاطس، كولا
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                            120 ر.س
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            بطاقة ائتمان
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 py-1 text-xs rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                مكتمل
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            منذ 10 دقائق
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <a href="{{ route('admin.orders.show', 'ORD-5328') }}" class="text-blue-500 hover:text-blue-700 transition-all">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button class="text-green-500 hover:text-green-700 transition-all">
                                    <i class="fas fa-print"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                            #ORD-5327
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            سارة عبدالله
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            بيتزا سوبريم، سلطة
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                            85 ر.س
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            نقدي
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 py-1 text-xs rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                                قيد التوصيل
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            منذ 25 دقيقة
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <a href="{{ route('admin.orders.show', 'ORD-5327') }}" class="text-blue-500 hover:text-blue-700 transition-all">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button class="text-green-500 hover:text-green-700 transition-all">
                                    <i class="fas fa-print"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                            #ORD-5326
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            خالد عمر
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            ستيك فيليه، سلطة، عصير
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                            220 ر.س
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            محفظة رقمية
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                قيد التحضير
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            منذ 35 دقيقة
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <a href="{{ route('admin.orders.show', 'ORD-5326') }}" class="text-blue-500 hover:text-blue-700 transition-all">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button class="text-green-500 hover:text-green-700 transition-all">
                                    <i class="fas fa-print"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                            #ORD-5325
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            نورة سعد
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            دجاج مشوي، أرز
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                            150 ر.س
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            بطاقة ائتمان
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 py-1 text-xs rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                مكتمل
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            منذ ساعة
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <a href="{{ route('admin.orders.show', 'ORD-5325') }}" class="text-blue-500 hover:text-blue-700 transition-all">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button class="text-green-500 hover:text-green-700 transition-all">
                                    <i class="fas fa-print"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                            #ORD-5324
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            فاطمة علي
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            باستا كاربونارا، عصير
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                            95 ر.س
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            نقدي
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 py-1 text-xs rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                مكتمل
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            منذ ساعتين
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <a href="{{ route('admin.orders.show', 'ORD-5324') }}" class="text-blue-500 hover:text-blue-700 transition-all">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button class="text-green-500 hover:text-green-700 transition-all">
                                    <i class="fas fa-print"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex justify-between items-center">
            <div class="text-sm text-gray-700 dark:text-gray-300">
                عرض <span class="font-medium">1</span> إلى <span class="font-medium">5</span> من <span class="font-medium">42</span> طلب
            </div>
            <div class="flex items-center space-x-2 space-x-reverse">
                <button class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 disabled:opacity-50">
                    السابق
                </button>
                <button class="px-3 py-1 rounded-md bg-primary text-white hover:bg-primary/90">
                    1
                </button>
                <button class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600">
                    2
                </button>
                <button class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600">
                    3
                </button>
                <button class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600">
                    التالي
                </button>
            </div>
        </div>
    </div>
</div>
@endsection