# دليل إعداد نظام إدارة المطعم

## 🚀 الإعداد السريع (للمرة الأولى)

### 1. متطلبات النظام
- PHP 8.1 أو أحدث
- MySQL 8.0 أو أحدث
- Composer
- Node.js و npm (اختياري للتطوير)

### 2. إعداد المشروع

#### الطريقة الأولى: الإعداد التلقائي (مُوصى به) ⭐
```bash
# 1. نسخ ملف البيئة
cp .env.example .env

# 2. تعديل إعدادات قاعدة البيانات في .env
# DB_DATABASE=restaurant_db
# DB_USERNAME=your_username
# DB_PASSWORD=your_password

# 3. تشغيل سكريبت الإعداد التلقائي
php setup.php

# 4. فحص صحة النظام (اختياري)
php check-system.php
```

#### الطريقة الثانية: الإعداد اليدوي
```bash
# 1. تثبيت المكتبات
composer install

# 2. نسخ ملف البيئة
cp .env.example .env

# 3. إنشاء مفتاح التطبيق
php artisan key:generate

# 4. إنشاء رابط التخزين
php artisan storage:link

# 5. تشغيل الهجرات
php artisan migrate

# 6. تشغيل البذور
php artisan db:seed

# 7. مسح الكاش
php artisan cache:clear
php artisan config:clear
```

### 3. تشغيل الخادم
```bash
php artisan serve
```

## 🔄 إعادة تعيين قاعدة البيانات

إذا واجهت مشاكل في البيانات أو تريد البدء من جديد:

```bash
php reset-database.php
```

## 🔍 فحص صحة النظام

للتأكد من أن جميع مكونات النظام تعمل بشكل صحيح:

```bash
php check-system.php
```

هذا الأمر سيفحص:
- ✅ الاتصال بقاعدة البيانات
- ✅ وجود الجداول المطلوبة
- ✅ البيانات الأساسية (مستخدمين، قائمة طعام، طاولات)
- ✅ مجلدات التخزين والصور
- ✅ الصلاحيات والأدوار

## 👥 معلومات تسجيل الدخول الافتراضية

### المدير
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** A178a2002
- **النوع:** مدير

## 🗂️ هيكل المشروع

```
├── app/
│   ├── Http/Controllers/     # المتحكمات
│   ├── Models/              # النماذج
│   └── ...
├── database/
│   ├── migrations/          # هجرات قاعدة البيانات
│   └── seeders/            # بذور البيانات
├── resources/
│   └── views/              # واجهات المستخدم
├── storage/
│   └── app/public/         # ملفات التخزين
├── setup.php               # سكريبت الإعداد التلقائي
└── reset-database.php      # سكريبت إعادة التعيين
```

## 🛠️ حل المشاكل الشائعة

### مشكلة: "Class 'Spatie\Permission\Models\Role' not found"
```bash
composer require spatie/laravel-permission
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
```

### مشكلة: "SQLSTATE[42000]: Syntax error"
- تأكد من إعدادات قاعدة البيانات في `.env`
- تأكد من تشغيل MySQL
- جرب إعادة تعيين قاعدة البيانات: `php reset-database.php`

### مشكلة: الصور لا تظهر
```bash
php artisan storage:link
```

### مشكلة: خطأ في الصلاحيات
```bash
# على Linux/Mac
sudo chmod -R 775 storage bootstrap/cache

# على Windows (تشغيل كمدير)
icacls storage /grant Everyone:F /T
icacls bootstrap/cache /grant Everyone:F /T
```

## 📋 قائمة التحقق للإعداد

- [ ] تم تثبيت PHP 8.1+
- [ ] تم تثبيت MySQL 8.0+
- [ ] تم تثبيت Composer
- [ ] تم نسخ `.env.example` إلى `.env`
- [ ] تم تعديل إعدادات قاعدة البيانات
- [ ] تم تشغيل `php setup.php` أو الإعداد اليدوي
- [ ] تم تشغيل الخادم بـ `php artisan serve`
- [ ] تم اختبار تسجيل الدخول

## 🆘 الحصول على المساعدة

إذا واجهت أي مشاكل:

1. تأكد من متطلبات النظام
2. جرب إعادة تعيين قاعدة البيانات
3. تحقق من ملف `.env`
4. راجع ملفات السجل في `storage/logs/`

## 🎯 الميزات الرئيسية

- ✅ إدارة المستخدمين والأدوار
- ✅ إدارة قائمة الطعام
- ✅ نظام الطلبات والحجوزات
- ✅ إدارة المخزون والمكونات
- ✅ نظام العروض والخصومات
- ✅ التقارير المالية
- ✅ نظام التقييمات
- ✅ الإشعارات
- ✅ واجهة متجاوبة
