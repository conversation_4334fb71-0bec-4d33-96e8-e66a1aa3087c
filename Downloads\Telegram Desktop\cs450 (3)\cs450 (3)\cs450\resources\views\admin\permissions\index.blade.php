@extends('layouts.admin')

@section('title', 'إدارة الصلاحيات')

@section('content')
<div class="mb-6">
    <div class="flex justify-between items-center">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white">إدارة الصلاحيات</h2>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">إدارة صلاحيات المستخدمين والأدوار</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('admin.permissions.roles') }}" class="bg-secondary hover:bg-secondary/90 text-white font-bold py-2 px-4 rounded-md flex items-center transition-all">
                <i class="fas fa-users-cog ml-2"></i>
                <span>إدارة الأدوار</span>
            </a>
        </div>
    </div>
</div>

<!-- إحصائيات الصلاحيات -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-blue-100">إجمالي الصلاحيات</p>
                <p class="text-2xl font-bold">{{ \Spatie\Permission\Models\Permission::count() }}</p>
            </div>
            <i class="fas fa-key text-3xl text-blue-200"></i>
        </div>
    </div>

    <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-green-100">الأدوار المتاحة</p>
                <p class="text-2xl font-bold">{{ \Spatie\Permission\Models\Role::count() }}</p>
            </div>
            <i class="fas fa-users-cog text-3xl text-green-200"></i>
        </div>
    </div>

    <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-purple-100">المديرين النشطين</p>
                <p class="text-2xl font-bold">{{ \App\Models\User::where('user_type', 'admin')->where('is_active', true)->count() }}</p>
            </div>
            <i class="fas fa-user-shield text-3xl text-purple-200"></i>
        </div>
    </div>

    <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-orange-100">الموظفين النشطين</p>
                <p class="text-2xl font-bold">{{ \App\Models\User::where('user_type', 'employee')->where('is_active', true)->count() }}</p>
            </div>
            <i class="fas fa-users text-3xl text-orange-200"></i>
        </div>
    </div>
</div>

<!-- أدوات إدارة سريعة -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">أدوات إدارة سريعة</h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- منح صلاحيات إدارية كاملة -->
        <div class="border border-red-200 dark:border-red-800 rounded-lg p-4">
            <h4 class="font-semibold text-red-700 dark:text-red-300 mb-2">
                <i class="fas fa-crown ml-2"></i>
                منح صلاحيات إدارية كاملة
            </h4>
            <form action="{{ route('admin.permissions.grant-full-admin') }}" method="POST">
                @csrf
                <select name="user_id" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-800 dark:text-white mb-3">
                    <option value="">اختر المستخدم</option>
                    @foreach($users->where('user_type', '!=', 'admin') as $user)
                        <option value="{{ $user->user_id }}">{{ $user->first_name }} {{ $user->last_name }} ({{ $user->user_type }})</option>
                    @endforeach
                </select>
                <button type="submit" class="w-full bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md transition-all">
                    منح صلاحيات كاملة
                </button>
            </form>
        </div>

        <!-- إزالة جميع الصلاحيات -->
        <div class="border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <h4 class="font-semibold text-yellow-700 dark:text-yellow-300 mb-2">
                <i class="fas fa-user-minus ml-2"></i>
                إزالة جميع الصلاحيات
            </h4>
            <form action="{{ route('admin.permissions.revoke-all') }}" method="POST">
                @csrf
                <select name="user_id" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-800 dark:text-white mb-3">
                    <option value="">اختر المستخدم</option>
                    @foreach($users->where('user_type', '!=', 'admin') as $user)
                        <option value="{{ $user->user_id }}">{{ $user->first_name }} {{ $user->last_name }} ({{ $user->user_type }})</option>
                    @endforeach
                </select>
                <button type="submit" class="w-full bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-md transition-all">
                    إزالة جميع الصلاحيات
                </button>
            </form>
        </div>

        <!-- نسخ الصلاحيات -->
        <div class="border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h4 class="font-semibold text-blue-700 dark:text-blue-300 mb-2">
                <i class="fas fa-copy ml-2"></i>
                نسخ الصلاحيات
            </h4>
            <form action="{{ route('admin.permissions.copy') }}" method="POST">
                @csrf
                <select name="from_user" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-800 dark:text-white mb-2">
                    <option value="">من المستخدم</option>
                    @foreach($users as $user)
                        <option value="{{ $user->user_id }}">{{ $user->first_name }} {{ $user->last_name }}</option>
                    @endforeach
                </select>
                <select name="to_user" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-800 dark:text-white mb-3">
                    <option value="">إلى المستخدم</option>
                    @foreach($users as $user)
                        <option value="{{ $user->user_id }}">{{ $user->first_name }} {{ $user->last_name }}</option>
                    @endforeach
                </select>
                <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-all">
                    نسخ الصلاحيات
                </button>
            </form>
        </div>
    </div>
</div>

<!-- الصلاحيات العربية المباشرة للإدارة -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
        <i class="fas fa-crown text-yellow-500 ml-2"></i>
        الصلاحيات المباشرة للإدارة (باللغة العربية)
    </h3>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- إدارة النظام -->
        <div class="border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h4 class="font-semibold text-blue-700 dark:text-blue-300 mb-3 flex items-center">
                <i class="fas fa-cogs text-blue-500 ml-2"></i>
                إدارة النظام
            </h4>
            <div class="space-y-1 text-sm">
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-blue-500"></i>
                    عرض_لوحة_التحكم
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-blue-500"></i>
                    إدارة_النظام
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-blue-500"></i>
                    وضع_الصيانة
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-blue-500"></i>
                    مسح_الذاكرة_المؤقتة
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-blue-500"></i>
                    النسخ_الاحتياطي
                </div>
            </div>
        </div>

        <!-- إدارة المستخدمين -->
        <div class="border border-green-200 dark:border-green-800 rounded-lg p-4">
            <h4 class="font-semibold text-green-700 dark:text-green-300 mb-3 flex items-center">
                <i class="fas fa-users text-green-500 ml-2"></i>
                إدارة المستخدمين
            </h4>
            <div class="space-y-1 text-sm">
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-green-500"></i>
                    عرض_المستخدمين
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-green-500"></i>
                    إضافة_مستخدمين
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-green-500"></i>
                    تعديل_المستخدمين
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-green-500"></i>
                    إدارة_الصلاحيات
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-green-500"></i>
                    تفعيل_المستخدمين
                </div>
            </div>
        </div>

        <!-- إدارة المالية -->
        <div class="border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <h4 class="font-semibold text-yellow-700 dark:text-yellow-300 mb-3 flex items-center">
                <i class="fas fa-dollar-sign text-yellow-500 ml-2"></i>
                إدارة المالية
            </h4>
            <div class="space-y-1 text-sm">
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-yellow-500"></i>
                    عرض_التقارير_المالية
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-yellow-500"></i>
                    تقارير_الضرائب
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-yellow-500"></i>
                    تقارير_الربح_والخسارة
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-yellow-500"></i>
                    تخطيط_الميزانية
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-yellow-500"></i>
                    إدارة_المدفوعات
                </div>
            </div>
        </div>

        <!-- صلاحيات الطوارئ -->
        <div class="border border-red-300 dark:border-red-700 rounded-lg p-4 bg-red-50 dark:bg-red-900/20">
            <h4 class="font-semibold text-red-700 dark:text-red-300 mb-3 flex items-center">
                <i class="fas fa-exclamation-triangle text-red-600 ml-2"></i>
                صلاحيات الطوارئ
            </h4>
            <div class="space-y-1 text-sm">
                <div class="flex items-center text-red-600 dark:text-red-400">
                    <i class="fas fa-circle text-xs ml-2 text-red-600"></i>
                    الوصول_في_الطوارئ
                </div>
                <div class="flex items-center text-red-600 dark:text-red-400">
                    <i class="fas fa-circle text-xs ml-2 text-red-600"></i>
                    تجاوز_القيود
                </div>
                <div class="flex items-center text-red-600 dark:text-red-400">
                    <i class="fas fa-circle text-xs ml-2 text-red-600"></i>
                    إيقاف_النظام
                </div>
                <div class="flex items-center text-red-600 dark:text-red-400">
                    <i class="fas fa-circle text-xs ml-2 text-red-600"></i>
                    عرض_سجل_التدقيق
                </div>
                <div class="flex items-center text-red-600 dark:text-red-400">
                    <i class="fas fa-circle text-xs ml-2 text-red-600"></i>
                    حظر_عناوين_IP
                </div>
            </div>
        </div>
    </div>

    <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- إدارة المطعم -->
        <div class="border border-purple-200 dark:border-purple-800 rounded-lg p-4">
            <h4 class="font-semibold text-purple-700 dark:text-purple-300 mb-3 flex items-center">
                <i class="fas fa-utensils text-purple-500 ml-2"></i>
                إدارة المطعم
            </h4>
            <div class="space-y-1 text-sm">
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-purple-500"></i>
                    إدارة_الطاولات
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-purple-500"></i>
                    إدارة_الحجوزات
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-purple-500"></i>
                    عرض_الطلبات
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-purple-500"></i>
                    إدارة_القائمة
                </div>
            </div>
        </div>

        <!-- إدارة الموظفين -->
        <div class="border border-indigo-200 dark:border-indigo-800 rounded-lg p-4">
            <h4 class="font-semibold text-indigo-700 dark:text-indigo-300 mb-3 flex items-center">
                <i class="fas fa-user-tie text-indigo-500 ml-2"></i>
                إدارة الموظفين
            </h4>
            <div class="space-y-1 text-sm">
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-indigo-500"></i>
                    عرض_الموظفين
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-indigo-500"></i>
                    إدارة_جداول_العمل
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-indigo-500"></i>
                    تقييم_الأداء
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-indigo-500"></i>
                    إدارة_الرواتب
                </div>
            </div>
        </div>

        <!-- التحليلات والتقارير -->
        <div class="border border-teal-200 dark:border-teal-800 rounded-lg p-4">
            <h4 class="font-semibold text-teal-700 dark:text-teal-300 mb-3 flex items-center">
                <i class="fas fa-chart-line text-teal-500 ml-2"></i>
                التحليلات والتقارير
            </h4>
            <div class="space-y-1 text-sm">
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-teal-500"></i>
                    التحليلات_المتقدمة
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-teal-500"></i>
                    التحليلات_الفورية
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-teal-500"></i>
                    إنشاء_تقارير_مخصصة
                </div>
                <div class="flex items-center text-gray-600 dark:text-gray-400">
                    <i class="fas fa-circle text-xs ml-2 text-teal-500"></i>
                    تصدير_التحليلات
                </div>
            </div>
        </div>
    </div>

    <div class="mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
        <p class="text-sm text-green-800 dark:text-green-200">
            <i class="fas fa-check-circle ml-2"></i>
            <strong>تم التحديث:</strong> جميع المديرين يحصلون الآن على صلاحيات مباشرة باللغة العربية لسهولة الفهم والاستخدام.
        </p>
    </div>
</div>

<!-- قائمة المستخدمين -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">المستخدمون وصلاحياتهم</h3>
    </div>

    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المستخدم</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">النوع</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الأدوار</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">عدد الصلاحيات</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                @foreach($users as $user)
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <div class="h-10 w-10 rounded-full bg-primary text-white flex items-center justify-center">
                                    {{ substr($user->first_name, 0, 1) }}{{ substr($user->last_name, 0, 1) }}
                                </div>
                            </div>
                            <div class="mr-4">
                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                    {{ $user->first_name }} {{ $user->last_name }}
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ $user->email }}
                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                            @if($user->user_type === 'admin') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                            @elseif($user->user_type === 'employee') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                            @else bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 @endif">
                            @if($user->user_type === 'admin') مدير
                            @elseif($user->user_type === 'employee') موظف
                            @else عميل @endif
                        </span>
                    </td>
                    <td class="px-6 py-4">
                        <div class="flex flex-wrap gap-1">
                            @forelse($user->roles as $role)
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                    {{ $role->name }}
                                </span>
                            @empty
                                <span class="text-sm text-gray-500 dark:text-gray-400">لا توجد أدوار</span>
                            @endforelse
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {{ $user->getAllPermissions()->count() }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                            @if($user->is_active) bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                            @else bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 @endif">
                            @if($user->is_active) نشط @else غير نشط @endif
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <a href="{{ route('admin.permissions.edit-user', $user->user_id) }}"
                           class="text-primary hover:text-primary/80 ml-3">
                            <i class="fas fa-edit"></i>
                            تعديل الصلاحيات
                        </a>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        {{ $users->links() }}
    </div>
</div>
@endsection

@section('scripts')
<script>
    // تحسين تجربة المستخدم
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة تأثيرات للجدول
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.01)';
            });

            row.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });
    });
</script>
@endsection
