@extends('auth.layouts.auth')

@section('title', 'تسجيل الدخول - نسخة تجريبية')

@section('content')
<div id="authContainer" class="w-full max-w-md fade-in">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-primary mb-2">Eat Hub</h1>
            <p class="text-gray-600 dark:text-gray-400">نسخة تجريبية - تسجيل دخول سريع</p>
        </div>

        @if(session('error'))
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                <span class="block sm:inline">{{ session('error') }}</span>
            </div>
        @endif

        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
                <span class="block sm:inline">{{ session('success') }}</span>
            </div>
        @endif

        <!-- تسجيل دخول سريع للاختبار -->
        <div class="mb-6">
            <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-white">تسجيل دخول سريع للاختبار</h3>
            <div class="space-y-3">
                <!-- دخول كعميل -->
                <form action="{{ route('demo.login') }}" method="POST">
                    @csrf
                    <input type="hidden" name="user_type" value="customer">
                    <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-4 rounded-lg transition duration-200">
                        🛒 دخول كعميل
                    </button>
                </form>

                <!-- دخول كموظف -->
                <form action="{{ route('demo.login') }}" method="POST">
                    @csrf
                    <input type="hidden" name="user_type" value="employee">
                    <button type="submit" class="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-4 rounded-lg transition duration-200">
                        👨‍💼 دخول كموظف
                    </button>
                </form>

                <!-- دخول كمدير -->
                <form action="{{ route('demo.login') }}" method="POST">
                    @csrf
                    <input type="hidden" name="user_type" value="admin">
                    <button type="submit" class="w-full bg-red-500 hover:bg-red-600 text-white font-bold py-3 px-4 rounded-lg transition duration-200">
                        👑 دخول كمدير
                    </button>
                </form>
            </div>
        </div>

        <!-- فاصل -->
        <div class="my-6">
            <div class="relative">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
                </div>
                <div class="relative flex justify-center text-sm">
                    <span class="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">أو</span>
                </div>
            </div>
        </div>

        <!-- تسجيل دخول عادي -->
        <form action="{{ route('login') }}" method="POST">
            @csrf
            <div class="mb-6">
                <label for="email" class="block text-gray-700 dark:text-gray-300 mb-2">البريد الإلكتروني</label>
                <div class="relative">
                    <span class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 dark:text-gray-400">
                        <i class="fas fa-envelope"></i>
                    </span>
                    <input type="email" id="email" name="email" value="{{ old('email') }}" class="w-full px-4 py-3 pr-10 border rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-white border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-primary text-base" placeholder="أدخل بريدك الإلكتروني" required>
                </div>
                @error('email')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <div class="flex justify-between items-center mb-2">
                    <label for="password" class="block text-gray-700 dark:text-gray-300">كلمة المرور</label>
                    <a href="{{ route('password.request') }}" class="text-sm text-primary hover:underline">
                        نسيت كلمة المرور؟
                    </a>
                </div>
                <div class="relative">
                    <span class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 dark:text-gray-400">
                        <i class="fas fa-lock"></i>
                    </span>
                    <input type="password" id="password" name="password" class="w-full px-4 py-3 pr-10 border rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-white border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-primary text-base" placeholder="أدخل كلمة المرور" required>
                    <button type="button" class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500 dark:text-gray-400" id="togglePassword">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                @error('password')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <button type="submit" class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-3 px-4 rounded-lg transition duration-200">
                تسجيل الدخول العادي
            </button>
        </form>

        <!-- معلومات الحسابات التجريبية -->
        <div class="mt-6 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
            <h4 class="font-semibold text-blue-800 dark:text-blue-200 mb-2">الحسابات التجريبية:</h4>
            <div class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                <p><strong>عميل:</strong> <EMAIL> / password</p>
                <p><strong>موظف:</strong> <EMAIL> / password</p>
                <p><strong>مدير:</strong> <EMAIL> / password</p>
            </div>
        </div>

        <div class="mt-6 text-center">
            <p class="text-gray-700 dark:text-gray-300">
                ليس لديك حساب؟
                <a href="{{ route('register') }}" class="text-primary hover:underline font-medium">إنشاء حساب جديد</a>
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                <a href="{{ route('login') }}" class="hover:underline">🔙 العودة لصفحة تسجيل الدخول العادية</a>
            </p>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // إظهار وإخفاء كلمة المرور
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordInput = document.getElementById('password');
        const icon = this.querySelector('i');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
</script>
@endsection
