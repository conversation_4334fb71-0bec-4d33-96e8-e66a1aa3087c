@extends('layouts.admin')

@section('title', 'تفاصيل المستخدم - نظام إدارة المطعم')

@section('page-title', 'تفاصيل المستخدم')

@section('content')
<div class="mb-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white">تفاصيل المستخدم: {{ $user->first_name }} {{ $user->last_name }}</h2>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('admin.users.edit', $user->user_id) }}" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg flex items-center">
                <i class="fas fa-edit ml-2"></i>
                <span>تعديل</span>
            </a>
            <a href="{{ route('admin.users') }}" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg flex items-center">
                <i class="fas fa-arrow-right ml-2"></i>
                <span>العودة للقائمة</span>
            </a>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="p-6">
            <div class="flex flex-col md:flex-row">
                <div class="md:w-1/3 mb-6 md:mb-0">
                    <div class="flex justify-center">
                        <div class="w-32 h-32 rounded-full bg-primary/20 flex items-center justify-center text-primary text-4xl font-bold">
                            {{ substr($user->first_name, 0, 1) }}{{ substr($user->last_name, 0, 1) }}
                        </div>
                    </div>
                    <div class="mt-4 text-center">
                        <h3 class="text-xl font-bold text-gray-800 dark:text-white">{{ $user->first_name }} {{ $user->last_name }}</h3>
                        <p class="text-gray-600 dark:text-gray-400">
                            <span class="px-2 py-1 text-xs rounded-full
                                {{ $user->user_type == 'admin' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400' : '' }}
                                {{ $user->user_type == 'employee' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400' : '' }}
                                {{ $user->user_type == 'customer' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' : '' }}">
                                {{ $user->user_type == 'admin' ? 'مدير' : '' }}
                                {{ $user->user_type == 'employee' ? 'موظف' : '' }}
                                {{ $user->user_type == 'customer' ? 'عميل' : '' }}
                            </span>
                        </p>
                        <p class="mt-2">
                            <span class="px-2 py-1 text-xs rounded-full {{ $user->is_active ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' }}">
                                {{ $user->is_active ? 'نشط' : 'غير نشط' }}
                            </span>
                        </p>
                    </div>
                </div>
                <div class="md:w-2/3 md:border-r md:border-gray-200 dark:md:border-gray-700 md:pr-6">
                    <h4 class="text-lg font-bold text-gray-800 dark:text-white mb-4">معلومات المستخدم</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">البريد الإلكتروني</p>
                            <p class="text-gray-800 dark:text-white">{{ $user->email }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">رقم الهاتف</p>
                            <p class="text-gray-800 dark:text-white">{{ $user->phone }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">تاريخ التسجيل</p>
                            <p class="text-gray-800 dark:text-white">{{ $user->created_at->format('Y-m-d') }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">آخر تحديث</p>
                            <p class="text-gray-800 dark:text-white">{{ $user->updated_at->format('Y-m-d') }}</p>
                        </div>
                    </div>

                    @if($user->user_type == 'customer')
                    <div class="mt-6">
                        <h4 class="text-lg font-bold text-gray-800 dark:text-white mb-4">إحصائيات العميل</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                                <p class="text-sm text-blue-500 dark:text-blue-400">عدد الطلبات</p>
                                <p class="text-2xl font-bold text-blue-700 dark:text-blue-300">{{ $user->orders_count ?? 0 }}</p>
                            </div>
                            <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                                <p class="text-sm text-green-500 dark:text-green-400">إجمالي المشتريات</p>
                                <p class="text-2xl font-bold text-green-700 dark:text-green-300">{{ $user->total_spent ?? 0 }} دينار</p>
                            </div>
                            <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                                <p class="text-sm text-purple-500 dark:text-purple-400">عدد الحجوزات</p>
                                <p class="text-2xl font-bold text-purple-700 dark:text-purple-300">{{ $user->reservations_count ?? 0 }}</p>
                            </div>
                        </div>
                    </div>
                    @endif

                    @if($user->user_type == 'employee')
                    <div class="mt-6">
                        <h4 class="text-lg font-bold text-gray-800 dark:text-white mb-4">إحصائيات الموظف</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                                <p class="text-sm text-blue-500 dark:text-blue-400">الطلبات المعالجة</p>
                                <p class="text-2xl font-bold text-blue-700 dark:text-blue-300">{{ $user->processed_orders_count ?? 0 }}</p>
                            </div>
                            <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                                <p class="text-sm text-green-500 dark:text-green-400">متوسط تقييم الخدمة</p>
                                <p class="text-2xl font-bold text-green-700 dark:text-green-300">{{ $user->average_rating ?? 0 }}/5</p>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
