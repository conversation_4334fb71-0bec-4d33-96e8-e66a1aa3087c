<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الحجز - Eat Hub</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
            color: #333;
            line-height: 1.6;
        }
        .print-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #f59e0b;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            color: #f59e0b;
            margin-bottom: 10px;
        }
        .restaurant-info {
            color: #666;
            font-size: 0.9rem;
        }
        .reservation-title {
            font-size: 1.8rem;
            font-weight: bold;
            color: #333;
            text-align: center;
            margin: 30px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .detail-item {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-right: 4px solid #f59e0b;
        }
        .detail-label {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .detail-value {
            color: #666;
            font-size: 1.1rem;
        }
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
        }
        .status-confirmed {
            background: #d1fae5;
            color: #065f46;
        }
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        .status-canceled {
            background: #fee2e2;
            color: #991b1b;
        }
        .preorder-section {
            margin-top: 30px;
            padding: 20px;
            background: #fff7ed;
            border-radius: 8px;
            border: 2px solid #fed7aa;
        }
        .preorder-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #ea580c;
            margin-bottom: 15px;
            text-align: center;
        }
        .preorder-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #fed7aa;
        }
        .preorder-item:last-child {
            border-bottom: none;
        }
        .item-details {
            flex: 1;
        }
        .item-name {
            font-weight: bold;
            color: #333;
        }
        .item-quantity {
            color: #666;
            font-size: 0.9rem;
        }
        .item-price {
            font-weight: bold;
            color: #ea580c;
        }
        .total-section {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 2px solid #ea580c;
            text-align: center;
        }
        .total-amount {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ea580c;
        }
        .special-requests {
            margin-top: 30px;
            padding: 20px;
            background: #f0f9ff;
            border-radius: 8px;
            border: 2px solid #bae6fd;
        }
        .special-requests-title {
            font-weight: bold;
            color: #0369a1;
            margin-bottom: 10px;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            padding-top: 20px;
            border-top: 2px solid #e5e7eb;
            color: #666;
            font-size: 0.9rem;
        }
        .qr-code {
            text-align: center;
            margin: 20px 0;
        }
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            .print-container {
                border: none;
                box-shadow: none;
                margin: 0;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="print-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🍽️ Eat Hub</div>
            <div class="restaurant-info">
                مطعم إيت هاب - تجربة طعام استثنائية<br>
                📍 العنوان: شارع الملك فهد، الرياض<br>
                📞 الهاتف: +966 11 123 4567
            </div>
        </div>

        <!-- Reservation Title -->
        <div class="reservation-title">
            تفاصيل الحجز رقم #{{ $reservation->reservation_id }}
        </div>

        <!-- Reservation Details -->
        <div class="details-grid">
            <div class="detail-item">
                <div class="detail-label">👤 اسم العميل</div>
                <div class="detail-value">
                    {{ $reservation->user ? $reservation->user->first_name . ' ' . $reservation->user->last_name : 'غير متوفر' }}
                </div>
            </div>

            <div class="detail-item">
                <div class="detail-label">📞 رقم الهاتف</div>
                <div class="detail-value">
                    {{ $reservation->user ? $reservation->user->phone : 'غير متوفر' }}
                </div>
            </div>

            <div class="detail-item">
                <div class="detail-label">📅 تاريخ الحجز</div>
                <div class="detail-value">
                    {{ \Carbon\Carbon::parse($reservation->reservation_time)->format('Y-m-d') }}
                </div>
            </div>

            <div class="detail-item">
                <div class="detail-label">🕐 وقت الحجز</div>
                <div class="detail-value">
                    {{ \Carbon\Carbon::parse($reservation->reservation_time)->format('H:i') }}
                </div>
            </div>

            <div class="detail-item">
                <div class="detail-label">👥 عدد الأشخاص</div>
                <div class="detail-value">
                    {{ $reservation->party_size ?? 'غير محدد' }} أشخاص
                </div>
            </div>

            <div class="detail-item">
                <div class="detail-label">🪑 الطاولة</div>
                <div class="detail-value">
                    {{ $reservation->table ? 'طاولة رقم ' . $reservation->table->table_number : 'غير محددة' }}
                </div>
            </div>

            <div class="detail-item">
                <div class="detail-label">⏱️ مدة الحجز</div>
                <div class="detail-value">
                    {{ $reservation->duration ?? 120 }} دقيقة
                </div>
            </div>

            <div class="detail-item">
                <div class="detail-label">📊 حالة الحجز</div>
                <div class="detail-value">
                    <span class="status-badge 
                        @if($reservation->status == 'confirmed') status-confirmed
                        @elseif($reservation->status == 'pending') status-pending
                        @elseif($reservation->status == 'canceled') status-canceled
                        @endif">
                        @if($reservation->status == 'confirmed') مؤكد
                        @elseif($reservation->status == 'pending') في الانتظار
                        @elseif($reservation->status == 'canceled') ملغي
                        @else {{ $reservation->status }} @endif
                    </span>
                </div>
            </div>
        </div>

        <!-- Pre-order Section -->
        @if($reservation->preOrder && $reservation->preOrder->orderItems->count() > 0)
        <div class="preorder-section">
            <div class="preorder-title">🍽️ الطلب المسبق</div>
            
            @foreach($reservation->preOrder->orderItems as $item)
            <div class="preorder-item">
                <div class="item-details">
                    <div class="item-name">{{ $item->menuItem->name }}</div>
                    <div class="item-quantity">الكمية: {{ $item->quantity }}</div>
                </div>
                <div class="item-price">{{ number_format($item->price * $item->quantity, 2) }} د.ل</div>
            </div>
            @endforeach

            <div class="total-section">
                <div class="total-amount">
                    المجموع الكلي: {{ number_format($reservation->preOrder->total_amount, 2) }} د.ل
                </div>
            </div>
        </div>
        @endif

        <!-- Special Requests -->
        @if($reservation->special_requests)
        <div class="special-requests">
            <div class="special-requests-title">📝 ملاحظات خاصة:</div>
            <div>{{ $reservation->special_requests }}</div>
        </div>
        @endif

        <!-- Offer Information -->
        @if($reservation->offer_title)
        <div class="special-requests">
            <div class="special-requests-title">🏷️ عرض خاص:</div>
            <div>{{ $reservation->offer_title }}</div>
            @if($reservation->contact_phone)
            <div style="margin-top: 10px;">
                <strong>رقم التواصل:</strong> {{ $reservation->contact_phone }}
            </div>
            @endif
        </div>
        @endif

        <!-- QR Code Section -->
        <div class="qr-code">
            <div style="margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: center;">
                <div style="font-weight: bold; margin-bottom: 10px; color: #333;">رمز QR للحجز</div>
                <div style="display: inline-block; padding: 10px; background: white; border-radius: 8px;">
                    <!-- يمكن إضافة QR code هنا باستخدام مكتبة JavaScript أو API -->
                    <div style="width: 100px; height: 100px; background: #e5e7eb; display: flex; align-items: center; justify-content: center; margin: 0 auto; border-radius: 4px;">
                        <span style="font-size: 0.8rem; color: #666;">QR Code</span>
                    </div>
                </div>
                <div style="font-size: 0.8rem; color: #666; margin-top: 10px;">
                    امسح الرمز لعرض تفاصيل الحجز
                </div>
            </div>
        </div>

        <!-- Important Notes -->
        <div style="margin: 30px 0; padding: 20px; background: #fef3c7; border-radius: 8px; border: 2px solid #fbbf24;">
            <div style="font-weight: bold; color: #92400e; margin-bottom: 10px; text-align: center;">📋 تعليمات مهمة</div>
            <ul style="margin: 0; padding-right: 20px; color: #92400e;">
                <li>يرجى الحضور قبل 15 دقيقة من موعد الحجز</li>
                <li>في حالة التأخير أكثر من 30 دقيقة، قد يتم إلغاء الحجز</li>
                <li>يرجى إحضار هذا المستند أو إظهاره على الهاتف</li>
                <li>للتعديل أو الإلغاء، يرجى الاتصال قبل 24 ساعة</li>
            </ul>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>🍽️ شكراً لاختياركم مطعم إيت هاب</strong></p>
            <p>تم طباعة هذا المستند في: {{ \Carbon\Carbon::now()->format('Y-m-d H:i:s') }}</p>
            <p>📧 للاستفسارات: <EMAIL> | 📞 +966 11 123 4567</p>
            <p style="margin-top: 15px; font-size: 0.8rem; color: #999;">
                🌐 www.eathub.com | 📱 تطبيق إيت هاب متوفر على App Store و Google Play
            </p>
        </div>
    </div>
</body>
</html>
