@extends('customer.layouts.simple')

@section('title', 'نتائج البحث - Eat Hub')

@section('content')

<!-- المحتوى الرئيسي -->
<main class="min-h-screen pt-20">
    <div class="container mx-auto px-4 py-8">
        <!-- عنوان الصفحة -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2">نتائج البحث</h1>
            @if($query)
                <p class="text-gray-600 dark:text-gray-400">
                    نتائج البحث عن: "<span class="font-semibold text-primary">{{ $query }}</span>"
                </p>
            @endif
        </div>

        @if($query)
            <!-- فلاتر البحث -->
            <div class="mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                <div class="flex flex-wrap gap-2">
                    <a href="{{ route('search', ['q' => $query, 'category' => 'all']) }}"
                       class="px-4 py-2 rounded-full text-sm {{ $category === 'all' ? 'bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600' }}">
                        الكل
                    </a>
                    <a href="{{ route('search', ['q' => $query, 'category' => 'menu']) }}"
                       class="px-4 py-2 rounded-full text-sm {{ $category === 'menu' ? 'bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600' }}">
                        قائمة الطعام
                    </a>
                    @auth
                    <a href="{{ route('search', ['q' => $query, 'category' => 'orders']) }}"
                       class="px-4 py-2 rounded-full text-sm {{ $category === 'orders' ? 'bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600' }}">
                        طلباتي
                    </a>
                    <a href="{{ route('search', ['q' => $query, 'category' => 'reservations']) }}"
                       class="px-4 py-2 rounded-full text-sm {{ $category === 'reservations' ? 'bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600' }}">
                        حجوزاتي
                    </a>
                    @endauth
                </div>
            </div>

            <!-- نتائج قائمة الطعام -->
            @if(isset($results['menuItems']) && $results['menuItems']->count() > 0)
                <div class="mb-8">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                        <i class="fas fa-utensils text-primary ml-2"></i>
                        قائمة الطعام ({{ $results['menuItems']->count() }})
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($results['menuItems'] as $item)
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition">
                                @if($item->image_url)
                                    <img src="{{ $item->image_url }}" alt="{{ $item->name }}" class="w-full h-48 object-cover">
                                @else
                                    <div class="w-full h-48 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                        <i class="fas fa-utensils text-4xl text-gray-400"></i>
                                    </div>
                                @endif

                                <div class="p-4">
                                    <h3 class="font-semibold text-gray-800 dark:text-white mb-2">{{ $item->name }}</h3>
                                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">{{ $item->description }}</p>

                                    <div class="flex justify-between items-center">
                                        <span class="text-lg font-bold text-primary">{{ $item->price }} د.ل</span>
                                        <span class="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-2 py-1 rounded">
                                            {{ $item->category }}
                                        </span>
                                    </div>

                                    @auth
                                    <button class="w-full mt-3 bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-lg transition">
                                        <i class="fas fa-plus ml-2"></i>
                                        إضافة للسلة
                                    </button>
                                    @endauth
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        <!-- Pagination غير متاح للـ Collection -->
                    </div>
                </div>
            @endif

            <!-- نتائج الطلبات -->
            @auth
            @if(isset($results['orders']) && $results['orders']->count() > 0)
                <div class="mb-8">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                        <i class="fas fa-shopping-bag text-primary ml-2"></i>
                        طلباتي ({{ method_exists($results['orders'], 'total') ? $results['orders']->total() : $results['orders']->count() }})
                    </h2>

                    <div class="space-y-4">
                        @foreach($results['orders'] as $order)
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="font-semibold text-gray-800 dark:text-white">طلب #{{ $order->order_id }}</h3>
                                        <p class="text-gray-600 dark:text-gray-400 text-sm">{{ $order->created_at->format('Y-m-d H:i') }}</p>
                                    </div>
                                    <div class="text-left">
                                        <span class="text-lg font-bold text-primary">{{ $order->total_amount }} د.ل</span>
                                        <span class="block text-xs px-2 py-1 rounded-full {{ $order->status === 'completed' ? 'bg-green-100 text-green-600' : ($order->status === 'pending' ? 'bg-yellow-100 text-yellow-600' : 'bg-gray-100 text-gray-600') }}">
                                            {{ $order->status === 'pending' ? 'في الانتظار' : ($order->status === 'preparing' ? 'قيد التحضير' : ($order->status === 'completed' ? 'مكتمل' : 'ملغي')) }}
                                        </span>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <a href="{{ route('customer.orders.show', $order->order_id) }}" class="text-primary hover:text-primary/80 text-sm">
                                        عرض التفاصيل <i class="fas fa-chevron-left mr-1"></i>
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    @if(method_exists($results['orders'], 'links'))
                    <div class="mt-6">
                        {{ $results['orders']->appends(request()->query())->links() }}
                    </div>
                    @endif
                </div>
            @endif

            <!-- نتائج الحجوزات -->
            @if(isset($results['reservations']) && $results['reservations']->count() > 0)
                <div class="mb-8">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                        <i class="fas fa-calendar-check text-primary ml-2"></i>
                        حجوزاتي ({{ method_exists($results['reservations'], 'total') ? $results['reservations']->total() : $results['reservations']->count() }})
                    </h2>

                    <div class="space-y-4">
                        @foreach($results['reservations'] as $reservation)
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="font-semibold text-gray-800 dark:text-white">حجز #{{ $reservation->reservation_id }}</h3>
                                        <p class="text-gray-600 dark:text-gray-400 text-sm">
                                            طاولة #{{ $reservation->table->table_number ?? 'غير محدد' }} - {{ $reservation->party_size }} أشخاص
                                        </p>
                                        <p class="text-gray-600 dark:text-gray-400 text-sm">{{ $reservation->reservation_time->format('Y-m-d H:i') }}</p>
                                    </div>
                                    <div class="text-left">
                                        <span class="text-xs px-2 py-1 rounded-full {{ $reservation->status === 'confirmed' ? 'bg-green-100 text-green-600' : ($reservation->status === 'pending' ? 'bg-yellow-100 text-yellow-600' : 'bg-gray-100 text-gray-600') }}">
                                            {{ $reservation->status === 'pending' ? 'في الانتظار' : ($reservation->status === 'confirmed' ? 'مؤكد' : 'ملغي') }}
                                        </span>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <a href="{{ route('customer.reservations.show', $reservation->reservation_id) }}" class="text-primary hover:text-primary/80 text-sm">
                                        عرض التفاصيل <i class="fas fa-chevron-left mr-1"></i>
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    @if(method_exists($results['reservations'], 'links'))
                    <div class="mt-6">
                        {{ $results['reservations']->appends(request()->query())->links() }}
                    </div>
                    @endif
                </div>
            @endif
            @endauth

            <!-- العروض -->
            @if(isset($results['offers']) && count($results['offers']) > 0)
                <div class="mb-12">
                    <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-6 flex items-center">
                        <i class="fas fa-tags text-primary ml-3"></i>
                        العروض المتاحة
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($results['offers'] as $offer)
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition">
                                @if($offer->image_path)
                                    <img src="{{ asset('storage/' . $offer->image_path) }}" alt="{{ $offer->title }}" class="w-full h-48 object-cover">
                                @else
                                    <div class="w-full h-48 bg-gradient-to-r from-primary to-primary/80 flex items-center justify-center">
                                        <i class="fas fa-tags text-white text-4xl"></i>
                                    </div>
                                @endif

                                <div class="p-4">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="font-semibold text-gray-800 dark:text-white">{{ $offer->title }}</h3>
                                        @if($offer->is_active && $offer->start_date <= now() && $offer->end_date >= now())
                                            <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                متاح الآن
                                            </span>
                                        @elseif($offer->start_date > now())
                                            <span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                                قريباً
                                            </span>
                                        @else
                                            <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                                                غير متاح
                                            </span>
                                        @endif
                                    </div>

                                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">{{ Str::limit($offer->description, 100) }}</p>

                                    <div class="mb-3">
                                        @if($offer->offer_type === 'percentage')
                                            <div class="bg-primary/10 text-primary px-3 py-2 rounded-lg text-center">
                                                <span class="text-lg font-bold">خصم {{ $offer->discount_percentage }}%</span>
                                            </div>
                                        @elseif($offer->offer_type === 'fixed_amount')
                                            <div class="bg-primary/10 text-primary px-3 py-2 rounded-lg text-center">
                                                <span class="text-lg font-bold">خصم {{ $offer->discount_amount }} ريال</span>
                                            </div>
                                        @elseif($offer->offer_type === 'buy_get')
                                            <div class="bg-purple-100 text-purple-800 px-3 py-2 rounded-lg text-center">
                                                <span class="text-sm font-bold">اشتري واحد واحصل على آخر مجاناً</span>
                                            </div>
                                        @else
                                            <div class="bg-orange-100 text-orange-800 px-3 py-2 rounded-lg text-center">
                                                <span class="text-sm font-bold">عرض مجموعة خاص</span>
                                            </div>
                                        @endif
                                    </div>

                                    <div class="text-sm text-gray-500 dark:text-gray-400 mb-3">
                                        <div class="flex justify-between">
                                            <span>يبدأ: {{ $offer->start_date->format('Y/m/d') }}</span>
                                            <span>ينتهي: {{ $offer->end_date->format('Y/m/d') }}</span>
                                        </div>
                                        @if($offer->menuItems->count() > 0)
                                            <div class="mt-1">{{ $offer->menuItems->count() }} عنصر مشمول في العرض</div>
                                        @endif
                                    </div>

                                    @if($offer->terms_conditions)
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mb-3 p-2 bg-gray-50 dark:bg-gray-700 rounded">
                                            @php
                                                $tcPreview = is_array($offer->terms_conditions) ? implode(' ', $offer->terms_conditions) : $offer->terms_conditions;
                                            @endphp
                                            {{ Str::limit($tcPreview, 80) }}
                                        </div>
                                    @endif

                                    <div class="flex justify-between items-center">
                                        @if($offer->min_order_amount)
                                            <span class="text-xs text-gray-500">حد أدنى: {{ $offer->min_order_amount }} ريال</span>
                                        @else
                                            <span></span>
                                        @endif

                                        @if($offer->is_active && $offer->start_date <= now() && $offer->end_date >= now())
                                            <button class="bg-primary text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-primary/90 transition">
                                                استخدم العرض
                                            </button>
                                        @else
                                            <span class="text-gray-400 text-sm">غير متاح حالياً</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- رسالة عدم وجود نتائج -->
            @if((isset($results['menuItems']) ? $results['menuItems']->count() : 0) === 0 &&
                (!auth()->check() || ((isset($results['orders']) ? $results['orders']->count() : 0) === 0 &&
                (isset($results['reservations']) ? $results['reservations']->count() : 0) === 0)) &&
                (!isset($results['offers']) || $results['offers']->count() === 0))
                <div class="text-center py-12">
                    <i class="fas fa-search text-6xl text-gray-300 dark:text-gray-600 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">لا توجد نتائج</h3>
                    <p class="text-gray-500 dark:text-gray-500">لم نجد أي نتائج تطابق بحثك. جرب كلمات مختلفة.</p>
                </div>
            @endif
        @else
            <!-- صفحة البحث الفارغة -->
            <div class="text-center py-12">
                <i class="fas fa-search text-6xl text-gray-300 dark:text-gray-600 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">ابحث عن أي شيء</h3>
                <p class="text-gray-500 dark:text-gray-500">استخدم شريط البحث أعلاه للعثور على الأطباق والطلبات والحجوزات.</p>
            </div>
        @endif
    </div>
@endsection
